#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部门获取功能修复
"""

import requests
import json

def test_department_api():
    """测试部门API"""
    base_url = "http://localhost:8009"
    
    # 测试员工215829（应该返回部门131）
    employee_id = "215829"
    endpoint = f"/api/department/auth/employee/{employee_id}"
    
    try:
        response = requests.get(f"{base_url}{endpoint}", timeout=5)
        print(f"请求: {base_url}{endpoint}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if data.get("ok"):
                department = data["data"].get("department")
                name = data["data"].get("name")
                print(f"✅ 成功获取员工信息: 姓名={name}, 部门={department}")
                
                if department == "131":
                    print("✅ 部门信息正确！")
                else:
                    print(f"❌ 部门信息错误，期望131，实际{department}")
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🧪 测试部门获取功能修复")
    print("=" * 50)
    test_department_api() 