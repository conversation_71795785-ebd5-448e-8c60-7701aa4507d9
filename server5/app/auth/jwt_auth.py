# server5/app/auth/jwt_auth.py
# Server5 JWT认证模块 - 验证Server3生成的token

import time
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordBearer

# 尝试导入不同的JWT库
try:
    from jose import JWTError, jwt
    JWT_LIBRARY = "jose"
except ImportError:
    try:
        import jwt as pyjwt
        JWT_LIBRARY = "pyjwt"
    except ImportError:
        JWT_LIBRARY = None
        logging.warning("No JWT library found. JWT verification will not work.")

logger = logging.getLogger(__name__)

# 与Server3和客户端一致的JWT配置
SECRET_KEY = "your-very-secret-signing-key"  # 与Server3和客户端保持一致
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_SECONDS = 3600  # 1小时过期

security = HTTPBearer()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT token并返回payload"""
    if not JWT_LIBRARY:
        logger.error("No JWT library available")
        return None
    
    try:
        if JWT_LIBRARY == "jose":
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        elif JWT_LIBRARY == "pyjwt":
            payload = pyjwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # 检查token是否过期
        exp = payload.get("exp")
        if exp and datetime.utcnow().timestamp() > exp:
            logger.warning("Token has expired")
            return None
            
        return payload
        
    except Exception as e:
        logger.error(f"JWT verification error: {e}")
        return None

def verify_jwt_token_websocket(token: str) -> Dict[str, Any]:
    """WebSocket专用的JWT token验证"""
    payload = verify_jwt_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    return payload

async def verify_jwt_token_async(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """异步验证JWT token（用于HTTP API）"""
    token = credentials.credentials
    payload = verify_jwt_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return payload

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """获取当前用户信息"""
    return await verify_jwt_token_async(credentials)

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """可选的用户验证（用于不需要强制认证的端点）"""
    if not credentials:
        return None
    
    try:
        return await verify_jwt_token_async(credentials)
    except HTTPException:
        return None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT access token（如果需要的话）"""
    if not JWT_LIBRARY:
        raise RuntimeError("No JWT library available")
    
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    
    to_encode.update({"exp": expire})
    
    if JWT_LIBRARY == "jose":
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    elif JWT_LIBRARY == "pyjwt":
        return pyjwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_employee_token(employee_id: str, employee_name: str, permission: str = "normal") -> str:
    """创建员工专用token"""
    data = {
        "sub": f"employee_{employee_id}",
        "employee_id": employee_id,
        "employee_name": employee_name,
        "user_type": "employee",
        "permission": permission
    }
    return create_access_token(data)

# 兼容性函数
verify_token = verify_jwt_token
verify_employee_token = verify_jwt_token_async 