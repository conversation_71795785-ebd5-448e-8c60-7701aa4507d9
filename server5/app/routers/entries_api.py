# server5/app/routers/entries_api.py
# Entries分区表API路由
# 20250626.s5，6 - 进行客户端的修改，支持program1.py的增删改查操作
# 20250709 - 修改table3的按键：支持"显示上个月"、"显示当月"、"显示下个月"功能
# 20250709 - 修复chart数据格式，适配program1的ChartWidget绘制需求
# 20250709 - 性能优化：添加连接池、缓存、优化查询
# 20250710 - 支持 HTTP-only 模式

print("🔥 当前加载的 entries_api.py 路径:", __file__)
print("🔥 当前加载的 entries_api.py 内容版本: 20250710 - HTTP-only 模式支持")
print("🔥🔥🔥 这是优化后的 entries_api.py！🔥🔥🔥")

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from pydantic import BaseModel, Field
import logging
import sys
import time
import os
from pathlib import Path
import asyncio
import re
from functools import lru_cache

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient
from app.services import OperationHandlerService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/entries", tags=["entries"])

# 检查是否为 HTTP-only 模式
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

# ===== 全局连接池和缓存 =====
_db_pool = None
_table_structure_cache = {}

async def get_db_pool():
    """获取数据库连接池（单例模式）"""
    global _db_pool
    if _db_pool is None:
        if HTTP_ONLY_MODE:
            # HTTP-only 模式下从 service_manager 获取数据库连接
            from app.main import service_manager
            if service_manager and service_manager.imdb_client:
                _db_pool = service_manager.imdb_client
                logger.info("🔗 HTTP-only 模式: 使用 service_manager 的数据库连接")
            else:
                raise HTTPException(status_code=503, detail="HTTP-only 模式下数据库连接不可用")
        else:
            # 完整模式下创建新的连接池
            _db_pool = IMDBClient()
            await _db_pool.connect()
            logger.info("🔗 完整模式: 数据库连接池已创建")
    return _db_pool

@lru_cache(maxsize=10)
def get_cached_time_conversion_rules():
    """获取缓存的时间转换规则"""
    return {
        "所定時間": lambda x: convert_scheduled_time_to_hours(x),
        "早出残業": lambda x: convert_overtime_to_hours(x),
        "内深夜残業": lambda x: convert_overtime_to_hours(x),
        "遅刻早退": lambda x: convert_overtime_to_hours(x),
        "休出時間": lambda x: convert_overtime_to_hours(x),
        "出張残業": lambda x: convert_overtime_to_hours(x),
    }

class EntryBase(BaseModel):
    """Entries基础模型"""
    entry_date: date = Field(..., description="作业日期")
    employee_id: str = Field(..., description="员工ID")
    duration: float = Field(0.0, description="工作时间")
    description: Optional[str] = Field(None, description="作业描述")
    project_code: Optional[str] = Field(None, description="项目代码")
    department: Optional[str] = Field(None, description="部门")
    overduration: Optional[float] = Field(0.0, description="加班时间")
    break_hours: Optional[float] = Field(0.0, description="休息时间")
    status: Optional[str] = Field("pending", description="状态")

class EntryCreate(EntryBase):
    """创建Entry请求模型"""
    pass

class EntryUpdate(BaseModel):
    """更新Entry请求模型 - 支持客户端发送的所有字段"""
    entry_date: Optional[str] = None
    employee_id: Optional[str] = None
    duration: Optional[float] = None
    # 新字段名（客户端实际发送的）
    model: Optional[str] = None
    number: Optional[str] = None
    factory_number: Optional[str] = None
    project_number: Optional[str] = None
    unit_number: Optional[str] = None
    category: Optional[str] = None
    item: Optional[str] = None
    department: Optional[str] = None
    source: Optional[str] = None
    # 兼容旧字段名
    description: Optional[str] = None
    project_code: Optional[str] = None
    overduration: Optional[float] = None
    break_hours: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class EntryResponse(BaseModel):
    """Entry响应模型"""
    id: int = Field(..., description="内部ID")
    external_id: Optional[int] = Field(None, description="外部ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    synced: bool = Field(False, description="是否已同步")

class ChartDataRequest(BaseModel):
    """图表数据请求模型"""
    employee_id: str = Field(..., description="员工ID")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")
    chart_type: str = Field("daily", description="图表类型: daily, weekly, monthly")

class ChartDataResponse(BaseModel):
    """图表数据响应模型 - 适配program1的ChartWidget"""
    daily_data: List[Dict[str, Any]] = Field(..., description="每日数据列表")
    month_name: str = Field(..., description="月份名称")
    entries_count: int = Field(0, description="entries记录数")
    timeprotab_count: int = Field(0, description="timeprotab记录数")

# ===== 优化的依赖注入 =====

async def get_imdb_client() -> IMDBClient:
    """获取IMDB客户端（使用连接池）"""
    return await get_db_pool()

async def get_operation_handler() -> OperationHandlerService:
    """获取操作处理服务"""
    if HTTP_ONLY_MODE:
        raise HTTPException(status_code=503, detail="HTTP-only 模式下不支持操作处理服务")
    
    # 在完整模式下，从 service_manager 获取
    from app.main import service_manager
    if service_manager and service_manager.f4_operation_handler:
        return service_manager.f4_operation_handler
    else:
        raise HTTPException(status_code=503, detail="操作处理服务不可用")

# ===== 辅助函数 =====

def convert_scheduled_time_to_hours(time_str) -> float:
    """将所定時間字符串转换为小时数"""
    if not time_str or time_str == "0":
        return 0.0
    
    try:
        # 处理时间格式 "8:00" -> 8.0
        if isinstance(time_str, str) and ":" in time_str:
            parts = time_str.split(":")
            hours = float(parts[0])
            minutes = float(parts[1]) if len(parts) > 1 else 0.0
            return hours + minutes / 60.0
        else:
            return float(time_str)
    except (ValueError, TypeError):
        return 0.0

def convert_overtime_to_hours(time_str) -> float:
    """将加班时间字符串转换为小时数"""
    if not time_str or time_str == "0":
        return 0.0
    
    try:
        # 处理时间格式 "1:30" -> 1.5
        if isinstance(time_str, str) and ":" in time_str:
            parts = time_str.split(":")
            hours = float(parts[0])
            minutes = float(parts[1]) if len(parts) > 1 else 0.0
            return hours + minutes / 60.0
        else:
            return float(time_str)
    except (ValueError, TypeError):
        return 0.0

# ===== API 路由 =====

@router.get("/{entry_id}", response_model=Dict[str, Any])
async def get_entry(
    entry_id: int,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取单个entry记录"""
    try:
        async with imdb_client.pool.acquire() as conn:
            result = await conn.fetchrow("""
                SELECT id, entry_date, employee_id, duration, model, number,
                       factory_number, project_number, unit_number, category, item,
                       department, source, external_id, ts, created_at, updated_at
                FROM entries 
                WHERE id = $1
            """, entry_id)
            
            if not result:
                raise HTTPException(status_code=404, detail=f"Entry {entry_id} not found")
            
            # 转换为字典格式
            entry_data = dict(result)
            
            # 处理日期格式
            if entry_data.get('entry_date'):
                entry_data['entry_date'] = entry_data['entry_date'].strftime('%Y-%m-%d')
            if entry_data.get('ts'):
                entry_data['ts'] = entry_data['ts'].isoformat()
            if entry_data.get('created_at'):
                entry_data['created_at'] = entry_data['created_at'].isoformat()
            if entry_data.get('updated_at'):
                entry_data['updated_at'] = entry_data['updated_at'].isoformat()
            
            return {"data": entry_data}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取entry失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取entry失败: {str(e)}")

@router.get("/", response_model=List[Dict[str, Any]])
async def get_entries(
    employee_id: Optional[str] = Query(None, description="员工ID过滤"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="返回条数限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取entries记录列表"""
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if employee_id:
            conditions.append("employee_id = ${}".format(len(params) + 1))
            params.append(employee_id)
        
        if start_date:
            conditions.append("entry_date >= ${}".format(len(params) + 1))
            params.append(start_date)
        
        if end_date:
            conditions.append("entry_date <= ${}".format(len(params) + 1))
            params.append(end_date)
        
        # 构建完整查询
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = f"""
            SELECT 
                id,
                external_id,
                entry_date,
                employee_id,
                model,
                number,
                factory_number,
                project_number,
                unit_number,
                category,
                item,
                duration,
                department,
                source,
                ts as created_at,
                ts as updated_at
            FROM entries
            WHERE {where_clause}
            ORDER BY entry_date DESC, id DESC
            LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}
        """
        
        params.extend([limit, offset])
        
        start_time = time.time()
        records = await imdb_client.execute_query(query, *params)
        query_time = time.time() - start_time
        
        logger.info(f"📊 Entries查询完成: {len(records)}条记录, 耗时: {query_time:.3f}秒")
        
        # 转换数据格式
        formatted_records = []
        for record in records:
            formatted_record = {
                "id": record.get("id"),
                "external_id": record.get("external_id"),
                "entry_date": record.get("entry_date").isoformat() if record.get("entry_date") else None,
                "employee_id": record.get("employee_id"),
                "model": record.get("model"),
                "number": record.get("number"),
                "factory_number": record.get("factory_number"),
                "project_number": record.get("project_number"),
                "unit_number": record.get("unit_number"),
                "category": record.get("category"),
                "item": record.get("item"),
                "duration": float(record.get("duration")) if record.get("duration") else 0.0,
                "department": record.get("department"),
                "source": record.get("source"),
                "created_at": record.get("created_at").isoformat() if record.get("created_at") else None,
                "updated_at": record.get("updated_at").isoformat() if record.get("updated_at") else None,
                "synced": True
            }
            formatted_records.append(formatted_record)
        
        return formatted_records
        
    except Exception as e:
        logger.error(f"❌ 获取entries数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取entries数据失败: {str(e)}")

@router.post("/", response_model=EntryResponse)
async def create_entry(
    entry: EntryCreate,
    operation_handler: OperationHandlerService = Depends(get_operation_handler),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """创建新的Entry记录"""
    try:
        # 插入数据到PostgreSQL
        insert_query = """
            INSERT INTO entries (
                entry_date, employee_id, duration, model, number, 
                factory_number, project_number, unit_number, 
                category, item, department, source, ts
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'api', NOW()
            ) RETURNING id, ts as created_at
        """
        
        # 使用传入的数据
        result = await imdb_client.execute_query(
            insert_query,
            entry.entry_date,
            entry.employee_id,
            entry.duration,
            entry.description,  # model
            "1",  # number
            "DEFAULT",  # factory_number
            entry.project_code,  # project_number
            "DEFAULT",  # unit_number
            1,  # category
            1,  # item
            entry.department
        )
        
        if not result:
            raise HTTPException(status_code=500, detail="插入数据失败")
        
        new_record = result[0]
        
        # 返回创建的记录
        return EntryResponse(
            id=new_record["id"],
            external_id=None,
            entry_date=entry.entry_date,
            employee_id=entry.employee_id,
            duration=entry.duration,
            description=entry.description,
            project_code=entry.project_code,
            department=entry.department,
            overduration=entry.overduration,
            break_hours=entry.break_hours,
            status=entry.status,
            created_at=new_record["created_at"],
            updated_at=new_record["created_at"],
            synced=True
        )
        
    except Exception as e:
        logger.error(f"❌ 创建Entry失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建Entry失败: {str(e)}")

@router.put("/{entry_id}", response_model=EntryResponse)
async def update_entry(
    entry_id: int,
    entry_update: EntryUpdate,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """更新Entry记录 - 支持HTTP-only模式"""
    try:
        logger.info(f"📝 收到更新请求: entry_id={entry_id}")
        
        # 首先尝试通过internal_id查找记录
        existing_entry = await imdb_client.fetch_one(
            "SELECT * FROM entries WHERE id = $1", entry_id
        )
        
        # 如果通过internal_id没找到，尝试通过external_id查找
        if not existing_entry:
            logger.info(f"🔍 通过internal_id={entry_id}未找到记录，尝试通过external_id查找")
            existing_entry = await imdb_client.fetch_one(
                "SELECT * FROM entries WHERE external_id = $1", entry_id
            )
        
        if not existing_entry:
            raise HTTPException(status_code=404, detail="记录不存在")
        
        # 获取实际的internal_id和external_id
        actual_internal_id = existing_entry.get('id')
        external_id = existing_entry.get('external_id')
        
        logger.info(f"📋 找到记录: internal_id={actual_internal_id}, external_id={external_id}")
        
        # 构建更新字段
        update_fields = []
        params = []
        param_count = 0
        
        update_data = entry_update.dict(exclude_unset=True)
        
        # 映射字段名称 - 支持客户端发送的所有字段
        field_mapping = {
            "duration": "duration",
            "model": "model",
            "number": "number",
            "factory_number": "factory_number",
            "project_number": "project_number",
            "unit_number": "unit_number",
            "category": "category",  # 区分 - 直接映射
            "item": "item",          # 项目 - 直接映射
            "department": "department",
            # 兼容旧字段名
            "description": "model",
            "project_code": "project_number", 
            "status": "category",  # 状态映射到区分
            "notes": "item"  # 备注映射到项目
        }
        
        # 记录已经处理过的字段
        already_set_fields = set()

        # 处理notes
        if "notes" in update_data and update_data["notes"]:
            notes = update_data["notes"]
            if "号机:" in notes:
                number_match = re.search(r"号机:([^ ]+)", notes)
                if number_match:
                    param_count += 1
                    update_fields.append(f"number = ${param_count}")
                    params.append(number_match.group(1))
                    already_set_fields.add("number")
            if "工场製番:" in notes:
                factory_match = re.search(r"工场製番:([^ ]+)", notes)
                if factory_match:
                    param_count += 1
                    update_fields.append(f"factory_number = ${param_count}")
                    params.append(factory_match.group(1))
                    already_set_fields.add("factory_number")
            if "工事番号:" in notes:
                project_match = re.search(r"工事番号:([^ ]+)", notes)
                if project_match:
                    param_count += 1
                    update_fields.append(f"project_number = ${param_count}")
                    params.append(project_match.group(1))
                    already_set_fields.add("project_number")
            if "ユニット番号:" in notes:
                unit_match = re.search(r"ユニット番号:([^ ]+)", notes)
                if unit_match:
                    param_count += 1
                    update_fields.append(f"unit_number = ${param_count}")
                    params.append(unit_match.group(1))
                    already_set_fields.add("unit_number")

        # 处理普通字段，避免重复，并进行数据类型转换
        for field, value in update_data.items():
            if field in field_mapping and field != "notes":
                db_field = field_mapping[field]
                if db_field in already_set_fields:
                    continue  # 跳过已经处理过的字段
                
                # 数据类型转换
                converted_value = value
                if db_field == "category" and value is not None:
                    try:
                        converted_value = int(value)
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换category值 '{value}' 为整数，使用默认值0")
                        converted_value = 0
                elif db_field == "item" and value is not None:
                    try:
                        converted_value = int(value)
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换item值 '{value}' 为整数，使用默认值0")
                        converted_value = 0
                elif db_field == "duration" and value is not None:
                    try:
                        converted_value = float(value)
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换duration值 '{value}' 为浮点数，使用默认值0.0")
                        converted_value = 0.0
                
                param_count += 1
                update_fields.append(f"{db_field} = ${param_count}")
                params.append(converted_value)
                already_set_fields.add(db_field)
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="没有需要更新的字段")
        
        # 在HTTP-only模式下，确保source为'user'以触发PostgreSQL触发器
        if existing_entry.get('source') != 'user':
            param_count += 1
            update_fields.append(f"source = ${param_count}")
            params.append('user')
        
        # 执行更新（包含source字段的更新）
        update_query = f"""
            UPDATE entries 
            SET {', '.join(update_fields)}, ts = NOW()
            WHERE id = ${param_count + 1}
            RETURNING ts as updated_at
        """
        
        params.append(actual_internal_id)
        
        result = await imdb_client.execute_query(update_query, *params)
        
        if not result:
            raise HTTPException(status_code=500, detail="更新失败")
        
        updated_at = result[0]["updated_at"]
        
        logger.info(f"✅ 更新成功: internal_id={actual_internal_id}, external_id={external_id}")
        
        # 构建返回的记录
        return EntryResponse(
            id=actual_internal_id,
            external_id=external_id,
            entry_date=existing_entry["entry_date"],
            employee_id=existing_entry["employee_id"],
            duration=update_data.get("duration", existing_entry.get("duration", 0.0)),
            description=update_data.get("description", existing_entry.get("model")),
            project_code=update_data.get("project_code", existing_entry.get("project_number")),
            department=update_data.get("department", existing_entry.get("department")),
            overduration=update_data.get("overduration", 0.0),
            break_hours=update_data.get("break_hours", 0.0),
            status=update_data.get("status", "updated"),
            created_at=existing_entry["ts"],
            updated_at=updated_at,
            synced=True
        )
        
    except Exception as e:
        logger.error(f"❌ 更新Entry失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新Entry失败: {str(e)}")

@router.delete("/{entry_id}")
async def delete_entry(
    entry_id: int,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """删除Entry记录 - 支持通过internal_id或external_id删除"""
    try:
        logger.info(f"🗑️ 收到删除请求: entry_id={entry_id}")
        
        # 首先尝试通过internal_id查找记录
        existing_entry = await imdb_client.fetch_one(
            "SELECT * FROM entries WHERE id = $1", entry_id
        )
        
        # 如果通过internal_id没找到，尝试通过external_id查找
        if not existing_entry:
            logger.info(f"🔍 通过internal_id={entry_id}未找到记录，尝试通过external_id查找")
            existing_entry = await imdb_client.fetch_one(
                "SELECT * FROM entries WHERE external_id = $1", entry_id
            )
        
        if not existing_entry:
            raise HTTPException(status_code=404, detail="记录不存在")
        
        # 获取实际的internal_id和external_id
        actual_internal_id = existing_entry.get('id')
        external_id = existing_entry.get('external_id')
        employee_id = existing_entry.get('employee_id')
        
        logger.info(f"📋 找到记录: internal_id={actual_internal_id}, external_id={external_id}")
        
        # 在HTTP-only模式下，直接执行删除操作
        if HTTP_ONLY_MODE:
            # 如果source不是'user'，先更新为'user'，然后删除
            if existing_entry.get('source') != 'user':
                # 先更新source为'user'，这会触发一次触发器（UPDATE队列项）
                await imdb_client.execute_command(
                    "UPDATE entries SET source = 'user' WHERE id = $1", actual_internal_id
                )
                # 等待一小段时间确保UPDATE触发器处理完成
                await asyncio.sleep(0.1)
            
            # 删除PostgreSQL记录，这会触发第二次触发器（DELETE队列项）
            await imdb_client.execute_command(
                "DELETE FROM entries WHERE id = $1", actual_internal_id
            )
            
            logger.info(f"✅ HTTP-only模式删除成功: internal_id={actual_internal_id}, external_id={external_id}")
            return {"message": f"Entry {external_id or actual_internal_id} 删除成功，已触发同步"}
        
        else:
            # 在完整模式下，通过操作处理服务执行删除
            try:
                operation_handler = await get_operation_handler()
                result = await operation_handler.delete_entry(actual_internal_id)
                
                if result:
                    return {"message": f"Entry {external_id or actual_internal_id} 删除成功"}
                else:
                    raise HTTPException(status_code=404, detail="记录不存在或删除失败")
            except HTTPException as e:
                if e.status_code == 503:
                    # 如果操作处理服务不可用，回退到直接删除
                    logger.warning("⚠️ 操作处理服务不可用，回退到直接删除")
                    if existing_entry.get('source') != 'user':
                        await imdb_client.execute_command(
                            "UPDATE entries SET source = 'user' WHERE id = $1", actual_internal_id
                        )
                    
                    await imdb_client.execute_command(
                        "DELETE FROM entries WHERE id = $1", actual_internal_id
                    )
                    
                    logger.info(f"✅ 回退删除成功: internal_id={actual_internal_id}, external_id={external_id}")
                    return {"message": f"Entry {external_id or actual_internal_id} 删除成功，已触发同步"}
                else:
                    raise
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 删除Entry失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除Entry失败: {str(e)}")

@router.get("/chart-data", response_model=ChartDataResponse)
async def get_chart_data(
    employee_id: str = Query(..., description="员工ID"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    chart_type: str = Query("daily", description="图表类型"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取图表数据 - 适配program1的ChartWidget"""
    try:
        # 获取entries数据
        entries_query = """
            SELECT 
                entry_date,
                SUM(duration) as total_duration,
                COUNT(*) as record_count
            FROM entries
            WHERE employee_id = $1 
              AND entry_date >= $2 
              AND entry_date <= $3
            GROUP BY entry_date
            ORDER BY entry_date
        """
        
        entries_records = await imdb_client.execute_query(
            entries_query, employee_id, start_date, end_date
        )
        
        # 获取timeprotab数据进行对比
        timeprotab_query = """
            SELECT 
                日付 as entry_date,
                所定時間,
                早出残業,
                内深夜残業,
                COUNT(*) as record_count
            FROM timeprotab
            WHERE employee_id = $1 
              AND 日付 >= $2 
              AND 日付 <= $3
            GROUP BY 日付, 所定時間, 早出残業, 内深夜残業
            ORDER BY 日付
        """
        
        timeprotab_records = await imdb_client.execute_query(
            timeprotab_query, employee_id, start_date, end_date
        )
        
        # 合并数据并格式化为ChartWidget需要的格式
        daily_data = []
        entries_dict = {r["entry_date"]: r for r in entries_records}
        timeprotab_dict = {r["entry_date"]: r for r in timeprotab_records}
        
        # 获取日期范围内的所有日期
        current_date = start_date
        while current_date <= end_date:
            entry_record = entries_dict.get(current_date)
            timeprotab_record = timeprotab_dict.get(current_date)
            
            # 计算timeprotab的工作时间
            timeprotab_hours = 0.0
            if timeprotab_record:
                scheduled_hours = convert_scheduled_time_to_hours(timeprotab_record.get("所定時間"))
                overtime_hours = (
                    convert_overtime_to_hours(timeprotab_record.get("早出残業")) +
                    convert_overtime_to_hours(timeprotab_record.get("内深夜残業"))
                )
                timeprotab_hours = scheduled_hours + overtime_hours
            
            daily_data.append({
                "date": current_date.isoformat(),
                "entries_hours": float(entry_record["total_duration"]) if entry_record else 0.0,
                "timeprotab_hours": timeprotab_hours,
                "entries_count": entry_record["record_count"] if entry_record else 0,
                "timeprotab_count": timeprotab_record["record_count"] if timeprotab_record else 0,
                "has_entries": entry_record is not None,
                "has_timeprotab": timeprotab_record is not None
            })
            
            current_date += timedelta(days=1)
        
        # 生成月份名称
        month_name = f"{start_date.year}年{start_date.month:02d}月"
        
        return ChartDataResponse(
            daily_data=daily_data,
            month_name=month_name,
            entries_count=len(entries_records),
            timeprotab_count=len(timeprotab_records)
        )
        
    except Exception as e:
        logger.error(f"❌ 获取图表数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图表数据失败: {str(e)}")

@router.get("/months")
async def get_available_months(
    employee_id: str = Query(..., description="员工ID"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取可用的月份列表"""
    try:
        query = """
            SELECT DISTINCT
                EXTRACT(YEAR FROM entry_date) as year,
                EXTRACT(MONTH FROM entry_date) as month,
                COUNT(*) as record_count
            FROM entries
            WHERE employee_id = $1
            GROUP BY EXTRACT(YEAR FROM entry_date), EXTRACT(MONTH FROM entry_date)
            ORDER BY year DESC, month DESC
        """
        
        records = await imdb_client.execute_query(query, employee_id)
        
        # 格式化月份列表
        months = []
        for record in records:
            year = int(record["year"])
            month = int(record["month"])
            months.append({
                "year": year,
                "month": month,
                "display_name": f"{year}年{month:02d}月",
                "record_count": record["record_count"]
            })
        
        return {"ok": True, "data": months}
        
    except Exception as e:
        logger.error(f"❌ 获取月份失败: {e}")
        return {"ok": False, "data": [], "error": f"获取月份失败: {str(e)}"} 