# server5/app/routers/client_entries_gateway.py
# 客户端数据写入网关 - 专门处理客户端到PostgreSQL entries表的写入操作
# 确保空值正确存储，触发数据库触发器，推送到entries_push_queue

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel, Field
from datetime import date, datetime

from app.database import IMDBClient
from app.services.f4_operation_handler import OperationHandlerService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/client/entries", tags=["Client Entries Gateway"])

def _safe_int_conversion(value: Optional[str]) -> int:
    """
    安全的整数转换函数，正确处理字符串"0"的情况

    Args:
        value: 输入的字符串值

    Returns:
        转换后的整数值，默认为0
    """
    if value is None:
        return 0

    # 去除前后空格
    cleaned_value = value.strip()

    # 如果是空字符串，返回0
    if not cleaned_value:
        return 0

    try:
        # 尝试转换为整数
        return int(cleaned_value)
    except (ValueError, TypeError):
        # 转换失败时返回默认值0
        logger.warning(f"无法将值 '{value}' 转换为整数，使用默认值0")
        return 0

# 依赖注入
async def get_imdb_client() -> IMDBClient:
    client = IMDBClient()
    await client.connect()
    return client

async def get_operation_handler() -> OperationHandlerService:
    return OperationHandlerService()

# 请求模型 - 客户端输入数据
class ClientEntryCreate(BaseModel):
    """客户端输入数据模型"""
    entry_date: str = Field(..., description="作业日期 (YYYY/MM/DD)")
    employee_id: str = Field(..., description="员工ID")
    duration: float = Field(..., description="工作时间")
    model: Optional[str] = Field(None, description="机种")
    number: Optional[str] = Field(None, description="号机")
    factory_number: Optional[str] = Field(None, description="工场製番")
    project_number: Optional[str] = Field(None, description="工事番号")
    unit_number: Optional[str] = Field(None, description="ユニット番号")
    category: Optional[str] = Field(None, description="区分")
    item: Optional[str] = Field(None, description="项目")
    department: Optional[str] = Field(None, description="部门")

# 响应模型
class ClientEntryResponse(BaseModel):
    """客户端写入响应模型"""
    success: bool = Field(..., description="操作是否成功")
    entry_id: Optional[int] = Field(None, description="创建的entry ID")
    message: str = Field(..., description="响应消息")

@router.post("/create", response_model=ClientEntryResponse)
async def create_client_entry(
    entry_data: ClientEntryCreate,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    客户端数据写入网关
    将客户端输入的数据写入到PostgreSQL entries表中
    空值存储为空，触发数据库触发器推送到entries_push_queue
    """
    try:
        logger.info(f"📝 客户端数据写入请求: employee_id={entry_data.employee_id}")
        
        # 验证必填字段
        if not entry_data.employee_id.strip():
            raise HTTPException(status_code=400, detail="员工ID不能为空")
        
        if entry_data.duration <= 0:
            raise HTTPException(status_code=400, detail="工作时间必须大于0")
        
        # 转换日期格式
        try:
            if '/' in entry_data.entry_date:
                # 将 YYYY/MM/DD 转换为 date 对象
                entry_date = datetime.strptime(entry_data.entry_date, "%Y/%m/%d").date()
            else:
                # 已经是 YYYY-MM-DD 格式
                entry_date = datetime.strptime(entry_data.entry_date, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式错误，应为 YYYY/MM/DD 或 YYYY-MM-DD")
        
        # 准备插入数据 - 统一使用空字符串而不是NULL，与更新操作保持一致
        insert_data = {
            "entry_date": entry_date,
            "employee_id": entry_data.employee_id.strip(),
            "duration": entry_data.duration,
            "model": entry_data.model.strip() if entry_data.model and entry_data.model.strip() else "",
            "number": entry_data.number.strip() if entry_data.number and entry_data.number.strip() else "",
            "factory_number": entry_data.factory_number.strip() if entry_data.factory_number and entry_data.factory_number.strip() else "",
            "project_number": entry_data.project_number.strip() if entry_data.project_number and entry_data.project_number.strip() else "",
            "unit_number": entry_data.unit_number.strip() if entry_data.unit_number and entry_data.unit_number.strip() else "",
            "category": int(entry_data.category) if entry_data.category and entry_data.category.strip() else 0,  # 转换为整数，默认为0
            "item": int(entry_data.item) if entry_data.item and entry_data.item.strip() else 0,  # 转换为整数，默认为0            
            "department": entry_data.department.strip() if entry_data.department and entry_data.department.strip() else "",
            "source": "user",  # 标记为用户操作，触发同步
            "ts": datetime.now()
        }
        
        logger.info(f"📦 准备插入数据: {insert_data}")
        
        # 连接到PostgreSQL并插入数据
        async with imdb_client.pool.acquire() as conn:
            # 插入数据到entries表
            result = await conn.fetchrow("""
                INSERT INTO entries (
                    entry_date, employee_id, duration, model, number,
                    factory_number, project_number, unit_number,
                    category, item, department, source, ts
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                RETURNING id
            """, 
                insert_data["entry_date"],
                insert_data["employee_id"],
                insert_data["duration"],
                insert_data["model"],
                insert_data["number"],
                insert_data["factory_number"],
                insert_data["project_number"],
                insert_data["unit_number"],
                insert_data["category"],
                insert_data["item"],
                insert_data["department"],
                insert_data["source"],
                insert_data["ts"]
            )
            
            entry_id = result['id']
            logger.info(f"✅ 数据已插入到entries表: entry_id={entry_id}")
            
            # 检查触发器是否创建了队列项
            queue_item = await conn.fetchrow("""
                SELECT queue_id FROM entries_push_queue 
                WHERE entry_id = $1 AND operation = 'INSERT' AND synced = FALSE
                ORDER BY created_ts DESC LIMIT 1
            """, entry_id)
            
            if queue_item:
                logger.info(f"✅ 触发器已创建队列项: queue_id={queue_item['queue_id']}")
            else:
                logger.warning(f"⚠️ 未找到对应的队列项，可能需要检查触发器")
            
            return ClientEntryResponse(
                success=True,
                entry_id=entry_id,
                message=f"数据写入成功，entry_id={entry_id}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 客户端数据写入失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"数据写入失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "client_entries_gateway"} 