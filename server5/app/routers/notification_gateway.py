# server5/app/routers/notification_gateway.py
# 通知网关WebSocket服务 - 将Redis通知转发给对应的客户端
# 2025/07/16 新增：实现f2成功写入MDB后的客户端UI通知

import asyncio
import json
import logging
from typing import Dict, Set
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.security import HTTPBearer

from ..auth.jwt_auth import verify_jwt_token_websocket
from ..database.redis_client import RedisClient

logger = logging.getLogger(__name__)

router = APIRouter()

class NotificationConnectionManager:
    """通知连接管理器"""
    
    def __init__(self):
        # 活跃连接: {employee_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # Redis客户端
        self.redis_client = RedisClient()
        # 订阅任务
        self.subscription_tasks: Dict[str, asyncio.Task] = {}
        # 连接状态
        self.redis_connected = False
        
    async def ensure_redis_connection(self):
        """确保Redis连接"""
        if not self.redis_connected:
            try:
                await self.redis_client.connect()
                self.redis_connected = True
                logger.info("通知网关Redis连接成功")
            except Exception as e:
                logger.error(f"通知网关Redis连接失败: {e}")
                raise
        
    async def connect(self, websocket: WebSocket, employee_id: str):
        """用户连接"""
        await websocket.accept()
        
        # 确保Redis连接
        await self.ensure_redis_connection()
        
        # 如果用户已连接，断开旧连接
        if employee_id in self.active_connections:
            old_ws = self.active_connections[employee_id]
            try:
                await old_ws.close()
                logger.info(f"关闭旧的通知连接: {employee_id}")
            except:
                pass
        
        # 添加新连接
        self.active_connections[employee_id] = websocket
        
        # 启动Redis订阅任务
        await self._start_subscription(employee_id)
        
        logger.info(f"用户通知连接成功: {employee_id} (总连接数: {len(self.active_connections)})")
        
        # 发送连接确认消息
        await self._send_connection_confirm(websocket, employee_id)
        
    async def disconnect(self, employee_id: str):
        """用户断开连接"""
        if employee_id in self.active_connections:
            del self.active_connections[employee_id]
            
            # 停止Redis订阅任务
            await self._stop_subscription(employee_id)
            
            logger.info(f"用户通知连接断开: {employee_id} (剩余连接数: {len(self.active_connections)})")
    
    async def _start_subscription(self, employee_id: str):
        """启动Redis订阅任务"""
        if employee_id in self.subscription_tasks:
            # 如果已有订阅任务，先停止
            await self._stop_subscription(employee_id)
        
        # 创建新的订阅任务
        task = asyncio.create_task(self._subscription_loop(employee_id))
        self.subscription_tasks[employee_id] = task
        
    async def _stop_subscription(self, employee_id: str):
        """停止Redis订阅任务"""
        if employee_id in self.subscription_tasks:
            task = self.subscription_tasks[employee_id]
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            del self.subscription_tasks[employee_id]
    
    async def _subscription_loop(self, employee_id: str):
        """Redis订阅循环"""
        try:
            user_key = f"user:{employee_id}:notifications"
            
            while employee_id in self.active_connections:
                try:
                    # 确保Redis连接
                    if not self.redis_connected:
                        await self.ensure_redis_connection()
                    
                    # 从Redis队列中获取通知（阻塞等待）
                    notification = await self.redis_client.pop_task(user_key, timeout=30)
                    
                    if notification:
                        # 发送通知给客户端
                        await self._send_notification(employee_id, notification)
                    else:
                        # 超时，继续循环
                        continue
                        
                except Exception as e:
                    logger.error(f"通知订阅循环异常 {employee_id}: {e}")
                    # 如果是Redis连接问题，标记为未连接
                    if "NoneType" in str(e) or "connection" in str(e).lower():
                        self.redis_connected = False
                    await asyncio.sleep(5)  # 异常后等待5秒再重试
                    
        except asyncio.CancelledError:
            logger.info(f"通知订阅任务已取消: {employee_id}")
        except Exception as e:
            logger.error(f"通知订阅任务异常 {employee_id}: {e}")
    
    async def _send_notification(self, employee_id: str, notification: Dict):
        """发送通知给客户端"""
        if employee_id not in self.active_connections:
            return
        
        try:
            websocket = self.active_connections[employee_id]
            
            # 添加发送时间戳
            notification["sent_at"] = datetime.utcnow().isoformat()
            
            await websocket.send_text(json.dumps(notification))
            logger.debug(f"通知已发送: {employee_id} -> {notification.get('type')}")
            
        except Exception as e:
            logger.error(f"发送通知失败 {employee_id}: {e}")
            # 连接可能已断开，清理连接
            await self.disconnect(employee_id)
    
    async def _send_connection_confirm(self, websocket: WebSocket, employee_id: str):
        """发送连接确认消息"""
        try:
            confirm_msg = {
                "type": "connection_confirm",
                "employee_id": employee_id,
                "timestamp": datetime.utcnow().isoformat(),
                "message": "通知连接已建立，将接收MDB同步状态更新"
            }
            
            await websocket.send_text(json.dumps(confirm_msg))
            
        except Exception as e:
            logger.error(f"发送连接确认失败 {employee_id}: {e}")
    
    async def get_connection_count(self) -> int:
        """获取连接数"""
        return len(self.active_connections)
    
    async def get_connected_users(self) -> list:
        """获取已连接用户列表"""
        return list(self.active_connections.keys())

# 全局连接管理器
notification_manager = NotificationConnectionManager()

@router.websocket("/ws/notify/{employee_id}")
async def notification_websocket_endpoint(websocket: WebSocket, employee_id: str, token: str):
    """通知WebSocket端点"""
    try:
        # 验证JWT token
        payload = verify_jwt_token_websocket(token)
        token_employee_id = payload.get("employee_id", "unknown")
        
        # 验证employee_id是否匹配
        if token_employee_id != employee_id:
            await websocket.close(code=4003, reason="Employee ID mismatch")
            return
        
        # 连接用户
        await notification_manager.connect(websocket, employee_id)
        
        try:
            # 保持连接，监听客户端消息
            while True:
                # 接收客户端消息（心跳等）
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type", "")
                
                if message_type == "ping":
                    # 心跳响应
                    pong_msg = {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(pong_msg))
                
                elif message_type == "get_status":
                    # 获取连接状态
                    status_msg = {
                        "type": "status",
                        "employee_id": employee_id,
                        "connected": True,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(status_msg))
                
                else:
                    logger.debug(f"收到未知消息类型: {message_type}")
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket断开: {employee_id}")
            
        except json.JSONDecodeError:
            logger.warning(f"无效的JSON消息: {employee_id}")
            
        except Exception as e:
            logger.error(f"通知WebSocket异常 {employee_id}: {e}")
            
    except Exception as e:
        logger.error(f"通知WebSocket连接失败 {employee_id}: {e}")
        try:
            await websocket.close(code=4000, reason="Connection failed")
        except:
            pass
    
    finally:
        # 清理连接
        await notification_manager.disconnect(employee_id)

@router.get("/status")
async def get_notification_status():
    """获取通知服务状态"""
    try:
        connection_count = await notification_manager.get_connection_count()
        connected_users = await notification_manager.get_connected_users()
        
        return {
            "service": "notification_gateway",
            "status": "running",
            "connection_count": connection_count,
            "connected_users": connected_users,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取通知状态失败: {e}")
        return {
            "service": "notification_gateway",
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        } 