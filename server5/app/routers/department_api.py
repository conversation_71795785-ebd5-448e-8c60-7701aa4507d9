# server5/app/routers/department_api.py
# 部门API路由 - 提供部门信息查询
# 20250710 - 支持 HTTP-only 模式，提供部门信息服务

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient
from app.database.postgresql_client import PostgreSQLClient

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/department", tags=["department"])

# 检查是否为 HTTP-only 模式
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

# ===== 依赖注入 =====

async def get_imdb_client() -> IMDBClient:
    """获取IMDB客户端"""
    if HTTP_ONLY_MODE:
        # HTTP-only 模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and service_manager.imdb_client:
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="HTTP-only 模式下数据库连接不可用")
    else:
        # 完整模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and hasattr(service_manager, 'imdb_client'):
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="数据库连接不可用")

async def get_auth_db_client() -> PostgreSQLClient:
    """获取auth数据库客户端"""
    if HTTP_ONLY_MODE:
        # HTTP-only 模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and service_manager.auth_db_client:
            return service_manager.auth_db_client
        else:
            raise HTTPException(status_code=503, detail="HTTP-only 模式下auth数据库连接不可用")
    else:
        # 完整模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and hasattr(service_manager, 'auth_db_client'):
            return service_manager.auth_db_client
        else:
            raise HTTPException(status_code=503, detail="auth数据库连接不可用")

# ===== API 路由 =====

@router.get("/list")
async def get_departments(
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取所有部门列表"""
    try:
        # 从 entries 表获取所有不同的部门
        query = """
            SELECT DISTINCT
                department,
                COUNT(*) as entry_count
            FROM entries
            WHERE department IS NOT NULL 
              AND department != ''
            GROUP BY department
            ORDER BY department
        """
        
        records = await imdb_client.execute_query(query)
        
        # 格式化部门列表
        departments = []
        for record in records:
            departments.append({
                "code": record["department"],
                "name": record["department"],
                "entry_count": record["entry_count"]
            })
        
        # 如果没有从数据库获取到部门信息，返回默认部门
        if not departments:
            departments = [
                {"code": "DEFAULT", "name": "默认部门", "entry_count": 0},
                {"code": "PRODUCTION", "name": "生产部门", "entry_count": 0},
                {"code": "QUALITY", "name": "质量部门", "entry_count": 0},
                {"code": "MAINTENANCE", "name": "维护部门", "entry_count": 0}
            ]
        
        return {"ok": True, "data": departments, "message": "查询成功"}
        
    except Exception as e:
        logger.error(f"❌ 获取部门列表失败: {e}")
        # 返回默认部门信息
        default_departments = [
            {"code": "DEFAULT", "name": "默认部门", "entry_count": 0}
        ]
        return {"ok": True, "data": default_departments, "message": "使用默认部门"}

@router.get("/info")
async def get_department_info(
    department_code: str = Query(..., description="部门代码"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取特定部门信息"""
    try:
        # 获取部门统计信息
        query = """
            SELECT 
                department,
                COUNT(*) as total_entries,
                COUNT(DISTINCT employee_id) as employee_count,
                COUNT(DISTINCT entry_date) as work_days,
                SUM(CAST(duration AS FLOAT)) as total_duration,
                AVG(CAST(duration AS FLOAT)) as avg_duration
            FROM entries
            WHERE department = $1
            GROUP BY department
        """
        
        result = await imdb_client.execute_query(query, department_code)
        
        if result:
            stats = result[0]
            department_info = {
                "code": department_code,
                "name": department_code,
                "total_entries": stats["total_entries"],
                "employee_count": stats["employee_count"],
                "work_days": stats["work_days"],
                "total_duration": round(float(stats["total_duration"] or 0), 2),
                "avg_duration": round(float(stats["avg_duration"] or 0), 2)
            }
            
            return {"ok": True, "data": department_info, "message": "查询成功"}
        else:
            # 如果没有找到部门，返回默认信息
            default_info = {
                "code": department_code,
                "name": department_code,
                "total_entries": 0,
                "employee_count": 0,
                "work_days": 0,
                "total_duration": 0.0,
                "avg_duration": 0.0
            }
            return {"ok": True, "data": default_info, "message": "部门无数据"}
        
    except Exception as e:
        logger.error(f"❌ 获取部门信息失败: {e}")
        return {"ok": False, "data": {}, "message": f"获取部门信息失败: {str(e)}"}

@router.get("/employees")
async def get_department_employees(
    department_code: str = Query(..., description="部门代码"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取部门员工列表"""
    try:
        # 获取部门员工列表
        query = """
            SELECT DISTINCT
                employee_id,
                COUNT(*) as entry_count,
                SUM(CAST(duration AS FLOAT)) as total_duration,
                MAX(entry_date) as last_entry_date
            FROM entries
            WHERE department = $1
            GROUP BY employee_id
            ORDER BY employee_id
        """
        
        records = await imdb_client.execute_query(query, department_code)
        
        # 格式化员工列表
        employees = []
        for record in records:
            employees.append({
                "employee_id": record["employee_id"],
                "entry_count": record["entry_count"],
                "total_duration": round(float(record["total_duration"] or 0), 2),
                "last_entry_date": record["last_entry_date"].isoformat() if record["last_entry_date"] else None
            })
        
        return {"ok": True, "data": employees, "message": "查询成功"}
        
    except Exception as e:
        logger.error(f"❌ 获取部门员工失败: {e}")
        return {"ok": False, "data": [], "message": f"获取部门员工失败: {str(e)}"}

@router.get("/stats")
async def get_department_stats(
    department_code: Optional[str] = Query(None, description="部门代码，为空时获取所有部门统计"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取部门统计信息"""
    try:
        if department_code:
            # 获取特定部门统计
            query = """
                SELECT 
                    department,
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT employee_id) as employee_count,
                    COUNT(DISTINCT entry_date) as work_days,
                    SUM(CAST(duration AS FLOAT)) as total_duration,
                    AVG(CAST(duration AS FLOAT)) as avg_duration
                FROM entries
                WHERE department = $1
                GROUP BY department
            """
            records = await imdb_client.execute_query(query, department_code)
        else:
            # 获取所有部门统计
            query = """
                SELECT 
                    department,
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT employee_id) as employee_count,
                    COUNT(DISTINCT entry_date) as work_days,
                    SUM(CAST(duration AS FLOAT)) as total_duration,
                    AVG(CAST(duration AS FLOAT)) as avg_duration
                FROM entries
                WHERE department IS NOT NULL AND department != ''
                GROUP BY department
                ORDER BY department
            """
            records = await imdb_client.execute_query(query)
        
        # 格式化统计数据
        stats = []
        for record in records:
            stats.append({
                "department": record["department"],
                "total_entries": record["total_entries"],
                "employee_count": record["employee_count"],
                "work_days": record["work_days"],
                "total_duration": round(float(record["total_duration"] or 0), 2),
                "avg_duration": round(float(record["avg_duration"] or 0), 2)
            })
        
        return {"ok": True, "data": stats, "message": "查询成功"}
        
    except Exception as e:
        logger.error(f"❌ 获取部门统计失败: {e}")
        return {"ok": False, "data": [], "message": f"获取部门统计失败: {str(e)}"}



@router.get("/employee/{employee_id}")
async def get_employee_department(
    employee_id: str,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    返回格式:
    {
        "ok": True,
        "data": {
            "employee_id": "E001",
            "department": "MFG"
        },
        "message": "获取成功"
    }
    """
    try:
        query = """
            SELECT department
            FROM entries
            WHERE employee_id = $1
              AND department IS NOT NULL
              AND department <> ''
            ORDER BY entry_date DESC
            LIMIT 1
        """
        rows = await imdb_client.execute_query(query, employee_id)
        dept = rows[0]["department"] if rows else "111"

        return {
            "ok": True,
            "data": {"employee_id": employee_id, "department": dept},
            "message": "获取成功" if rows else "未找到部门信息，使用默认值"
        }

    except Exception as e:
        logger.error(f"❌ 获取员工部门信息失败: {e}")
        return {"ok": False, "data": {}, "message": str(e)}

@router.get("/auth/employee/{employee_id}")
async def get_employee_from_auth(
    employee_id: str,
    auth_db_client: PostgreSQLClient = Depends(get_auth_db_client)
):
    """
    从auth数据库的members表中获取员工信息
    返回格式:
    {
        "ok": True,
        "data": {
            "employee_id": "215829",
            "name": "mike",
            "department": "131"
        },
        "message": "获取成功"
    }
    """
    try:
        # 从auth数据库的members表中查询员工信息
        query = """
            SELECT name, department
            FROM members
            WHERE employee_id = $1
        """
        
        rows = await auth_db_client.execute_query(query, employee_id)
        
        if rows:
            # 找到员工记录
            row = rows[0]
            name = row.get("name", "") or ""  # 处理空值或空字符串
            department = row.get("department", "111")  # 默认部门为111
            
            logger.info(f"✅ 从auth数据库获取员工信息成功: {employee_id}, name={name}, department={department}")
            
            return {
                "ok": True,
                "data": {
                    "employee_id": employee_id,
                    "name": name,
                    "department": department
                },
                "message": "获取成功"
            }
        else:
            # 未找到员工记录，返回默认值
            logger.warning(f"⚠️ 在auth数据库中未找到员工: {employee_id}，使用默认值")
            
            return {
                "ok": True,
                "data": {
                    "employee_id": employee_id,
                    "name": "",
                    "department": "111"
                },
                "message": "未找到员工信息，使用默认值"
            }

    except Exception as e:
        logger.error(f"❌ 从auth数据库获取员工信息失败: {e}")
        return {
            "ok": False, 
            "data": {
                "employee_id": employee_id,
                "name": "",
                "department": "111"
            }, 
            "message": f"获取员工信息失败: {str(e)}"
        }
