# server5/app/database/postgresql_client.py
# PostgreSQL异步客户端 - 支持LISTEN/NOTIFY和分区表操作

import asyncio
import asyncpg
from typing import Optional, List, Dict, Any, Callable
from datetime import datetime, timedelta
import logging
import sys
from pathlib import Path
import decimal
sys.path.append(str(Path(__file__).parent.parent.parent))

# 动态导入配置
import importlib
import os

config_name = os.environ.get("MY_SUITE_CONFIG", "config_ubuntu_remote")
config = importlib.import_module(f"config.{config_name}")

PG_DATABASE_URL = getattr(config, 'PG_DATABASE_URL', '')
IMDB_DATABASE_URL = getattr(config, 'IMDB_DATABASE_URL', '')
DB_CONNECTION_TIMEOUT = getattr(config, 'DB_CONNECTION_TIMEOUT', 10)

logger = logging.getLogger(__name__)

class PostgreSQLClient:
    """PostgreSQL异步客户端"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.pool: Optional[asyncpg.Pool] = None
        self.listeners: Dict[str, List[Callable]] = {}
        self._listen_task: Optional[asyncio.Task] = None
        
    async def connect(self):
        """建立连接池"""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=10,
                max_queries=50000,
                max_inactive_connection_lifetime=300,
                timeout=DB_CONNECTION_TIMEOUT
            )
            logger.info("✅ PostgreSQL连接池创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ PostgreSQL连接失败: {e}")
            return False
    
    async def disconnect(self):
        """关闭连接"""
        if self._listen_task and not self._listen_task.done():
            self._listen_task.cancel()
            
        if self.pool:
            await self.pool.close()
            logger.info("🔌 PostgreSQL连接已关闭")
    
    async def execute_query(self, query: str, *args) -> List[Dict]:
        """执行查询并返回结果"""
        if not self.pool:
            raise RuntimeError("数据库连接池未初始化")
            
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetch(query, *args)
                return [dict(row) for row in result]
            except Exception as e:
                logger.error(f"查询执行失败: {query[:100]}... 错误: {e}")
                raise
    
    async def execute_command(self, command: str, *args) -> str:
        """执行命令(INSERT/UPDATE/DELETE)"""
        if not self.pool:
            raise RuntimeError("数据库连接池未初始化")
            
        async with self.pool.acquire() as conn:
            try:
                result = await conn.execute(command, *args)
                logger.debug(f"命令执行成功: {result}")
                return result
            except Exception as e:
                logger.error(f"命令执行失败: {command[:100]}... 错误: {e}")
                raise
    
    async def fetch_one(self, query: str, *args) -> Optional[Dict]:
        """获取单行结果"""
        if not self.pool:
            raise RuntimeError("数据库连接池未初始化")
            
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetchrow(query, *args)
                return dict(result) if result else None
            except Exception as e:
                logger.error(f"单行查询失败: {e}")
                raise
    
    async def add_listener(self, channel: str, callback: Callable[[str, str], None]):
        """添加NOTIFY监听器"""
        if channel not in self.listeners:
            self.listeners[channel] = []
        self.listeners[channel].append(callback)
        
        # 如果是第一个监听器，启动监听任务
        if not self._listen_task or self._listen_task.done():
            # 必须使用一个独立的连接来监听，因为它会阻塞
            self._listen_task = asyncio.create_task(self._listen_notifications())
    
    async def _listen_notifications(self):
        """监听PostgreSQL NOTIFY事件"""
        # 使用独立的连接进行监听，以避免阻塞连接池中的其他操作
        conn = await asyncpg.connect(self.database_url)
        try:
            # 监听所有注册的频道
            for channel in self.listeners.keys():
                await conn.add_listener(channel, self._notification_handler)
                logger.info(f"📡 开始监听频道: {channel}")
            
            # 保持任务运行以接收通知
            while True:
                await asyncio.sleep(3600) # 保持任务存活
                    
        except asyncio.CancelledError:
            logger.info("监听任务被取消")
        except Exception as e:
            logger.error(f"监听任务失败: {e}", exc_info=True)
        finally:
            if conn and not conn.is_closed():
                # 移除所有监听器
                for channel in self.listeners.keys():
                    try:
                        await conn.remove_listener(channel, self._notification_handler)
                    except Exception as e:
                        logger.warning(f"移除监听器 {channel} 失败: {e}")
                await conn.close()
                logger.info("监听连接已关闭")

    async def _notification_handler(self, connection, pid, channel, payload):
        """处理收到的通知"""
        logger.debug(f"📨 收到通知: {channel} -> {payload}")
        
        # 调用所有注册的回调函数
        if channel in self.listeners:
            for callback in self.listeners[channel]:
                try:
                    # 将回调作为新任务运行，避免阻塞通知处理程序
                    asyncio.create_task(callback(channel, payload))
                except Exception as e:
                    logger.error(f"创建回调任务失败: {e}")
    
    async def notify(self, channel: str, payload: str = ""):
        """发送NOTIFY消息"""
        if not self.pool:
            raise RuntimeError("数据库连接池未初始化")
            
        async with self.pool.acquire() as conn:
            try:
                await conn.execute(f"NOTIFY {channel}, '{payload}'")
                logger.debug(f"📤 发送通知: {channel} -> {payload}")
            except Exception as e:
                logger.error(f"发送通知失败: {e}")
                raise

class IMDBClient(PostgreSQLClient):
    """IMDB数据库客户端 - 专门处理entries分区表"""
    
    def __init__(self):
        super().__init__(IMDB_DATABASE_URL)
    
    async def get_user_entries(self, employee_id: str, days: int = 30) -> List[Dict]:
        """获取用户的entries数据"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        query = """
            SELECT * FROM entries 
            WHERE employee_id = $1 
              AND entry_date BETWEEN $2 AND $3
            ORDER BY entry_date DESC, ts DESC
        """
        
        return await self.execute_query(query, employee_id, start_date, end_date)
    
    async def create_entry(self, entry_data: Dict, source: str = 'user') -> int:
        """创建新的entries记录"""
        # 转换entry_date字符串为date对象
        entry_date_str = entry_data['entry_date']
        if isinstance(entry_date_str, str):
            # 处理 "2025/06/27" 格式
            if '/' in entry_date_str:
                from datetime import datetime
                entry_date = datetime.strptime(entry_date_str, "%Y/%m/%d").date()
            else:
                # 处理 "2025-06-27" 格式
                from datetime import datetime
                entry_date = datetime.strptime(entry_date_str, "%Y-%m-%d").date()
        else:
            entry_date = entry_date_str
        
        query = """
            INSERT INTO entries (
                employee_id, entry_date, model, number,
                factory_number, project_number, unit_number,
                category, item, duration, department, source
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING id
        """
        
        result = await self.fetch_one(
            query,
            entry_data['employee_id'],
            entry_date,
            entry_data.get('model'),
            entry_data.get('number'),
            entry_data.get('factory_number'),
            entry_data.get('project_number'),
            entry_data.get('unit_number'),
            entry_data['category'],
            entry_data['item'],
            entry_data['duration'],
            entry_data['department'],
            source  # 添加source字段
        )
        
        return result['id'] if result else None
    
    async def get_active_employee_ids(self, days: int = 90) -> List[str]:
        """获取最近指定天数内有活动记录的员工ID列表"""
        since_date = datetime.now().date() - timedelta(days=days)
        query = """
            SELECT DISTINCT employee_id 
            FROM entries
            WHERE entry_date >= $1
        """
        try:
            results = await self.execute_query(query, since_date)
            return [row['employee_id'] for row in results]
        except Exception as e:
            logger.error(f"获取活跃员工ID失败: {e}")
            return []
    
    async def update_external_id(self, entry_id: int, external_id: int, trigger_queue: bool = True):
        """更新entries记录的external_id"""
        if trigger_queue:
            # 正常更新，会触发队列
            query = "UPDATE entries SET external_id = $1 WHERE id = $2"
            await self.execute_command(query, external_id, entry_id)
        else:
            # 系统更新，不触发队列
            query = "UPDATE entries SET external_id = $1, source = 'system' WHERE id = $2"
            await self.execute_command(query, external_id, entry_id)
    
    async def update_external_id_system(self, entry_id: int, external_id: int):
        """系统更新external_id，不触发队列"""
        query = "UPDATE entries SET external_id = $1, source = 'system' WHERE id = $2"
        await self.execute_command(query, external_id, entry_id)
    
    async def get_queue_items(self, synced: bool = False, limit: int = 100) -> List[Dict]:
        """获取队列中的待处理项目"""
        query = """
            SELECT q.queue_id, q.operation, q.entry_id, q.created_ts,
                   e.*
            FROM entries_push_queue q
            JOIN entries e ON e.id = q.entry_id
            WHERE q.synced = $1
            ORDER BY q.created_ts ASC
            LIMIT $2
        """
        
        return await self.execute_query(query, synced, limit)
    
    async def mark_queue_synced(self, queue_id: int, error_message: str = None):
        """标记队列项为已同步"""
        # 根据数据库结构，entries_push_queue表只有synced布尔字段
        # 没有synced_at和error_message字段
        query = """
            UPDATE entries_push_queue 
            SET synced = TRUE
            WHERE queue_id = $1
        """
        await self.execute_command(query, queue_id)
    
    async def create_partition_if_needed(self, month_code: str):
        """创建指定月份的分区(如果不存在)"""
        try:
            # 解析月份代码 (格式: YYYYMM)
            year = int(month_code[:4])
            month = int(month_code[4:])
            
            # 计算分区边界
            start_date = f"{year}-{month:02d}-01"
            if month == 12:
                end_date = f"{year+1}-01-01"
            else:
                end_date = f"{year}-{month+1:02d}-01"
            
            # 使用新的命名格式：entries_YYYY_MM
            partition_name = f"entries_{year}_{month:02d}"
            
            query = f"""
                CREATE TABLE IF NOT EXISTS {partition_name} 
                PARTITION OF entries
                FOR VALUES FROM ('{start_date}') TO ('{end_date}')
            """
            
            await self.execute_command(query)
            logger.info(f"✅ 分区创建成功: {partition_name}")
            
        except Exception as e:
            logger.error(f"❌ 分区创建失败 {month_code}: {e}")
            raise
    
    async def get_connection_status(self) -> Dict:
        """获取数据库连接状态"""
        try:
            if not self.pool:
                return {"status": "disconnected", "error": "连接池未初始化"}
            
            # 测试连接
            result = await self.fetch_one("SELECT NOW() as current_time, version() as pg_version")
            
            return {
                "status": "connected",
                "current_time": result["current_time"],
                "pg_version": result["pg_version"],
                "pool_size": self.pool.get_size(),
                "pool_max_size": self.pool.get_max_size(),
                "pool_min_size": self.pool.get_min_size()
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def fetch_all(self, query: str, *args) -> List[Dict]:
        """获取所有查询结果 - 兼容旧代码"""
        return await self.execute_query(query, *args)
    
    async def get_entries_by_month(self, year: int, month: int, limit: int = 1000) -> List[Dict]:
        """获取指定月份的entries数据"""
        query = """
            SELECT entry_date, employee_id, model, number, factory_number,
                   project_number, unit_number, category, item, duration,
                   department, external_id, ts, id
            FROM entries 
            WHERE EXTRACT(YEAR FROM entry_date) = $1 
              AND EXTRACT(MONTH FROM entry_date) = $2
            ORDER BY entry_date DESC, ts DESC
            LIMIT $3
        """
        
        return await self.execute_query(query, year, month, limit)
    
    async def get_available_months(self) -> List[Dict]:
        """获取entries表中可用的月份列表"""
        query = """
            SELECT DISTINCT 
                EXTRACT(YEAR FROM entry_date) as year,
                EXTRACT(MONTH FROM entry_date) as month,
                COUNT(*) as count
            FROM entries 
            GROUP BY EXTRACT(YEAR FROM entry_date), EXTRACT(MONTH FROM entry_date)
            ORDER BY year DESC, month DESC
        """
        
        return await self.execute_query(query)
    
    async def clear_test_data(self):
        """清理entries表中的测试数据"""
        # 删除external_id为1000-1010的测试数据
        query = """
            DELETE FROM entries 
            WHERE external_id BETWEEN 1000 AND 1010
               OR employee_id LIKE 'EMP%'
               OR employee_id = 'test_employee'
        """
        
        result = await self.execute_command(query)
        logger.info(f"🗑️ 清理测试数据完成: {result}")
        return result
    
    async def clear_staging_table(self):
        """清空staging_entries表"""
        query = "DELETE FROM staging_entries"
        try:
            await self.execute_command(query)
            logger.info("🗑️ staging_entries 表已清空")
        except Exception as e:
            logger.error(f"清空staging_entries表失败: {e}")
            raise

    async def bulk_insert_staging(self, records: List[Dict]):
        """批量插入数据到staging_entries表"""
        if not records:
            return
            
        def _round_duration(value) -> float:
            """对duration值进行精度控制，避免浮点数精度问题"""
            if value is None:
                return 0.0
            
            try:
                # 使用decimal进行精确的四舍五入
                decimal_value = decimal.Decimal(str(value))
                # 四舍五入到2位小数
                rounded_value = decimal_value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
                return float(rounded_value)
            except (ValueError, TypeError, decimal.InvalidOperation):
                # 如果转换失败，返回0.0
                logger.warning(f"⚠️ duration值转换失败: {value}, 使用默认值0.0")
                return 0.0
        
        def _format_number_field(value) -> str:
            """对号機字段进行格式化，确保数字类型转换为整数格式的字符串"""
            if value is None:
                return ""
            
            try:
                # 如果是数字类型，转换为整数再转为字符串
                if isinstance(value, (int, float)):
                    # 对于浮点数，先四舍五入到整数
                    if isinstance(value, float):
                        value = round(value)
                    return str(int(value))
                else:
                    # 如果是字符串，尝试转换为数字再格式化
                    numeric_value = float(value)
                    return str(int(round(numeric_value)))
            except (ValueError, TypeError):
                # 如果转换失败，返回原始值的字符串形式
                return str(value) if value is not None else ""

        query = """
            INSERT INTO staging_entries (
                external_id, entry_date, ts, employee_id, model, number,
                factory_number, project_number, unit_number,
                category, item, duration, department
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
            )
        """
        
        # 将字典列表转换为元组列表
        data_to_insert = []
        for r in records:
            # 转换entry_date字符串为date对象
            entry_date_str = r['entry_date']
            if isinstance(entry_date_str, str):
                # 处理 "2025/06/25" 格式
                if '/' in entry_date_str:
                    from datetime import datetime
                    entry_date = datetime.strptime(entry_date_str, "%Y/%m/%d").date()
                else:
                    # 处理 "2025-06-25" 格式
                    from datetime import datetime
                    entry_date = datetime.strptime(entry_date_str, "%Y-%m-%d").date()
            else:
                entry_date = entry_date_str
            
            # 转换ts字符串为datetime对象
            ts_str = r['ts']
            if isinstance(ts_str, str):
                from datetime import datetime
                # 处理 ISO 格式 "2025-06-27T17:48:16.453984"
                if 'T' in ts_str:
                    ts = datetime.fromisoformat(ts_str.replace('Z', '+00:00'))
                else:
                    # 处理其他格式，尝试解析
                    ts = datetime.strptime(ts_str, "%Y-%m-%d %H:%M:%S")
            else:
                ts = ts_str
            
            # 转换字段类型以匹配PostgreSQL schema
            # number: TEXT (需要字符串) - 使用格式化处理
            number_val = _format_number_field(r.get('number'))
            
            # category: INT (需要整数)
            category_val = int(r['category']) if r.get('category') is not None else None
            
            # item: INT (需要整数)
            item_val = int(r['item']) if r.get('item') is not None else None
            
            # duration: NUMERIC (使用精度控制)
            duration_val = _round_duration(r.get('duration'))
            
            data_to_insert.append((
                r['external_id'], entry_date, ts, r['employee_id'],
                r.get('model'), number_val, r.get('factory_number'),
                r.get('project_number'), r.get('unit_number'), category_val,
                item_val, duration_val, r.get('department')
            ))

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                await conn.executemany(query, data_to_insert)
        logger.info(f"批量插入 {len(records)} 条记录到 staging_entries")

    async def upsert_from_staging(self) -> int:
        """
        从staging_entries执行UPSERT到entries分区表
        - 自动创建所需的分区表
        - ON CONFLICT: 如果external_id和entry_date冲突, 则更新
        - 返回受影响的行数
        """
        try:
            # 1. 首先获取staging表中所有需要的月份
            months_query = """
                SELECT DISTINCT 
                    EXTRACT(YEAR FROM entry_date) as year,
                    EXTRACT(MONTH FROM entry_date) as month
                FROM staging_entries
                ORDER BY year, month
            """
            
            months_data = await self.execute_query(months_query)
            logger.info(f"📊 staging表中包含 {len(months_data)} 个月份的数据")
            
            # 2. 为每个月份创建分区（如果不存在）
            for month_data in months_data:
                year = int(month_data['year'])
                month = int(month_data['month'])
                month_code = f"{year}{month:02d}"
                
                try:
                    await self.create_partition_if_needed(month_code)
                except Exception as e:
                    logger.error(f"❌ 创建分区失败 {month_code}: {e}")
                    # 继续处理其他月份，不中断整个流程
            
            # 3. 执行UPSERT操作
            query = """
                INSERT INTO entries (
                    external_id, entry_date, ts, employee_id, model, number,
                    factory_number, project_number, unit_number,
                    category, item, duration, department, source
                )
                SELECT
                    s.external_id, s.entry_date, s.ts, s.employee_id, s.model, s.number,
                    s.factory_number, s.project_number, s.unit_number,
                    s.category, s.item, s.duration, s.department, 'system'
                FROM staging_entries s
                ON CONFLICT (entry_date, external_id) DO UPDATE SET
                    ts = EXCLUDED.ts,
                    model = EXCLUDED.model,
                    number = EXCLUDED.number,
                    factory_number = EXCLUDED.factory_number,
                    project_number = EXCLUDED.project_number,
                    unit_number = EXCLUDED.unit_number,
                    category = EXCLUDED.category,
                    item = EXCLUDED.item,
                    duration = EXCLUDED.duration,
                    department = EXCLUDED.department,
                    source = 'system'
                WHERE
                    -- 只有在时间戳不同时才更新
                    entries.ts <> EXCLUDED.ts;
            """
            
            result_str = await self.execute_command(query)
            # 从 "INSERT 0 123" 这样的字符串中解析数字
            affected_rows = int(result_str.split()[-1])
            logger.info(f"UPSERT完成，影响行数: {affected_rows}")
            return affected_rows
            
        except Exception as e:
            logger.error(f"UPSERT from staging 失败: {e}")
            raise 

    async def get_external_ids_in_range(self, start_date: datetime.date, end_date: datetime.date) -> set:
        """
        # 2025 07/04 +  16：00 + 相关主题: 为f5删除同步添加
        获取指定日期范围内所有唯一的、非空的 external_id。
        """
        query = """
            SELECT DISTINCT external_id 
            FROM entries 
            WHERE entry_date BETWEEN $1 AND $2
              AND external_id IS NOT NULL
        """
        try:
            results = await self.execute_query(query, start_date, end_date)
            return {row['external_id'] for row in results}
        except Exception as e:
            logger.error(f"获取 external_id 范围查询失败: {e}", exc_info=True)
            return set()

    async def get_external_ids_in_range_for_employee(self, employee_id: str, start_date: datetime.date, end_date: datetime.date) -> set:
        """
        # 2025 07/15 + 新增: 为f5员工删除同步添加
        获取指定员工在指定日期范围内所有唯一的、非空的 external_id。
        """
        query = """
            SELECT DISTINCT external_id 
            FROM entries 
            WHERE employee_id = $1
              AND entry_date BETWEEN $2 AND $3
              AND external_id IS NOT NULL
        """
        try:
            results = await self.execute_query(query, employee_id, start_date, end_date)
            return {row['external_id'] for row in results}
        except Exception as e:
            logger.error(f"获取员工 {employee_id} 的 external_id 范围查询失败: {e}", exc_info=True)
            return set()

    async def delete_entries_by_external_ids(self, ids: List[int]) -> int:
        """
        # 2025 07/04 +  16：00 + 相关主题: 为f5删除同步添加
        根据 external_id 列表批量删除记录。
        """
        if not ids:
            return 0
            
        # 使用事务来确保操作的原子性
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                try:
                    # 临时禁用触发器以避免触发f2同步
                    await conn.execute("SET session_replication_role = 'replica'")
                    
                    query = "DELETE FROM entries WHERE external_id = ANY($1::int[])"
                    result_str = await conn.execute(query, ids)
                    
                    # 恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    
                    # 'DELETE N' -> N
                    deleted_count = int(result_str.split(' ')[1])
                    logger.info(f"成功删除了 {deleted_count} 条记录 (已禁用触发器)")
                    return deleted_count
                except (ValueError, IndexError) as e:
                    logger.error(f"解析删除计数失败: {result_str}, 错误: {e}")
                    # 确保即使出错也恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    return 0
                except Exception as e:
                    logger.error(f"根据 external_id 批量删除失败: {e}", exc_info=True)
                    # 确保即使出错也恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    return 0

    async def delete_entries_by_external_ids_system(self, ids: List[int]) -> int:
        """
        # 2025 07/15 + 新增: 为f5员工删除同步添加
        根据 external_id 列表批量删除记录（系统操作，不改变source属性）。
        """
        if not ids:
            return 0
            
        # 使用事务来确保操作的原子性
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                try:
                    # 临时禁用触发器以避免触发f2同步
                    await conn.execute("SET session_replication_role = 'replica'")
                    
                    query = "DELETE FROM entries WHERE external_id = ANY($1::int[])"
                    result_str = await conn.execute(query, ids)
                    
                    # 恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    
                    # 'DELETE N' -> N
                    deleted_count = int(result_str.split(' ')[1])
                    logger.info(f"成功删除了 {deleted_count} 条记录 (系统删除，已禁用触发器)")
                    return deleted_count
                except (ValueError, IndexError) as e:
                    logger.error(f"解析删除计数失败: {result_str}, 错误: {e}")
                    # 确保即使出错也恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    return 0
                except Exception as e:
                    logger.error(f"根据 external_id 批量删除失败: {e}", exc_info=True)
                    # 确保即使出错也恢复触发器
                    await conn.execute("SET session_replication_role = 'origin'")
                    return 0