2025-07-10 14:08:59,671 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 14:08:59,672 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 14:08:59,672 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 14:08:59,672 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 14:08:59,673 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 14:08:59,673 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 14:08:59,683 - INFO - Redis连接成功
2025-07-10 14:08:59,683 - INFO - Redis连接成功
2025-07-10 14:08:59,684 - INFO - Redis连接成功
2025-07-10 14:08:59,690 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,690 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,691 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,968 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:08:59,969 - INFO - Redis连接成功
2025-07-10 14:08:59,969 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 14:08:59,969 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 14:08:59,969 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 42660 秒...
2025-07-10 14:08:59,995 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:08:59,995 - INFO - ✅ f1监听器服务启动成功
2025-07-10 14:09:00,029 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:00,029 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 14:09:00,030 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 14:09:00,032 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 14:09:00,036 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 14:09:00,094 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:00,094 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 14:09:00,094 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 14:09:00,094 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 14:09:00,096 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 14:09:00,096 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 14:09:09,434 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,435 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,436 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,436 - INFO - 🔌 f1监听器服务已停止
2025-07-10 14:09:09,436 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 14:09:09,436 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 f1监听器服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 18:08:39,905 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 18:08:39,905 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 18:08:39,905 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 18:08:39,905 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 18:08:39,906 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 18:08:39,906 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 18:08:39,915 - INFO - Redis连接成功
2025-07-10 18:08:39,915 - INFO - Redis连接成功
2025-07-10 18:08:39,916 - INFO - Redis连接成功
2025-07-10 18:08:39,922 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:39,923 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:39,924 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:40,245 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,246 - INFO - Redis连接成功
2025-07-10 18:08:40,246 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 18:08:40,246 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 18:08:40,246 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 28280 秒...
2025-07-10 18:08:40,263 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,263 - INFO - ✅ f1监听器服务启动成功
2025-07-10 18:08:40,281 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,281 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 18:08:40,281 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 18:08:40,286 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,286 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 18:08:40,286 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 18:08:40,286 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 18:08:40,488 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 18:08:40,509 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-14 16:40:00,322 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-749' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 11:18:15,206 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:18:15,206 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:18:15,206 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:18:15,206 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:18:15,207 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:18:15,207 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:18:15,216 - INFO - Redis连接成功
2025-07-15 11:18:15,216 - INFO - Redis连接成功
2025-07-15 11:18:15,217 - INFO - Redis连接成功
2025-07-15 11:18:15,222 - INFO - ✅ MongoDB连接成功
2025-07-15 11:18:15,223 - INFO - ✅ MongoDB连接成功
2025-07-15 11:18:15,223 - INFO - ✅ MongoDB连接成功
2025-07-15 11:18:15,523 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:18:15,524 - INFO - Redis连接成功
2025-07-15 11:18:15,524 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:18:15,524 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:18:15,524 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 52904 秒...
2025-07-15 11:18:15,550 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:18:15,550 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:18:15,559 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:18:15,563 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:18:15,608 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:18:15,608 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:18:15,608 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:18:15,608 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:18:15,608 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:18:15,609 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:18:15,639 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:18:15,639 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:18:15,639 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:18:15,640 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:18:15,641 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:18:15,641 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:21:35,601 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,602 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,603 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,603 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:21:35,603 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:21:35,603 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:21:35,780 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:21:35,780 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:21:35,781 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:21:35,781 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:21:35,781 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:21:35,781 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:21:37,058 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:21:37,058 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:21:37,058 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:21:37,058 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:21:37,059 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:21:37,059 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:21:37,069 - INFO - Redis连接成功
2025-07-15 11:21:37,069 - INFO - Redis连接成功
2025-07-15 11:21:37,069 - INFO - Redis连接成功
2025-07-15 11:21:37,075 - INFO - ✅ MongoDB连接成功
2025-07-15 11:21:37,075 - INFO - ✅ MongoDB连接成功
2025-07-15 11:21:37,077 - INFO - ✅ MongoDB连接成功
2025-07-15 11:21:37,366 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:21:37,366 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:21:37,371 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:21:37,371 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:21:37,371 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:21:37,372 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:21:37,372 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:21:37,372 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:21:37,376 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:21:37,379 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145170 - queue_id=128 - 重试次数=0
2025-07-15 11:21:37,380 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:21:37,425 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:21:37,425 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:21:37,480 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:21:37,481 - INFO - Redis连接成功
2025-07-15 11:21:37,481 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:21:37,481 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:21:37,481 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 52703 秒...
2025-07-15 11:21:37,482 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:21:37,482 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:21:37,483 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:21:37,586 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:22:02,051 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,054 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,056 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,058 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:22:02,058 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:22:02,058 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:22:02,080 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:22:02,080 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:22:02,080 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:22:02,081 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:22:02,081 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:22:02,081 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:23:30,707 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:23:30,707 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:23:30,707 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:23:30,707 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:23:30,708 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:23:30,708 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:23:30,717 - INFO - Redis连接成功
2025-07-15 11:23:30,717 - INFO - Redis连接成功
2025-07-15 11:23:30,717 - INFO - Redis连接成功
2025-07-15 11:23:30,722 - INFO - ✅ MongoDB连接成功
2025-07-15 11:23:30,723 - INFO - ✅ MongoDB连接成功
2025-07-15 11:23:30,724 - INFO - ✅ MongoDB连接成功
2025-07-15 11:23:30,985 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:23:30,985 - INFO - Redis连接成功
2025-07-15 11:23:30,986 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:23:30,986 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:23:30,986 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 52589 秒...
2025-07-15 11:23:31,048 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:23:31,048 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:23:31,070 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:23:31,070 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:23:31,071 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:23:31,071 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:23:31,071 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:23:31,071 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:23:31,075 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:23:31,079 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:23:31,079 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145170 - queue_id=128 - 重试次数=0
2025-07-15 11:23:31,124 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:23:31,124 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:23:31,124 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:23:31,124 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:23:31,126 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:23:31,307 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:26:26,968 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:26:26,968 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:26:26,968 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:26:26,968 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:26:26,969 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:26:26,969 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:26:26,979 - INFO - Redis连接成功
2025-07-15 11:26:26,979 - INFO - Redis连接成功
2025-07-15 11:26:26,979 - INFO - Redis连接成功
2025-07-15 11:26:26,983 - INFO - ✅ MongoDB连接成功
2025-07-15 11:26:26,985 - INFO - ✅ MongoDB连接成功
2025-07-15 11:26:26,986 - INFO - ✅ MongoDB连接成功
2025-07-15 11:26:27,285 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:26:27,306 - INFO - Redis连接成功
2025-07-15 11:26:27,306 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:26:27,306 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:26:27,306 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 52413 秒...
2025-07-15 11:26:27,310 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:26:27,310 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:26:27,348 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:26:27,348 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:26:27,349 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:26:27,349 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:26:27,349 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:26:27,350 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:26:27,351 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:26:27,354 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:26:27,354 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:26:27,354 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:26:27,354 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:26:27,496 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:26:27,513 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:26:54,611 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:54,612 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:54,613 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:54,613 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:26:54,613 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:26:54,613 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:26:54,989 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:26:54,989 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:26:55,004 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:26:55,004 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:26:55,004 - INFO - 🔌 f4操作处理服务已停止
2025-07-16 08:47:21,064 - INFO - 🔊 f1监听器服务初始化完成
2025-07-16 08:47:21,065 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-16 08:47:21,065 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-16 08:47:21,065 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-16 08:47:21,066 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-16 08:47:21,066 - INFO - 🎮 f4操作处理器初始化完成
2025-07-16 08:47:21,081 - INFO - Redis连接成功
2025-07-16 08:47:21,082 - INFO - Redis连接成功
2025-07-16 08:47:21,082 - INFO - Redis连接成功
2025-07-16 08:47:21,315 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:47:21,316 - INFO - Redis连接成功
2025-07-16 08:47:21,316 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-16 08:47:21,316 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-16 08:47:21,316 - INFO - 下一次f3/f5同步任务将在 2025-07-17 02:00:00 执行，等待 61959 秒...
2025-07-16 08:47:21,349 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:47:21,380 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:47:21,408 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:47:26,078 - ERROR - ❌ MongoDB连接失败: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6876e889d6cb9376baac89d4, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:47:26,078 - ERROR - 数据库连接失败，无法启动监听器
2025-07-16 08:47:26,080 - ERROR - ❌ MongoDB连接失败: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6876e889d6cb9376baac89d5, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:47:26,080 - ERROR - ❌ 数据库连接失败: [True, True, False]
2025-07-16 08:47:26,082 - ERROR - ❌ MongoDB连接失败: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6876e889d6cb9376baac89d6, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:47:26,082 - ERROR - ❌ 数据库连接失败，无法启动操作处理服务
2025-07-16 08:47:26,083 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,083 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,083 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,084 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,084 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,084 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,089 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,089 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,089 - INFO - 🔌 f3数据拉取服务已停止
2025-07-16 08:47:26,089 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,090 - INFO - 🔌 f4操作处理服务已停止
2025-07-16 08:47:26,090 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,091 - INFO - 🔌 f1监听器服务已停止
2025-07-16 08:47:26,091 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,092 - INFO - 🔌 f2推送回写服务已停止
2025-07-16 08:47:26,582 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 Redis连接已关闭
2025-07-16 08:47:26,583 - INFO - 🔌 f1监听器服务已停止
2025-07-16 08:47:26,583 - INFO - 🔌 f2推送回写服务已停止
2025-07-16 08:47:26,583 - INFO - 🔌 f4操作处理服务已停止
