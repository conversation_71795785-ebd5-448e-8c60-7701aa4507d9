INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3/app']
INFO:     Uvicorn running on http://0.0.0.0:8006 (Press CTRL+C to quit)
INFO:     Started reloader process [2766565] using WatchFiles
============================================================
🚀 Starting MySuite Auth Microservice
============================================================
📁 Working Directory: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3
🌐 Service Port: 8006
📝 API Documentation: http://localhost:8006/docs
🔍 Health Check: http://localhost:8006/health
============================================================
INFO:     Started server process [2766573]
INFO:     Waiting for application startup.
=== Auth Service Starting Up ===
2025-07-16 16:53:23,924 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-16 16:53:23,924 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-16 16:53:23,927 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-16 16:53:23,927 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-16 16:53:23,930 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-16 16:53:23,930 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-16 16:53:23,932 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 16:53:23,935 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-16 16:53:23,935 INFO sqlalchemy.engine.Engine [generated in 0.00014s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-16 16:53:23,957 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-16 16:53:23,957 INFO sqlalchemy.engine.Engine [cached since 0.02201s ago] ('hardware_fingerprints', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-16 16:53:23,958 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
✅ Database for auth service initialized!
🚀 Auth Service startup complete.
INFO:     127.0.0.1:54984 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:54988 - "GET /health HTTP/1.1" 200 OK
2025-07-16 16:53:53,385 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 16:53:53,387 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 16:53:53,387 INFO sqlalchemy.engine.Engine [generated in 0.00012s] ('215829',)
2025-07-16 16:53:53,392 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:45812 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:45828 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45844 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:45846 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:45860 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 16:58:13,842 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 16:58:13,843 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 16:58:13,843 INFO sqlalchemy.engine.Engine [cached since 260.5s ago] ('215829',)
2025-07-16 16:58:13,847 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33084 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33086 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33088 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33104 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33120 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:02:36,610 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:02:36,611 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:02:36,611 INFO sqlalchemy.engine.Engine [cached since 523.2s ago] ('215829',)
2025-07-16 17:02:36,615 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43678 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43694 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43708 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43712 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43724 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57932 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:57940 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57956 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:04:53,002 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:04:53,003 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:04:53,003 INFO sqlalchemy.engine.Engine [cached since 659.6s ago] ('215829',)
2025-07-16 17:04:53,004 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46702 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46704 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59112 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:59128 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:59136 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:05:45,513 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:05:45,514 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:05:45,514 INFO sqlalchemy.engine.Engine [cached since 712.1s ago] ('215829',)
2025-07-16 17:05:45,517 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56480 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56494 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56508 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56520 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56526 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:18:41,154 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:18:41,155 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:18:41,155 INFO sqlalchemy.engine.Engine [cached since 1488s ago] ('215829',)
2025-07-16 17:18:41,159 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51534 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51536 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51550 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51566 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51576 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:21:50,793 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:21:50,793 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:21:50,793 INFO sqlalchemy.engine.Engine [cached since 1677s ago] ('215829',)
2025-07-16 17:21:50,795 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36188 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36198 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36212 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36222 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36226 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:22:52,551 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:22:52,552 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:22:52,552 INFO sqlalchemy.engine.Engine [cached since 1739s ago] ('215829',)
2025-07-16 17:22:52,556 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:45832 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:45848 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45858 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:45864 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39364 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 17:28:55,976 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 17:28:55,977 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 17:28:55,977 INFO sqlalchemy.engine.Engine [cached since 2103s ago] ('215829',)
2025-07-16 17:28:55,980 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:42960 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:42972 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42980 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:42982 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:42996 - "GET /api/verify HTTP/1.1" 200 OK
