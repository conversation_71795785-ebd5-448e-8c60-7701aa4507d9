2025-07-10 13:03:01,672 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-10 13:03:01,673 - INFO - 启动f1-f4服务...
2025-07-10 13:03:01,673 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 13:03:01,673 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 13:03:01,673 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 13:03:01,673 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 13:03:01,674 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 13:03:01,674 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 13:03:01,674 - INFO - 启动 f1_listener...
2025-07-10 13:03:01,679 - INFO - Redis连接成功
2025-07-10 13:03:01,688 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:01,867 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:01,868 - INFO - ✅ f1监听器服务启动成功
2025-07-10 13:03:01,869 - INFO - f1_listener 启动成功
2025-07-10 13:03:01,869 - INFO - 启动 f2_push_writer...
2025-07-10 13:03:01,872 - INFO - Redis连接成功
2025-07-10 13:03:01,877 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 13:03:01,881 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 13:03:01,882 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:01,990 - INFO - 📡 开始监听频道: push_job
2025-07-10 13:03:01,991 - INFO - 📡 开始监听频道: partition_check
2025-07-10 13:03:01,993 - INFO - 📡 开始监听频道: sync_trigger
2025-07-10 13:03:02,105 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:02,105 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 13:03:02,105 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 13:03:02,106 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 13:03:02,106 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 13:03:02,106 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 13:03:02,107 - INFO - f2_push_writer 启动成功
2025-07-10 13:03:02,107 - INFO - 启动 f3_data_puller...
2025-07-10 13:03:02,414 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:02,415 - INFO - Redis连接成功
2025-07-10 13:03:02,415 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 13:03:02,415 - INFO - f3_data_puller 启动成功
2025-07-10 13:03:02,415 - INFO - 启动 f4_operation_handler...
2025-07-10 13:03:02,415 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 13:03:02,415 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 46618 秒...
2025-07-10 13:03:02,418 - INFO - Redis连接成功
2025-07-10 13:03:02,426 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:02,645 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:02,645 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 13:03:02,645 - INFO - f4_operation_handler 启动成功
2025-07-10 13:03:02,645 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-10 13:03:49,708 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-10 13:03:49,709 - INFO - 启动f1-f4服务...
2025-07-10 13:03:49,709 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 13:03:49,709 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 13:03:49,709 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 13:03:49,709 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 13:03:49,710 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 13:03:49,710 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 13:03:49,710 - INFO - 启动 f1_listener...
2025-07-10 13:03:49,714 - INFO - Redis连接成功
2025-07-10 13:03:49,724 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:49,928 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:49,928 - INFO - ✅ f1监听器服务启动成功
2025-07-10 13:03:49,930 - INFO - f1_listener 启动成功
2025-07-10 13:03:49,930 - INFO - 启动 f2_push_writer...
2025-07-10 13:03:49,933 - INFO - Redis连接成功
2025-07-10 13:03:49,936 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 13:03:49,940 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 13:03:49,942 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:50,059 - INFO - 📡 开始监听频道: push_job
2025-07-10 13:03:50,061 - INFO - 📡 开始监听频道: partition_check
2025-07-10 13:03:50,062 - INFO - 📡 开始监听频道: sync_trigger
2025-07-10 13:03:50,197 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:50,197 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 13:03:50,197 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 13:03:50,197 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 13:03:50,197 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 13:03:50,198 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 13:03:50,198 - INFO - f2_push_writer 启动成功
2025-07-10 13:03:50,198 - INFO - 启动 f3_data_puller...
2025-07-10 13:03:50,488 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:50,489 - INFO - Redis连接成功
2025-07-10 13:03:50,489 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 13:03:50,489 - INFO - f3_data_puller 启动成功
2025-07-10 13:03:50,489 - INFO - 启动 f4_operation_handler...
2025-07-10 13:03:50,489 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 13:03:50,489 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 46570 秒...
2025-07-10 13:03:50,490 - INFO - Redis连接成功
2025-07-10 13:03:50,502 - INFO - ✅ MongoDB连接成功
2025-07-10 13:03:50,755 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:03:50,755 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 13:03:50,755 - INFO - f4_operation_handler 启动成功
2025-07-10 13:03:50,755 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-10 13:09:37,926 - INFO - 🔄 正在停止所有f1-f4服务...
2025-07-10 13:09:37,927 - INFO - 🔄 停止 f4_operation_handler...
2025-07-10 13:09:37,929 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:09:37,930 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:09:37,930 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:09:37,931 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 13:09:37,931 - INFO - ✅ f4_operation_handler 已停止
2025-07-10 13:09:37,931 - INFO - 🔄 停止 f3_data_puller...
2025-07-10 13:09:37,932 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:09:37,933 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:09:37,933 - INFO - 🔌 f3数据拉取服务已停止
2025-07-10 13:09:37,933 - INFO - ✅ f3_data_puller 已停止
2025-07-10 13:09:37,933 - INFO - 🔄 停止 f2_push_writer...
2025-07-10 13:09:37,937 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 13:09:37,937 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 13:09:37,938 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 13:09:37,938 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 13:09:37,941 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:09:37,942 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:09:37,948 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:09:37,948 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 13:09:37,948 - INFO - ✅ f2_push_writer 已停止
2025-07-10 13:09:37,949 - INFO - 🔄 停止 f1_listener...
2025-07-10 13:09:37,951 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:09:37,951 - INFO - 监听任务被取消
2025-07-10 13:09:37,951 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:09:37,951 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:09:37,951 - INFO - 🔌 f1监听器服务已停止
2025-07-10 13:09:37,951 - INFO - ✅ f1_listener 已停止
2025-07-10 13:57:11,433 - ERROR - Server5 HTTP服务启动失败: No module named 'app.routers.entries_router'
2025-07-10 14:07:29,335 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:07:29,335 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:08:40,176 - INFO - 🌐 HTTP-only 模式: 无微服务需要停止
2025-07-10 14:09:39,699 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:09:39,699 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:09:57,872 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:57,872 - INFO - ▶️ [get_timeprotab] 开始从DB拉数据
2025-07-10 14:09:57,881 - INFO - ✅ [get_timeprotab] DB 拉数据完成，耗时 0.009s 共 31 条
2025-07-10 14:09:57,881 - INFO - 📋 获取timeprotab成功: 31条记录
2025-07-10 14:09:57,886 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:58,013 - ERROR - ❌ 获取可用月份失败: 数据库连接池未初始化
2025-07-10 14:09:58,107 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:58,107 - INFO - 🔗 数据库连接池已创建
2025-07-10 14:09:58,107 - INFO - ▶️ [get_entries] 开始从DB拉数据
2025-07-10 14:09:58,113 - INFO - ✅ [get_entries] DB 拉数据完成，耗时 0.006s 共 1 条
2025-07-10 14:09:58,113 - INFO - 📋 获取entries成功: 1条记录
2025-07-10 14:10:21,780 - INFO - ▶️ [get_entries] 开始从DB拉数据
2025-07-10 14:10:21,784 - INFO - ✅ [get_entries] DB 拉数据完成，耗时 0.005s 共 22 条
2025-07-10 14:10:21,785 - INFO - 📋 获取entries成功: 22条记录
2025-07-10 14:10:23,704 - INFO - ▶️ [get_entries] 开始从DB拉数据
2025-07-10 14:10:23,706 - INFO - ✅ [get_entries] DB 拉数据完成，耗时 0.002s 共 1 条
2025-07-10 14:10:23,706 - INFO - 📋 获取entries成功: 1条记录
2025-07-10 14:22:14,710 - INFO - 🌐 HTTP-only 模式: 无微服务需要停止
2025-07-10 14:26:49,225 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:26:49,225 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:26:49,478 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:26:49,478 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:27:02,123 - ERROR - 查询执行失败: 
            SELECT 
                id,
                employee_id,
                日付,
          ... 错误: 列"ｶﾞﾚﾝﾀﾞ"は存在しません
HINT:  列"timeprotab.ｶﾚﾝﾀﾞ"を参照しようとしていたようです。
2025-07-10 14:27:02,124 - ERROR - ❌ 获取timeprotab数据失败: 列"ｶﾞﾚﾝﾀﾞ"は存在しません
HINT:  列"timeprotab.ｶﾚﾝﾀﾞ"を参照しようとしていたようです。
2025-07-10 14:27:02,510 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:27:04,349 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 14:27:08,270 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-10 14:27:09,818 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 14:27:55,994 - ERROR - 查询执行失败: 
            SELECT 
                id,
                employee_id,
                日付,
          ... 错误: 列"ｶﾞﾚﾝﾀﾞ"は存在しません
HINT:  列"timeprotab.ｶﾚﾝﾀﾞ"を参照しようとしていたようです。
2025-07-10 14:27:55,996 - ERROR - ❌ 获取timeprotab数据失败: 列"ｶﾞﾚﾝﾀﾞ"は存在しません
HINT:  列"timeprotab.ｶﾚﾝﾀﾞ"を参照しようとしていたようです。
2025-07-10 14:27:57,555 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 14:39:14,931 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:39:14,935 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:41:59,595 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:41:59,595 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:41:59,830 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:41:59,830 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:42:15,475 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:42:15,485 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:42:15,491 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 14:42:15,625 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 14:51:45,296 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:52:20,185 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:52:20,189 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:55:08,575 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:55:08,575 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:55:08,823 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:55:08,823 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:55:18,700 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:55:18,715 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:55:18,722 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 14:55:20,382 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 14:58:52,495 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:58:52,499 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:10:44,134 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:10:44,134 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:10:44,356 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:10:44,356 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:11:00,224 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:11:00,233 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:11:00,239 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 15:11:00,255 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:11:00,262 - ERROR - 查询执行失败: 
            SELECT DISTINCT department
            FROM entries
            WHERE employee_id = $1
... 错误: SELECT DISTINCTではORDER BYの式はSELECTリスト内になければなりません
2025-07-10 15:11:00,263 - ERROR - ❌ 获取员工部门信息失败: SELECT DISTINCTではORDER BYの式はSELECTリスト内になければなりません
2025-07-10 15:13:29,032 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:13:29,033 - ERROR - 查询执行失败: 
            SELECT DISTINCT department
            FROM entries
            WHERE employee_id = $1
... 错误: SELECT DISTINCTではORDER BYの式はSELECTリスト内になければなりません
2025-07-10 15:13:29,034 - ERROR - ❌ 获取员工部门信息失败: SELECT DISTINCTではORDER BYの式はSELECTリスト内になければなりません
2025-07-10 15:13:35,498 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:13:35,503 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:13:39,846 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:13:39,846 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:13:40,101 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:13:40,101 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:13:43,409 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:13:43,413 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:13:54,098 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:13:54,099 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:13:54,309 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:13:54,309 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:14:06,665 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:14:06,752 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:14:19,312 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:14:52,216 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:16:05,623 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:16:05,627 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:16:07,415 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:16:07,415 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:16:07,642 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:16:07,642 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:16:56,596 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:16:57,100 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:16:57,107 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-10 15:16:57,609 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:16:57,681 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:16:59,256 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 15:17:43,348 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:17:43,352 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:18:10,466 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:18:10,466 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:18:10,673 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:18:10,673 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:18:18,351 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:18:18,360 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:18:18,367 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 15:18:20,641 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:18:20,650 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:18:58,971 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:18:58,975 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:19:01,835 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:19:01,835 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:19:02,167 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:19:02,167 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:19:13,911 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:19:13,920 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:19:13,927 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 15:19:13,941 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:19:14,018 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:35:51,739 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:35:51,739 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:35:54,477 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:35:54,478 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:35:54,723 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:35:54,723 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:36:15,267 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:36:15,276 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:36:15,281 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 15:36:15,285 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:36:15,362 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:37:14,040 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:37:14,041 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:37:14,263 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:37:14,263 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:37:26,844 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:37:26,852 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:37:26,857 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 15:37:26,871 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:37:26,954 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:39:00,192 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:39:00,196 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:39:02,505 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:39:02,505 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:39:02,722 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:39:02,722 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:39:14,368 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:39:14,380 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:39:14,385 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 15:39:14,391 - ERROR - ❌ 获取员工部门信息失败: name 'query' is not defined
2025-07-10 15:41:33,588 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:41:33,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:46:18,380 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:46:18,381 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:46:18,628 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:46:18,628 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:46:28,194 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:46:28,201 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:46:28,207 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 15:46:28,211 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:46:28,297 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:51:58,744 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:51:58,745 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:52:00,992 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:52:00,993 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:52:01,245 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:52:01,245 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:52:13,433 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:52:13,442 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:52:13,449 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-10 15:52:13,556 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:54:22,359 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:54:30,534 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:55:07,333 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:55:13,056 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:55:13,060 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:56:03,397 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:56:03,398 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:56:03,610 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:56:03,610 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:56:07,218 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:56:07,221 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:57:04,437 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:57:04,438 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:57:04,645 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:57:04,646 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:57:52,791 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:57:52,807 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:57:52,811 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 16:00:58,665 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:00:58,669 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:01:21,322 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:01:21,322 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:01:21,527 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:01:21,527 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:08:31,902 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:08:31,911 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 16:08:31,917 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 16:08:34,090 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:08:38,383 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:19:14,187 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:19:14,213 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 16:20:35,998 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:20:36,012 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 16:21:20,586 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:21:20,605 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 16:26:24,623 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:26:24,641 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 16:26:24,973 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:29:31,997 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:29:32,012 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 16:29:32,310 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:30:10,497 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:30:10,533 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 16:30:10,796 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:31:13,092 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:31:13,097 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:31:49,268 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:31:49,268 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:31:49,492 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:31:49,492 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:32:23,180 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:32:23,195 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 16:32:23,202 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-10 16:32:23,531 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:32:31,057 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:32:31,158 - INFO - 📊 图表数据生成完成: 21个数据点
2025-07-10 16:32:38,381 - INFO - 📊 图表数据生成完成: 21个数据点
2025-07-10 16:32:38,482 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 16:32:46,737 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-10 16:32:48,731 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-10 16:32:50,419 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:47:12,864 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:47:12,864 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:57:34,549 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:57:34,549 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:57:34,832 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:57:34,832 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:57:46,296 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 16:57:46,331 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 16:57:46,338 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 16:57:46,689 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 3
2025-07-10 16:57:53,157 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 3
2025-07-10 16:57:53,256 - INFO - 📊 双表图表数据生成完成: 30天, Entries有效数据: 21, Timeprotab有效数据: 21
2025-07-10 16:57:59,246 - INFO - 📊 双表图表数据生成完成: 30天, Entries有效数据: 21, Timeprotab有效数据: 21
2025-07-10 16:57:59,347 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 20
2025-07-10 16:58:00,522 - INFO - 📊 双表图表数据生成完成: 30天, Entries有效数据: 21, Timeprotab有效数据: 21
2025-07-10 16:58:00,617 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 20
2025-07-10 17:14:11,289 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 17:14:11,289 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 17:17:31,628 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 17:17:31,628 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 17:17:31,890 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 17:17:31,890 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 17:17:46,762 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 17:17:46,775 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 17:17:46,782 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 17:17:47,131 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:17:50,996 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:17:51,101 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:18:36,595 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:18:36,700 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 12, 不匹配: 19
2025-07-10 17:19:09,300 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 17:19:09,321 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 17:19:09,607 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:31:55,544 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 17:31:55,561 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 17:31:55,827 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:31:55,888 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:31:55,928 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:31:55,941 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:31:55,982 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:32:00,168 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-10 17:32:26,007 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:32:26,097 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 12, 不匹配: 19
2025-07-10 17:32:29,894 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:32:29,995 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:33:25,056 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 17:50:37,033 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 17:50:37,054 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-10 17:50:37,352 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:50:37,429 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:50:37,450 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:50:37,471 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:50:37,564 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 17:51:53,198 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 17:53:17,517 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-10 17:53:51,695 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 17:53:52,628 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-10 17:54:01,981 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.004秒
2025-07-10 17:54:10,072 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.012秒
2025-07-10 17:54:12,067 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-10 17:54:15,959 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.004秒
2025-07-10 17:54:17,852 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.004秒
2025-07-10 17:54:22,574 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 17:54:24,425 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-10 18:17:15,107 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:17:15,147 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 18:17:15,432 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:17:15,495 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:17:15,514 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:17:15,535 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:17:15,597 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:29:18,842 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:29:18,863 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 18:29:19,135 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:29:19,222 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:29:19,242 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:29:19,254 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:29:19,324 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:29:58,797 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-10 18:30:03,934 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 18:30:06,014 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-10 18:30:07,915 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 18:30:11,515 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:30:11,542 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.014秒
2025-07-10 18:30:11,817 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:30:11,880 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:30:11,934 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:30:12,021 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:30:12,123 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:39:37,752 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 18:39:37,753 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 18:54:07,112 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 18:54:07,113 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 18:54:07,364 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:54:07,364 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 18:54:21,589 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:54:21,603 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 18:54:21,609 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 18:54:21,949 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:54:21,975 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:54:21,992 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:54:22,056 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:54:22,095 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:16:54,815 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:16:54,832 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-11 08:16:55,413 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:16:55,479 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:16:55,498 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:16:55,518 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:16:55,600 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:24:20,524 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:24:20,541 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-11 08:24:20,836 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:24:20,878 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:24:20,898 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:24:20,938 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:24:20,979 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:25:49,727 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:25:49,742 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 08:25:50,015 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:25:50,036 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:25:50,112 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:25:50,179 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:25:50,280 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:27:11,063 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:27:11,078 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 08:27:11,338 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:11,368 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:11,439 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:27:11,502 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:11,603 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:27:42,759 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:27:42,772 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.001秒
2025-07-11 08:27:43,030 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:43,051 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:43,131 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:27:43,193 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:27:43,294 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:31:15,307 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:31:15,321 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-11 08:31:15,583 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:31:15,603 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:31:15,682 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:31:15,747 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:31:15,855 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:33:22,104 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:33:22,117 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 08:33:22,397 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:33:22,419 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:33:22,494 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:33:22,563 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:33:22,663 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:35:01,094 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:35:01,108 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 08:35:01,371 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:01,394 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:01,474 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:35:01,547 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:01,648 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:35:32,925 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:35:32,939 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 08:35:33,196 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:33,217 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:33,297 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:35:33,356 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-11 08:35:33,457 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:57:50,472 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 08:57:50,520 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-11 08:57:50,795 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 08:57:50,866 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 08:57:50,888 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 08:57:50,904 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 08:57:50,967 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 09:00:52,631 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 09:02:15,229 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-11 09:03:57,378 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.011秒
2025-07-11 09:09:07,189 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 09:09:07,206 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-11 09:09:07,501 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 09:09:07,545 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 09:09:07,572 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-11 09:09:07,604 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 09:09:07,648 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 09:09:29,692 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-11 09:13:39,499 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 09:13:39,515 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.004秒
2025-07-11 09:13:39,785 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 09:13:39,816 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 09:13:39,882 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 09:13:39,956 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 09:13:40,056 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 09:14:08,172 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.009秒
2025-07-11 10:21:30,208 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 10:21:30,231 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 10:21:30,511 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 10:21:30,589 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 10:21:30,601 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 10:21:30,623 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 10:21:30,691 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 10:25:29,056 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 10:25:31,663 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 10:25:33,297 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-11 10:25:34,393 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 11:33:59,598 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:34:07,693 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:34:48,572 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.021秒
2025-07-11 11:34:49,816 - INFO - 📊 Entries查询完成: 6条记录, 耗时: 0.016秒
2025-07-11 11:34:54,936 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.010秒
2025-07-11 11:36:31,697 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:53:37,720 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:53:37,732 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 11:53:38,615 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 11:53:38,622 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:53:38,661 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 11:53:38,665 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:53:38,976 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 11:53:38,980 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:53:38,989 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 11:53:38,993 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:54:01,852 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:54:01,862 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 11:54:02,414 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 11:54:02,432 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:54:02,469 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 11:54:02,473 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:54:02,588 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 11:54:02,592 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:54:02,688 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 11:54:02,692 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:55:43,059 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 11:55:43,064 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 11:55:43,160 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 11:55:43,165 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 11:56:02,973 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:00,172 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:00,185 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 12:01:00,839 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:00,844 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:00,854 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:00,859 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:00,872 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:00,877 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:00,889 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:00,894 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:18,473 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:18,478 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:18,577 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 12:01:18,582 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:43,105 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:43,114 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 12:01:43,379 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:43,384 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:43,426 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:43,432 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:43,481 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:43,486 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:43,570 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:43,575 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:43,671 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:43,676 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:47,396 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:47,400 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:01:47,496 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 12:01:47,502 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:52,718 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:01:52,723 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:01:52,818 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:01:52,823 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:03:52,337 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:03:55,544 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:04:07,479 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 12:04:09,642 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-11 12:10:35,638 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:10:35,650 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 12:10:35,927 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:10:35,931 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:10:35,971 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:10:35,975 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:10:35,989 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:10:35,993 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:10:36,029 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:10:36,033 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:10:36,072 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:10:36,079 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:12:35,503 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:12:35,512 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 12:12:35,790 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:12:35,794 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:12:35,821 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:12:35,826 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:12:35,885 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:12:35,890 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:12:35,972 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:12:35,976 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:12:36,072 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:12:36,076 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:14:37,187 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:14:37,196 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 12:14:37,461 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:14:37,466 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:14:37,491 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:14:37,495 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:14:37,563 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:14:37,567 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 12:14:37,645 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 12:14:37,650 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 12:14:37,749 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 12:14:37,754 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:04,502 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:04,525 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.007秒
2025-07-11 13:17:04,811 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:04,815 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:04,868 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:04,872 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:04,886 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:04,890 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:04,915 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:17:04,920 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:04,969 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:17:04,976 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:10,790 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:17:10,795 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:16,161 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:16,165 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:24,418 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:24,423 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:26,221 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:26,222 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:26,225 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:26,226 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:26,230 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:17:27,125 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:27,130 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:28,271 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:28,272 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:28,274 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:28,275 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:28,283 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:17:29,880 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:17:29,884 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:29,982 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 13:17:29,986 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:34,500 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:34,505 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:38,040 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:38,041 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:38,042 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:38,043 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:17:38,045 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:17:38,742 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:38,753 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:43,252 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:17:43,256 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:17:43,352 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:17:43,357 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:17:50,158 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:18:12,582 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:18:18,158 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:18:19,881 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-11 13:18:20,785 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.009秒
2025-07-11 13:18:21,700 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 13:18:22,568 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:18:23,625 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:18:24,581 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:18:34,175 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:18:34,892 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:18:36,523 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:18:37,871 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:18:39,125 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:18:40,334 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:18:41,392 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:18:42,413 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:18:43,067 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:18:43,713 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:19:25,756 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:25,785 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:19:26,062 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:19:26,066 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:26,107 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:19:26,112 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:26,160 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:19:26,165 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:26,178 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:19:26,183 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:19:26,262 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:19:26,267 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:19:27,515 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:19:29,131 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:30,223 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:31,162 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:19:31,916 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:32,476 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:19:33,359 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:33,874 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:19:34,480 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:35,173 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:19:35,842 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:19:36,796 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:19:37,986 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:16,113 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:16,122 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:24:16,407 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:24:16,412 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:16,450 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:24:16,454 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:16,509 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:24:16,513 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:24:16,600 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:24:16,604 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:16,701 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:24:16,706 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:24:18,976 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:24:21,476 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:24:22,846 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:27:53,402 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:27:53,431 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:27:53,757 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:27:53,761 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:27:53,799 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:27:53,803 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:27:53,823 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:27:53,827 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:27:53,856 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:27:53,860 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:27:53,918 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:27:53,922 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:28:02,695 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:28:03,661 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:32:54,430 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:32:54,446 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:32:54,742 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:32:54,748 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:32:54,808 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:32:54,812 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:32:54,850 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:32:54,854 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:32:54,948 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:32:54,952 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:32:55,048 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:32:55,053 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:35:35,283 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:35:35,296 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:35:35,587 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:35:35,597 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:35:35,674 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:35:35,679 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:35:35,723 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:35:35,728 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:35:35,822 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:35:35,827 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:35:35,923 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:35:35,927 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:02,143 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:02,155 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.005秒
2025-07-11 13:41:02,434 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:41:02,438 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:02,497 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:41:02,502 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:02,526 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:41:02,531 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:02,548 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:41:02,553 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:02,599 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:41:02,606 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:06,565 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:07,950 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:09,563 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:11,024 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:41:12,071 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:12,758 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:13,181 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:15,588 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:16,272 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:41:16,656 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:41:18,360 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:19,528 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:41:20,290 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:41:21,506 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:41:23,481 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:44:53,043 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:44:53,070 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:44:53,365 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:44:53,370 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:44:53,409 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:44:53,414 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:44:53,439 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:44:53,443 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:44:53,463 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:44:53,468 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:44:53,540 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:44:53,545 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:44:57,526 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:44:58,598 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 13:44:59,819 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:45:00,705 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.012秒
2025-07-11 13:50:36,711 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:36,724 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 13:50:36,999 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:50:37,004 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:37,045 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:50:37,050 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:37,074 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:50:37,078 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:37,101 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:50:37,105 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:50:37,148 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:50:37,155 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:50:41,323 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:50:41,328 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:42,307 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:50:42,308 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:50:42,310 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:50:42,312 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:50:42,318 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:50:43,685 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:50:43,696 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:50:45,056 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:50:46,223 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:50:48,657 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:56:24,329 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:24,341 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.005秒
2025-07-11 13:56:24,617 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:24,621 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:24,676 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:24,682 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:24,714 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:24,718 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:24,740 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:56:24,745 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:56:24,780 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:56:24,787 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:56:26,936 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:26,937 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:26,938 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:26,939 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:26,943 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:56:27,739 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:27,754 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:28,311 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:28,315 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:29,036 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:29,037 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:29,039 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:29,041 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:29,048 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:56:29,707 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:29,716 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:30,291 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:56:30,295 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:56:30,822 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:30,833 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:31,387 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:31,388 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:31,389 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:31,390 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 13:56:31,392 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 13:56:32,002 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:56:32,013 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:56:33,776 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 13:56:34,642 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:56:35,249 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:57:01,624 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:01,634 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:03,035 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:03,040 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:03,856 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:03,861 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:04,307 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:04,312 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:04,784 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:04,794 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:05,576 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:05,586 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:06,159 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:06,170 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:07,016 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:07,026 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:07,720 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:07,725 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:09,751 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:09,755 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:57:11,783 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 13:57:11,792 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 13:57:13,272 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 13:57:13,276 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 13:58:27,352 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 13:58:28,484 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.012秒
2025-07-11 13:58:29,530 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:58:30,579 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:58:31,963 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 13:58:32,942 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 13:58:33,470 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-11 13:58:33,970 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:58:39,564 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:58:40,255 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:58:42,145 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:58:42,729 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:58:43,480 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 13:58:44,976 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 13:58:45,505 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 14:05:52,376 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:05:52,411 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.007秒
2025-07-11 14:05:52,710 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:05:52,714 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:05:52,748 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:05:52,752 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:05:52,766 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:05:52,770 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:05:52,811 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 14:05:52,816 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 14:05:52,849 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 14:05:52,856 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 14:05:57,741 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 14:05:58,989 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 14:05:59,690 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 14:17:14,249 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:17:14,261 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 14:17:14,551 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:17:14,555 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:17:14,598 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:17:14,604 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:17:14,622 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 14:17:14,627 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 14:17:14,658 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 14:17:14,666 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 14:17:14,696 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 14:17:14,707 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:45:53,190 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:45:53,208 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.007秒
2025-07-11 15:45:53,517 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:45:53,522 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:45:53,581 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:45:53,585 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:45:53,598 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:45:53,603 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:45:53,630 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:45:53,635 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:45:53,681 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:45:53,691 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:45:56,168 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 15:45:56,951 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 15:45:57,409 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 15:45:58,566 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:45:59,133 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:45:59,554 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 15:46:00,784 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:00,785 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:00,786 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:00,787 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:00,790 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 15:46:01,328 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:46:01,333 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:46:01,887 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:46:01,891 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:46:02,513 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:02,514 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:02,515 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:02,516 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 15:46:02,520 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 15:46:03,290 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:46:03,301 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:46:03,675 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:46:03,679 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:46:04,764 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:46:04,776 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:47:27,967 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:47:27,979 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 15:47:28,264 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:47:28,274 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:47:28,344 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:47:28,349 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:47:28,408 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:47:28,412 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:47:28,522 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:47:28,526 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:47:28,623 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:47:28,628 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:56:15,416 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:56:15,430 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.007秒
2025-07-11 15:56:15,706 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:56:15,711 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:56:15,754 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:56:15,759 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:56:15,773 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 15:56:15,777 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 15:56:15,808 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:56:15,812 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 15:56:15,855 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 15:56:15,864 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:03:43,939 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:03:43,951 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.005秒
2025-07-11 16:03:44,225 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:03:44,229 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:03:44,272 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:03:44,277 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:03:44,301 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:03:44,305 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:03:44,327 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:03:44,332 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:03:44,376 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:03:44,386 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:04:31,264 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:04:31,274 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:04:31,550 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:04:31,556 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:04:31,586 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:04:31,590 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:04:31,645 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:04:31,649 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:04:31,744 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:04:31,748 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:04:31,852 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:04:31,856 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:06:10,882 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:06:10,893 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:06:11,168 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:06:11,174 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:06:11,205 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:06:11,209 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:06:11,272 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:06:11,276 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:06:11,364 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:06:11,369 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:06:11,466 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:06:11,471 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:10:37,932 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:10:37,942 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.004秒
2025-07-11 16:10:38,231 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:10:38,239 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:10:38,276 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:10:38,281 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:10:38,335 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:10:38,339 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:10:38,431 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:10:38,435 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:10:38,533 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:10:38,538 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:27:44,627 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:27:44,640 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.005秒
2025-07-11 16:27:44,914 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:27:44,919 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:27:44,961 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:27:44,966 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:27:44,986 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:27:44,991 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:27:45,028 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:27:45,033 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:27:45,065 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:27:45,075 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:11,271 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:11,271 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:11,273 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:11,273 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:11,276 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:28:12,356 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:12,360 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:13,634 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:13,635 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:13,636 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:13,637 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:13,640 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:28:14,662 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:14,666 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:16,176 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:16,181 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:17,142 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:17,148 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:18,053 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:18,054 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:18,055 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:18,056 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:18,058 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:28:18,932 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:18,936 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:19,843 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:19,848 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:21,211 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:21,219 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:22,886 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:22,897 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:23,562 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:23,573 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:23,933 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:23,941 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:24,488 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:24,492 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:25,352 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:25,356 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:27,352 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:27,357 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:29,225 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:29,230 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:29,325 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 16:28:29,329 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:32,615 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:32,626 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:32,717 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:32,727 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:37,198 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:37,203 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:38,906 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:38,910 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:39,568 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:39,569 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:39,570 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:39,571 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:39,573 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:28:40,346 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:40,350 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:40,881 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:40,886 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:41,684 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:41,695 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:42,549 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:42,550 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:42,551 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:42,552 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:28:42,555 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:28:43,279 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:43,283 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:28:44,185 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:28:44,190 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:28:45,308 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:28:45,312 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:12,641 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:12,652 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 16:52:12,938 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:12,943 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:12,991 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:12,995 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:13,026 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:13,030 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:13,049 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:13,054 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:13,093 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:13,103 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:24,942 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:24,957 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:24,962 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:25,050 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:52:27,461 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:27,471 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:27,476 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:27,568 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:52:29,215 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:29,227 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:29,227 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:29,228 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:29,229 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:29,232 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:29,321 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 16:52:30,836 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:30,847 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:30,851 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:30,944 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:52:32,249 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:32,259 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:32,263 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:32,364 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.011秒
2025-07-11 16:52:33,991 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:34,001 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:34,005 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:34,098 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:52:35,457 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:35,463 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:35,463 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:35,465 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:35,465 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:35,468 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:35,562 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-11 16:52:36,353 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:36,363 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:36,367 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:36,460 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 16:52:37,575 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:37,586 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:37,591 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:37,684 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:52:38,779 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:38,789 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:38,794 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:38,887 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 16:52:39,920 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:39,926 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:39,927 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:39,928 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:39,929 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:39,931 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:40,025 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 16:52:40,578 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:40,588 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:40,592 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:40,685 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 16:52:48,881 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:48,891 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:48,896 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:48,988 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:52:49,814 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:49,820 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:49,821 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:49,822 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:49,823 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:52:49,825 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:52:49,920 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 16:52:50,591 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:50,601 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:50,605 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:50,698 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 16:52:57,820 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:57,832 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:52:57,837 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:52:57,928 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:52:59,164 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:59,176 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:52:59,180 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:52:59,273 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:53:00,110 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:00,120 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:00,124 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:00,217 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:53:27,852 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:27,863 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:27,868 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:27,962 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:53:28,516 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:28,526 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:28,531 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:28,624 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:53:29,023 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:29,033 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:29,037 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:29,131 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:53:36,869 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:36,873 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:37,749 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:37,753 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:39,236 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:39,249 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:40,033 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:53:40,034 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:53:40,036 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:53:40,037 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 16:53:40,039 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 16:53:40,928 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:40,940 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:41,434 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:41,438 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:42,996 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 16:53:43,001 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:43,096 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 19, 匹配: 30, 不匹配: 0
2025-07-11 16:53:43,100 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:46,429 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:46,433 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:46,529 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:46,533 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:48,864 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:48,869 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:48,964 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 16:53:48,969 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:50,942 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:50,946 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:53:51,932 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:53:51,942 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:53:52,492 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:53:52,497 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:55:17,598 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:55:17,608 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:55:17,613 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:55:17,706 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 16:55:18,425 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:55:18,454 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 16:55:18,464 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 16:55:18,542 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 16:55:19,243 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:55:19,259 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 16:55:19,264 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 16:55:19,356 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:07:51,287 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:07:51,298 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-11 17:07:51,600 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:07:51,604 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:07:51,667 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:07:51,671 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:07:51,704 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:07:51,708 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:07:51,723 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:07:51,727 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:07:51,769 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:07:51,781 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:07:59,800 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:07:59,818 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:07:59,818 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:07:59,820 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:07:59,821 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:07:59,823 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:07:59,905 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 17:08:00,033 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:00,037 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:01,363 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:01,373 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:01,378 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:01,470 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 17:08:01,589 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:01,594 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:02,216 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:02,227 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:02,232 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:02,324 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 17:08:02,445 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:08:02,450 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:06,198 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:06,208 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:08:06,212 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:06,304 - INFO - 📊 Entries查询完成: 6条记录, 耗时: 0.002秒
2025-07-11 17:08:06,424 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 19, 匹配: 30, 不匹配: 0
2025-07-11 17:08:06,428 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:06,840 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:06,850 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:06,854 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:06,955 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.010秒
2025-07-11 17:08:07,066 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:07,070 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:07,783 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:07,788 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:07,789 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:07,790 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:07,791 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:07,794 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:07,888 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 17:08:08,002 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:08,006 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:08,549 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:08,559 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:08,563 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:08,656 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:08:08,773 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:08,777 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:09,360 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:09,370 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:09,375 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:09,468 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 17:08:09,586 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:08:09,590 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:10,720 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:10,730 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:10,734 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:10,828 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:08:10,946 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:10,952 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:11,774 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:11,788 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:11,789 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:11,790 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:11,791 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:11,794 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:11,884 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-11 17:08:12,002 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:12,007 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:12,843 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:12,858 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:12,863 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:12,955 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:08:13,074 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:13,078 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:13,692 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:13,703 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:13,707 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:13,800 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 17:08:13,919 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:08:13,924 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:15,074 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:15,084 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:15,088 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:15,181 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:08:15,300 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:15,304 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:17,267 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:17,272 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:17,273 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:17,274 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:17,275 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:17,278 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:17,372 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-11 17:08:17,493 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:17,505 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:18,679 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:18,690 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:18,695 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:18,787 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:08:18,906 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:18,911 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:56,318 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:56,318 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:56,320 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:56,320 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-11 17:08:56,323 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:08:56,533 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:56,538 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:57,231 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:08:57,235 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:08:57,448 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:57,454 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:08:59,887 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:08:59,891 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:00,110 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:09:00,120 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:00,989 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:09:00,993 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:01,204 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:01,209 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:03,877 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:04,665 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:05,226 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:09:05,947 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:06,431 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:06,983 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:07,691 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:09,739 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:09,749 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:09,754 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:09,847 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 17:09:09,965 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:09:09,970 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:10,263 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:10,273 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:09:10,278 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:10,371 - INFO - 📊 Entries查询完成: 6条记录, 耗时: 0.002秒
2025-07-11 17:09:10,489 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 19, 匹配: 30, 不匹配: 0
2025-07-11 17:09:10,493 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:11,123 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:11,133 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 19, 匹配: 30, 不匹配: 0
2025-07-11 17:09:11,137 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:11,231 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.003秒
2025-07-11 17:09:11,345 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2503"は存在しません
2025-07-11 17:09:11,345 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2503"は存在しません
2025-07-11 17:09:11,347 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2503"は存在しません
2025-07-11 17:09:11,347 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2503"は存在しません
2025-07-11 17:09:11,350 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-11 17:09:12,570 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:12,599 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:09:12,610 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:12,686 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:09:12,831 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:12,835 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:14,806 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:14,817 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:09:14,821 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:14,914 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:09:15,032 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:15,036 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:15,399 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:15,409 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:09:15,413 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:15,507 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-11 17:09:15,624 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:15,628 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:16,208 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:16,218 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:16,222 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:09:16,315 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-11 17:09:16,434 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-11 17:09:16,439 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:16,595 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:16,608 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:09:16,612 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:09:16,704 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:09:16,823 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:09:16,827 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:11:56,981 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:11:56,987 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-11 17:11:57,268 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:11:57,272 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:11:57,299 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:11:57,303 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:11:57,371 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:11:57,375 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-11 17:11:57,454 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-11 17:11:57,458 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-11 17:11:57,555 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-11 17:11:57,560 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-14 16:39:58,198 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-14 16:39:58,198 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-14 16:39:58,393 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 16:39:58,393 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-14 16:40:14,822 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:40:14,827 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-14 16:40:14,834 - INFO - 📊 Entries查询完成: 25条记录, 耗时: 0.006秒
2025-07-14 16:40:15,185 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:40:15,190 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:40:15,222 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:40:15,227 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:40:15,262 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:40:15,267 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:40:15,295 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-14 16:40:15,301 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-14 16:40:15,331 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-14 16:40:15,341 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-14 16:53:54,785 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-14 16:53:54,785 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-14 16:53:56,840 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-14 16:53:56,840 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-14 16:53:57,048 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 16:53:57,049 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-14 16:54:13,221 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:54:13,226 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-14 16:54:13,234 - INFO - 📊 Entries查询完成: 25条记录, 耗时: 0.008秒
2025-07-14 16:54:13,578 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:54:13,583 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:54:13,636 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:54:13,641 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:54:13,651 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-14 16:54:13,655 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-14 16:54:13,683 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-14 16:54:13,688 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-14 16:54:13,727 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-14 16:54:13,736 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-14 16:57:47,252 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-14 16:57:47,256 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-14 16:57:52,366 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-14 16:57:52,366 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-14 16:57:52,599 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 16:57:52,599 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-14 17:25:36,197 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-14 17:25:36,197 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-14 17:25:38,627 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-14 17:25:38,627 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-14 17:25:38,830 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 17:25:38,830 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-14 17:25:59,149 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-14 17:25:59,151 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': None, 'item': None, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 14, 17, 25, 59, 151264)}
2025-07-14 17:25:59,151 - ERROR - ❌ 客户端数据写入失败: 'NoneType' object has no attribute 'acquire'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/client_entries_gateway.py", line 97, in create_client_entry
    conn = await imdb_client.pool.acquire()
                 ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'acquire'
2025-07-14 17:33:13,374 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-14 17:33:13,375 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-14 17:34:08,312 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-14 17:34:08,312 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-14 17:34:08,569 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 17:34:08,569 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-14 17:34:53,526 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-14 17:34:53,528 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-14 17:34:53,529 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '', 'item': '', 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 14, 17, 34, 53, 529593)}
2025-07-14 17:34:53,532 - ERROR - ❌ 客户端数据写入失败: invalid input for query argument $9: '' ('str' object cannot be interpreted as an integer)
Traceback (most recent call last):
  File "asyncpg/protocol/prepared_stmt.pyx", line 175, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
  File "asyncpg/protocol/codecs/base.pyx", line 227, in asyncpg.protocol.protocol.Codec.encode
  File "asyncpg/protocol/codecs/base.pyx", line 129, in asyncpg.protocol.protocol.Codec.encode_scalar
  File "asyncpg/pgproto/./codecs/int.pyx", line 54, in asyncpg.pgproto.pgproto.int4_encode
TypeError: 'str' object cannot be interpreted as an integer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/client_entries_gateway.py", line 101, in create_client_entry
    result = await conn.fetchrow("""
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 748, in fetchrow
    data = await self._execute(
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 1864, in _execute
    result, _ = await self.__execute(
                ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 1961, in __execute
    result, stmt = await self._do_execute(
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 2024, in _do_execute
    result = await executor(stmt, None)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 185, in bind_execute
  File "asyncpg/protocol/prepared_stmt.pyx", line 204, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
asyncpg.exceptions.DataError: invalid input for query argument $9: '' ('str' object cannot be interpreted as an integer)
2025-07-14 17:40:28,265 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-14 17:40:28,265 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 07:34:36,980 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 07:34:36,980 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 07:34:37,206 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 07:34:37,206 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 07:50:36,242 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 07:50:36,242 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 08:01:20,625 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 08:01:20,626 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 08:01:20,955 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:01:20,955 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 08:01:58,485 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:01:58,505 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 08:01:58,514 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.007秒
2025-07-15 08:01:58,848 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:01:58,852 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:01:58,878 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:01:58,882 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:01:58,910 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:01:58,914 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:01:58,951 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:01:58,956 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:01:58,990 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:01:58,999 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:06:04,428 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:06:04,429 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 08:06:04,431 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': 'test', 'number': '1', 'factory_number': 'test', 'project_number': 'test', 'unit_number': 'test', 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 8, 6, 4, 431153)}
2025-07-15 08:06:04,440 - INFO - ✅ 数据已插入到entries表: entry_id=145126
2025-07-15 08:06:04,444 - INFO - ✅ 触发器已创建队列项: queue_id=94
2025-07-15 08:06:53,143 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:06:53,144 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 08:06:53,144 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': 'TEST_MODEL', 'number': 'TEST001', 'factory_number': 'FACTORY001', 'project_number': 'PROJECT001', 'unit_number': 'UNIT001', 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 8, 6, 53, 144191)}
2025-07-15 08:06:53,154 - INFO - ✅ 数据已插入到entries表: entry_id=145127
2025-07-15 08:06:53,157 - INFO - ✅ 触发器已创建队列项: queue_id=95
2025-07-15 08:08:04,584 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:08:04,602 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:08:04,607 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:08:04,694 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-15 08:08:04,828 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-15 08:08:04,832 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:08:05,144 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:08:05,158 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:08:05,163 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:08:05,253 - INFO - 📊 Entries查询完成: 30条记录, 耗时: 0.003秒
2025-07-15 08:08:05,374 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:08:05,381 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:08:40,724 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 08:08:40,727 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 08:08:48,590 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 08:08:48,590 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 08:08:48,803 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:08:48,803 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 08:09:08,285 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:09:08,290 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 08:09:08,296 - INFO - 📊 Entries查询完成: 30条记录, 耗时: 0.006秒
2025-07-15 08:09:08,627 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:09:08,631 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:09:08,681 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:09:08,685 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:09:08,701 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:09:08,706 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:09:08,733 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:09:08,738 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:09:08,794 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:09:08,803 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:09:56,868 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:09:56,870 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 08:09:56,871 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': 'test', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 8, 9, 56, 871624)}
2025-07-15 08:09:56,881 - INFO - ✅ 数据已插入到entries表: entry_id=145128
2025-07-15 08:09:56,884 - INFO - ✅ 触发器已创建队列项: queue_id=96
2025-07-15 08:09:56,991 - INFO - 📊 Entries查询完成: 31条记录, 耗时: 0.003秒
2025-07-15 08:27:08,720 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:27:08,732 - INFO - 📊 Entries查询完成: 31条记录, 耗时: 0.006秒
2025-07-15 08:27:09,027 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:27:09,032 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:27:09,059 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:27:09,064 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:27:09,078 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:27:09,083 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:27:09,131 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:27:09,135 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:27:09,154 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:27:09,162 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:43:42,026 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:43:42,038 - INFO - 📊 Entries查询完成: 31条记录, 耗时: 0.006秒
2025-07-15 08:43:42,316 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:43:42,320 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:43:42,365 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:43:42,369 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:43:42,402 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 08:43:42,406 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 08:43:42,422 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:43:42,427 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:43:42,465 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 08:43:42,474 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 08:48:01,914 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:48:01,914 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 08:48:01,915 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': 'TEST_DELETE', 'number': 'TEST001', 'factory_number': 'FACTORY001', 'project_number': 'PROJECT001', 'unit_number': 'UNIT001', 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 8, 48, 1, 915006)}
2025-07-15 08:48:01,926 - INFO - ✅ 数据已插入到entries表: entry_id=145129
2025-07-15 08:48:01,930 - INFO - ✅ 触发器已创建队列项: queue_id=97
2025-07-15 08:48:04,273 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:48:04,273 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 08:48:04,273 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': 'TEST_COMPLETE_DELETE', 'number': 'TEST002', 'factory_number': 'FACTORY002', 'project_number': 'PROJECT002', 'unit_number': 'UNIT002', 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 8, 48, 4, 273283)}
2025-07-15 08:48:04,286 - INFO - ✅ 数据已插入到entries表: entry_id=145130
2025-07-15 08:48:04,290 - INFO - ✅ 触发器已创建队列项: queue_id=98
2025-07-15 08:48:41,044 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 08:48:41,047 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 08:58:20,551 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 08:58:20,552 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 08:58:20,776 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:58:20,776 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 09:38:05,412 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 09:38:05,412 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 09:38:12,246 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 09:38:12,246 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 09:38:12,453 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 09:38:12,454 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 09:39:09,804 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:39:09,813 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 09:39:09,821 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.007秒
2025-07-15 09:39:10,165 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:39:10,169 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:39:10,194 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:39:10,199 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:39:10,228 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:39:10,232 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:39:10,266 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:39:10,270 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:39:10,308 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:39:10,318 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:39:44,806 - INFO - 🗑️ 收到删除请求: entry_id=603643
2025-07-15 09:41:48,406 - INFO - 📊 Entries查询完成: 100条记录, 耗时: 0.011秒
2025-07-15 09:42:00,424 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.008秒
2025-07-15 09:42:00,433 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.006秒
2025-07-15 09:42:00,442 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.006秒
2025-07-15 09:42:03,460 - INFO - 📊 Entries查询完成: 3条记录, 耗时: 0.006秒
2025-07-15 09:42:16,557 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-15 09:42:16,565 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.006秒
2025-07-15 09:42:16,577 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.011秒
2025-07-15 09:42:16,579 - INFO - 🗑️ 收到删除请求: entry_id=145131
2025-07-15 09:42:16,650 - INFO - ✅ HTTP-only模式删除成功: entry_id=145131
2025-07-15 09:43:27,793 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:43:27,800 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 09:43:28,102 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:43:28,107 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:43:28,136 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:43:28,140 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:43:28,189 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:43:28,194 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:43:28,207 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:43:28,212 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:43:28,290 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:43:28,294 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:46:34,606 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:46:34,615 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.004秒
2025-07-15 09:46:34,905 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:46:34,910 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:46:34,935 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:46:34,939 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:46:34,990 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:46:34,994 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:46:35,008 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:46:35,012 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:46:35,091 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:46:35,095 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:47:05,594 - INFO - 🗑️ 收到删除请求: entry_id=603642
2025-07-15 09:51:50,352 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.015秒
2025-07-15 09:51:50,355 - INFO - 🗑️ 收到删除请求: entry_id=603643
2025-07-15 09:52:01,400 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 09:52:01,404 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 10:01:02,088 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 10:01:02,088 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 10:01:02,327 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:01:02,327 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 10:01:15,056 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:01:15,062 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 10:01:15,069 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.006秒
2025-07-15 10:01:15,406 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:01:15,410 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:01:15,456 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:01:15,460 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:01:15,470 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:01:15,476 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:01:15,511 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:01:15,516 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:01:15,551 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:01:15,562 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:01:39,937 - INFO - 🗑️ 收到删除请求: entry_id=603639
2025-07-15 10:01:39,956 - INFO - 🔍 通过internal_id=603639未找到记录，尝试通过external_id查找
2025-07-15 10:01:39,972 - INFO - 📋 找到记录: internal_id=145155, external_id=603639
2025-07-15 10:01:40,009 - INFO - ✅ HTTP-only模式删除成功: internal_id=145155, external_id=603639
2025-07-15 10:11:30,983 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:11:30,994 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.006秒
2025-07-15 10:11:31,301 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:11:31,306 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:11:31,332 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:11:31,337 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:11:31,351 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:11:31,356 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:11:31,403 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:11:31,407 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:11:31,437 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:11:31,446 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:11:44,540 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:11:44,541 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:11:44,542 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 4.0, 'model': 'test', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 4, 'item': 4, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 11, 44, 542847)}
2025-07-15 10:11:44,552 - INFO - ✅ 数据已插入到entries表: entry_id=145164
2025-07-15 10:11:44,555 - INFO - ✅ 触发器已创建队列项: queue_id=114
2025-07-15 10:11:44,662 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 10:14:37,919 - INFO - 🗑️ 收到删除请求: entry_id=603639
2025-07-15 10:14:37,939 - INFO - 🔍 通过internal_id=603639未找到记录，尝试通过external_id查找
2025-07-15 10:14:37,959 - INFO - 🗑️ 收到删除请求: entry_id=999999
2025-07-15 10:14:37,974 - INFO - 🔍 通过internal_id=999999未找到记录，尝试通过external_id查找
2025-07-15 10:15:25,351 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:15:25,359 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 10:15:25,654 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:15:25,658 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:15:25,684 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:15:25,688 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:15:25,750 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:15:25,755 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:15:25,843 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:15:25,847 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:15:25,943 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:15:25,947 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:15:40,522 - INFO - 🗑️ 收到删除请求: entry_id=603657
2025-07-15 10:15:40,541 - INFO - 🔍 通过internal_id=603657未找到记录，尝试通过external_id查找
2025-07-15 10:15:40,567 - INFO - 📋 找到记录: internal_id=145164, external_id=603657
2025-07-15 10:15:40,605 - INFO - ✅ HTTP-only模式删除成功: internal_id=145164, external_id=603657
2025-07-15 10:16:41,169 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:16:41,169 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:16:41,169 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 5.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 5, 'item': 5, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 16, 41, 169603)}
2025-07-15 10:16:41,181 - INFO - ✅ 数据已插入到entries表: entry_id=145165
2025-07-15 10:16:41,184 - INFO - ✅ 触发器已创建队列项: queue_id=117
2025-07-15 10:16:41,298 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.010秒
2025-07-15 10:28:40,033 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:28:40,044 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.006秒
2025-07-15 10:28:40,348 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:28:40,352 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:28:40,386 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:28:40,390 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:28:40,403 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:28:40,408 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:28:40,450 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:28:40,455 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:28:40,486 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:28:40,495 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:28:50,662 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:28:50,662 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:28:50,662 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 2.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 2, 'item': 2, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 28, 50, 662569)}
2025-07-15 10:28:50,671 - INFO - ✅ 数据已插入到entries表: entry_id=145166
2025-07-15 10:28:50,675 - INFO - ✅ 触发器已创建队列项: queue_id=118
2025-07-15 10:28:50,781 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 10:29:07,080 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:29:07,097 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 10:29:07,396 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:29:07,401 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:29:07,449 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:29:07,454 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:29:07,477 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:29:07,482 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:29:07,495 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:29:07,500 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:29:07,575 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:29:07,580 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:33:27,305 - INFO - 🗑️ 收到删除请求: entry_id=603659
2025-07-15 10:33:27,325 - INFO - 🔍 通过internal_id=603659未找到记录，尝试通过external_id查找
2025-07-15 10:34:13,417 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 10:34:13,420 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 10:34:19,025 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 10:34:19,025 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 10:34:19,231 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:34:19,231 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 10:42:18,129 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:18,134 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 10:42:18,141 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.006秒
2025-07-15 10:42:18,424 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:42:18,429 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:18,465 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:42:18,470 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:18,485 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:42:18,489 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:18,527 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:42:18,531 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:42:18,566 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:42:18,575 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:42:35,137 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:42:35,140 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:42:35,141 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.6, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 42, 35, 141541)}
2025-07-15 10:42:35,152 - INFO - ✅ 数据已插入到entries表: entry_id=145167
2025-07-15 10:42:35,156 - INFO - ✅ 触发器已创建队列项: queue_id=121
2025-07-15 10:42:35,264 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 10:42:43,280 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:43,300 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:42:43,305 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:42:43,389 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 10:42:43,517 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:42:43,521 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:42:52,081 - INFO - 🗑️ 收到删除请求: entry_id=603660
2025-07-15 10:42:52,100 - INFO - 🔍 通过internal_id=603660未找到记录，尝试通过external_id查找
2025-07-15 10:42:52,117 - INFO - 📋 找到记录: internal_id=145167, external_id=603660
2025-07-15 10:42:52,154 - INFO - ✅ HTTP-only模式删除成功: internal_id=145167, external_id=603660
2025-07-15 10:43:12,120 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:43:12,120 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:43:12,120 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.7, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 43, 12, 120833)}
2025-07-15 10:43:12,130 - INFO - ✅ 数据已插入到entries表: entry_id=145168
2025-07-15 10:43:12,134 - INFO - ✅ 触发器已创建队列项: queue_id=124
2025-07-15 10:43:12,241 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 10:57:23,313 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:57:23,323 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.006秒
2025-07-15 10:57:23,633 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:57:23,638 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:57:23,675 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:57:23,680 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:57:23,694 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:57:23,698 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:57:23,734 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:57:23,739 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:57:23,771 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:57:23,780 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:57:38,862 - INFO - 🗑️ 收到删除请求: entry_id=603661
2025-07-15 10:57:38,891 - INFO - 🔍 通过internal_id=603661未找到记录，尝试通过external_id查找
2025-07-15 10:57:38,908 - INFO - 📋 找到记录: internal_id=145168, external_id=603661
2025-07-15 10:57:38,950 - INFO - ✅ HTTP-only模式删除成功: internal_id=145168, external_id=603661
2025-07-15 10:59:28,219 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:59:28,227 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 10:59:28,520 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:59:28,524 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:59:28,553 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:59:28,558 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:59:28,612 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:59:28,616 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:59:28,635 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 10:59:28,639 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 10:59:28,714 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 10:59:28,718 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 10:59:37,184 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 10:59:37,184 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 10:59:37,184 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 3.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 10, 59, 37, 184414)}
2025-07-15 10:59:37,193 - INFO - ✅ 数据已插入到entries表: entry_id=145169
2025-07-15 10:59:37,196 - INFO - ✅ 触发器已创建队列项: queue_id=127
2025-07-15 10:59:37,303 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 11:06:45,165 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 11:06:45,165 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:18:07,060 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 11:18:07,060 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 11:18:07,266 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:18:07,266 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 11:19:44,961 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:19:44,966 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 11:19:44,972 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.006秒
2025-07-15 11:19:45,300 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:19:45,306 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:19:45,344 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:19:45,349 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:19:45,377 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:19:45,381 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:19:45,408 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:19:45,412 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:19:45,449 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:19:45,458 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:20:01,408 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:20:01,410 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 11:20:01,411 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 2.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 2, 'item': 2, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 11, 20, 1, 411525)}
2025-07-15 11:20:01,420 - INFO - ✅ 数据已插入到entries表: entry_id=145170
2025-07-15 11:20:01,423 - INFO - ✅ 触发器已创建队列项: queue_id=128
2025-07-15 11:20:01,530 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 11:23:43,734 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 11:23:43,737 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:23:45,372 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 11:23:45,372 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 11:23:45,597 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:23:45,597 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 11:25:39,667 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 11:25:39,667 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 11:25:39,934 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:25:39,934 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 11:27:12,952 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 11:27:12,956 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:29:31,274 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 11:29:31,274 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 11:29:31,502 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:29:31,502 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 11:31:03,862 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:31:03,867 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 11:31:03,874 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.007秒
2025-07-15 11:31:04,200 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:31:04,205 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:31:04,248 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:31:04,252 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:31:04,261 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:31:04,265 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:31:04,304 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:31:04,309 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:31:04,349 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:31:04,360 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:31:15,578 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:31:15,580 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 11:31:15,581 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.8, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 11, 31, 15, 581775)}
2025-07-15 11:31:15,590 - INFO - ✅ 数据已插入到entries表: entry_id=145171
2025-07-15 11:31:15,594 - INFO - ✅ 触发器已创建队列项: queue_id=129
2025-07-15 11:31:15,701 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 11:31:54,051 - INFO - 🗑️ 收到删除请求: entry_id=603668
2025-07-15 11:31:54,074 - INFO - 🔍 通过internal_id=603668未找到记录，尝试通过external_id查找
2025-07-15 11:31:54,090 - INFO - 📋 找到记录: internal_id=145169, external_id=603668
2025-07-15 11:31:54,130 - INFO - ✅ HTTP-only模式删除成功: internal_id=145169, external_id=603668
2025-07-15 11:31:57,687 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 11:33:02,756 - INFO - 🗑️ 收到删除请求: entry_id=603671
2025-07-15 11:33:02,773 - INFO - 🔍 通过internal_id=603671未找到记录，尝试通过external_id查找
2025-07-15 11:33:02,788 - INFO - 📋 找到记录: internal_id=145170, external_id=603671
2025-07-15 11:33:02,817 - INFO - ✅ HTTP-only模式删除成功: internal_id=145170, external_id=603671
2025-07-15 11:33:04,296 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.004秒
2025-07-15 11:33:40,028 - INFO - 🗑️ 收到删除请求: entry_id=603672
2025-07-15 11:33:40,043 - INFO - 🔍 通过internal_id=603672未找到记录，尝试通过external_id查找
2025-07-15 11:33:40,064 - INFO - 📋 找到记录: internal_id=145171, external_id=603672
2025-07-15 11:33:40,096 - INFO - ✅ HTTP-only模式删除成功: internal_id=145171, external_id=603672
2025-07-15 11:33:41,492 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 11:34:52,711 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:34:52,711 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 11:34:52,711 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.8, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 11, 34, 52, 711658)}
2025-07-15 11:34:52,724 - INFO - ✅ 数据已插入到entries表: entry_id=145172
2025-07-15 11:34:52,728 - INFO - ✅ 触发器已创建队列项: queue_id=136
2025-07-15 11:34:52,848 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.011秒
2025-07-15 11:35:22,497 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:35:22,512 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:35:22,516 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:35:22,605 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 11:35:22,728 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:35:22,733 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:35:40,528 - INFO - 🗑️ 收到删除请求: entry_id=603673
2025-07-15 11:35:40,544 - INFO - 🔍 通过internal_id=603673未找到记录，尝试通过external_id查找
2025-07-15 11:35:40,560 - INFO - 📋 找到记录: internal_id=145172, external_id=603673
2025-07-15 11:35:40,591 - INFO - ✅ HTTP-only模式删除成功: internal_id=145172, external_id=603673
2025-07-15 11:35:42,311 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 11:56:31,017 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:56:31,029 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.007秒
2025-07-15 11:56:31,315 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:56:31,320 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:56:31,356 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:56:31,361 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:56:31,393 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:56:31,397 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:56:31,417 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:56:31,421 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:56:31,458 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:56:31,468 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:58:43,520 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:58:43,520 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 11:58:43,520 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 5, 'item': 5, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 11, 58, 43, 520856)}
2025-07-15 11:58:43,531 - INFO - ✅ 数据已插入到entries表: entry_id=145173
2025-07-15 11:58:43,535 - INFO - ✅ 触发器已创建队列项: queue_id=139
2025-07-15 11:58:43,641 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 11:58:55,170 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:58:55,187 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:58:55,191 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:58:55,278 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 11:58:55,403 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:58:55,408 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:58:56,013 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:58:56,023 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 11:58:56,028 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 11:58:56,125 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.004秒
2025-07-15 11:58:56,240 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 11:58:56,244 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 11:59:25,712 - INFO - 🗑️ 收到删除请求: entry_id=603674
2025-07-15 11:59:25,732 - INFO - 🔍 通过internal_id=603674未找到记录，尝试通过external_id查找
2025-07-15 11:59:25,747 - INFO - 📋 找到记录: internal_id=145173, external_id=603674
2025-07-15 11:59:25,789 - INFO - ✅ HTTP-only模式删除成功: internal_id=145173, external_id=603674
2025-07-15 11:59:27,620 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.003秒
2025-07-15 12:00:36,515 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:00:36,516 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 12:00:36,516 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 5, 'item': 5, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 12, 0, 36, 516262)}
2025-07-15 12:00:36,525 - INFO - ✅ 数据已插入到entries表: entry_id=145174
2025-07-15 12:00:36,528 - INFO - ✅ 触发器已创建队列项: queue_id=142
2025-07-15 12:00:36,640 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.008秒
2025-07-15 12:00:52,892 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:00:52,903 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:00:52,908 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:00:53,001 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 12:00:53,120 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 12:00:53,124 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 12:15:26,657 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:15:26,657 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 12:15:26,657 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 2.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 2, 'item': 2, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 12, 15, 26, 657881)}
2025-07-15 12:15:26,667 - INFO - ✅ 数据已插入到entries表: entry_id=145175
2025-07-15 12:15:26,671 - INFO - ✅ 触发器已创建队列项: queue_id=143
2025-07-15 12:15:26,888 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.113秒
2025-07-15 12:15:41,677 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:15:41,699 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:15:41,704 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:15:41,794 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 12:15:41,921 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 12:15:41,925 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 12:17:06,198 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:17:06,209 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:17:06,213 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:17:06,306 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 12:17:06,426 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 12:17:06,435 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 12:17:56,061 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:17:56,061 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 12:17:56,061 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 2.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 2, 'item': 2, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 12, 17, 56, 61675)}
2025-07-15 12:17:56,072 - INFO - ✅ 数据已插入到entries表: entry_id=145176
2025-07-15 12:17:56,076 - INFO - ✅ 触发器已创建队列项: queue_id=145
2025-07-15 12:17:56,183 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 12:35:07,357 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 12:35:07,358 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:35:12,898 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 12:35:12,898 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 12:35:13,122 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:35:13,122 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 12:35:24,539 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:35:24,543 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 12:35:24,549 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.006秒
2025-07-15 12:35:24,882 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:35:24,886 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:35:24,931 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:35:24,936 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:35:24,950 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 12:35:24,954 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 12:35:24,983 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 12:35:24,987 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 12:35:25,032 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 12:35:25,042 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 12:38:08,973 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 12:38:08,977 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:42:29,703 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 12:42:29,703 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 12:42:29,925 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:42:29,925 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 12:42:39,497 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 12:42:39,606 - INFO - 📊 Entries查询完成: 1000条记录, 耗时: 0.109秒
2025-07-15 12:42:39,625 - INFO - 📝 收到更新请求: entry_id=603682
2025-07-15 12:42:39,654 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 12:42:39,670 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 12:42:39,670 - ERROR - ❌ 更新Entry失败: name 're' is not defined
2025-07-15 12:43:23,960 - INFO - 📊 Entries查询完成: 1000条记录, 耗时: 0.020秒
2025-07-15 12:43:23,985 - INFO - 📝 收到更新请求: entry_id=603682
2025-07-15 12:43:24,001 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 12:43:24,016 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 12:43:24,016 - ERROR - ❌ 更新Entry失败: name 're' is not defined
2025-07-15 12:43:27,837 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 12:43:27,841 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:44:22,857 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 12:44:22,857 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 12:44:23,065 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:44:23,065 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 12:44:35,649 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 12:44:35,740 - INFO - 📊 Entries查询完成: 1000条记录, 耗时: 0.091秒
2025-07-15 12:44:35,760 - INFO - 📝 收到更新请求: entry_id=603682
2025-07-15 12:44:35,782 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 12:44:35,798 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 12:44:35,827 - ERROR - 查询执行失败: 
            UPDATE entries 
            SET number = $1, factory_number = $2, project_number = $3, ... 错误: 同じ列"project_number"に複数の代入があります
2025-07-15 12:44:35,827 - ERROR - ❌ 更新Entry失败: 同じ列"project_number"に複数の代入があります
2025-07-15 13:04:43,003 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 13:04:43,004 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:45,202 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 13:04:45,202 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 13:04:45,422 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:04:45,422 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 13:05:10,793 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 13:05:10,901 - INFO - 📊 Entries查询完成: 1000条记录, 耗时: 0.108秒
2025-07-15 13:05:10,919 - INFO - 📝 收到更新请求: entry_id=603682
2025-07-15 13:05:10,942 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 13:05:10,968 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 13:05:10,970 - ERROR - 查询执行失败: 
            UPDATE entries 
            SET number = $1, factory_number = $2, project_number = $3, ... 错误: invalid input for query argument $7: 'TEST_STATUS' ('str' object cannot be interpreted as an integer)
2025-07-15 13:05:10,970 - ERROR - ❌ 更新Entry失败: invalid input for query argument $7: 'TEST_STATUS' ('str' object cannot be interpreted as an integer)
2025-07-15 13:07:29,755 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:07:29,769 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.004秒
2025-07-15 13:07:30,053 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:07:30,057 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:07:30,102 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:07:30,106 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:07:30,118 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:07:30,123 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:07:30,156 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:07:30,161 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:07:30,220 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:07:30,228 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:07:49,835 - INFO - 📝 收到更新请求: entry_id=603682
2025-07-15 13:07:49,854 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 13:07:49,871 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 13:07:49,872 - ERROR - 查询执行失败: 
            UPDATE entries 
            SET duration = $1, model = $2, project_number = $3, departm... 错误: invalid input for query argument $5: '2' ('str' object cannot be interpreted as an integer)
2025-07-15 13:07:49,887 - ERROR - ❌ 更新Entry失败: invalid input for query argument $5: '2' ('str' object cannot be interpreted as an integer)
2025-07-15 13:13:27,432 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 13:13:27,432 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:12,079 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 13:18:12,079 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 13:18:12,287 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:12,287 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 13:18:30,942 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:18:30,947 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 13:18:30,953 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.006秒
2025-07-15 13:18:31,281 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:18:31,285 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:18:31,312 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:18:31,316 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:18:31,345 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:18:31,350 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:18:31,383 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:18:31,387 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:18:31,430 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:18:31,438 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:18:48,483 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:48,485 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 13:18:48,486 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 5, 'item': 5, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 13, 18, 48, 486343)}
2025-07-15 13:18:48,498 - INFO - ✅ 数据已插入到entries表: entry_id=145177
2025-07-15 13:18:48,501 - INFO - ✅ 触发器已创建队列项: queue_id=147
2025-07-15 13:18:48,608 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:19:04,039 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:19:04,065 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:19:04,069 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:19:04,157 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:19:04,282 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:19:04,286 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:19:19,182 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:19:19,208 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:19:19,224 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:19:19,263 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:19:19,369 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:21:09,395 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:21:09,419 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:21:09,437 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:21:09,460 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:21:09,567 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:21:32,976 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:21:32,998 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:21:33,003 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:21:33,104 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.011秒
2025-07-15 13:21:33,222 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:21:33,233 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:43:37,668 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:43:37,870 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:43:37,887 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:43:37,910 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:43:38,024 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.006秒
2025-07-15 13:44:10,130 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:44:10,149 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:44:10,175 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:44:10,197 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:44:10,304 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:45:01,498 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:45:01,514 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:45:01,529 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:45:01,545 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:45:01,651 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:50:41,245 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:50:41,256 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.006秒
2025-07-15 13:50:41,549 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:50:41,554 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:50:41,594 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:50:41,599 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:50:41,614 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:50:41,618 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:50:41,652 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:50:41,656 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:50:41,685 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:50:41,694 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:50:56,964 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:50:56,983 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:50:56,999 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:50:57,021 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:50:57,128 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:51:27,967 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 13:51:27,971 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:29,942 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 13:51:29,942 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 13:51:30,142 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:51:30,143 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 13:51:49,310 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:51:49,315 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 13:51:49,322 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.006秒
2025-07-15 13:51:49,653 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:51:49,657 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:51:49,682 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:51:49,688 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:51:49,717 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 13:51:49,722 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 13:51:49,756 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:51:49,761 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:51:49,801 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 13:51:49,811 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 13:52:04,289 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:52:04,308 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:52:04,325 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:52:04,347 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:52:04,454 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 13:54:10,879 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 13:54:10,905 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 13:54:10,925 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 13:54:10,964 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 13:54:11,070 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:05:51,744 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 14:05:51,744 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:53,978 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 14:05:53,978 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 14:05:54,330 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:05:54,330 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 14:06:07,002 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:06:07,007 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 14:06:07,014 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.007秒
2025-07-15 14:06:07,349 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:06:07,354 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:06:07,383 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:06:07,388 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:06:07,416 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:06:07,421 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:06:07,452 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:06:07,456 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:06:07,491 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:06:07,501 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:06:29,572 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 14:06:29,594 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 14:06:29,611 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 14:06:29,651 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 14:06:29,758 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:14:54,485 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 14:14:54,485 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:14:56,255 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 14:14:56,255 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 14:14:56,530 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:14:56,531 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 14:15:17,581 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:15:17,586 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 14:15:17,592 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.006秒
2025-07-15 14:15:17,941 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:15:17,946 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:15:17,992 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:15:17,996 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:15:18,005 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:15:18,009 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:15:18,044 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:15:18,049 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:15:18,082 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:15:18,091 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:15:35,052 - INFO - 📝 收到更新请求: entry_id=603683
2025-07-15 14:15:35,071 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 14:15:35,088 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 14:15:35,130 - INFO - ✅ 更新成功: internal_id=145177, external_id=603683
2025-07-15 14:15:35,236 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:17:12,308 - INFO - 🗑️ 收到删除请求: entry_id=603683
2025-07-15 14:17:12,333 - INFO - 🔍 通过internal_id=603683未找到记录，尝试通过external_id查找
2025-07-15 14:17:12,351 - INFO - 📋 找到记录: internal_id=145177, external_id=603683
2025-07-15 14:17:12,385 - INFO - ✅ HTTP-only模式删除成功: internal_id=145177, external_id=603683
2025-07-15 14:17:15,797 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 14:18:26,095 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:18:26,097 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 14:18:26,098 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 14, 18, 26, 98158)}
2025-07-15 14:18:26,107 - INFO - ✅ 数据已插入到entries表: entry_id=145178
2025-07-15 14:18:26,111 - INFO - ✅ 触发器已创建队列项: queue_id=164
2025-07-15 14:18:26,218 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:18:41,244 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:18:41,261 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 2
2025-07-15 14:18:41,266 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:18:41,353 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:18:41,477 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:18:41,481 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:19:07,498 - INFO - 📝 收到更新请求: entry_id=603684
2025-07-15 14:19:07,517 - INFO - 🔍 通过internal_id=603684未找到记录，尝试通过external_id查找
2025-07-15 14:19:07,533 - INFO - 📋 找到记录: internal_id=145178, external_id=603684
2025-07-15 14:19:07,564 - INFO - ✅ 更新成功: internal_id=145178, external_id=603684
2025-07-15 14:19:07,679 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.011秒
2025-07-15 14:23:11,920 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 14:23:11,924 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:31,487 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 14:28:31,487 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 14:28:31,708 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:28:31,708 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 14:28:46,351 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:28:46,363 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 14:28:46,369 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.006秒
2025-07-15 14:28:46,709 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 2
2025-07-15 14:28:46,713 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:28:46,740 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 2
2025-07-15 14:28:46,745 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:28:46,775 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 2
2025-07-15 14:28:46,780 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:28:46,811 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:28:46,815 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:28:46,851 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:28:46,860 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:29:03,460 - INFO - 📝 收到更新请求: entry_id=603684
2025-07-15 14:29:03,490 - INFO - 🔍 通过internal_id=603684未找到记录，尝试通过external_id查找
2025-07-15 14:29:03,507 - INFO - 📋 找到记录: internal_id=145178, external_id=603684
2025-07-15 14:29:03,531 - INFO - ✅ 更新成功: internal_id=145178, external_id=603684
2025-07-15 14:29:03,637 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:30:36,357 - INFO - 🗑️ 收到删除请求: entry_id=603684
2025-07-15 14:30:36,386 - INFO - 🔍 通过internal_id=603684未找到记录，尝试通过external_id查找
2025-07-15 14:30:36,413 - INFO - 📋 找到记录: internal_id=145178, external_id=603684
2025-07-15 14:30:36,469 - INFO - ✅ HTTP-only模式删除成功: internal_id=145178, external_id=603684
2025-07-15 14:31:29,297 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 14:34:22,325 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 14:34:22,330 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:27,479 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 14:34:27,479 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 14:34:27,712 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:34:27,712 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 14:35:14,954 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:14,964 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 14:35:14,971 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.006秒
2025-07-15 14:35:15,355 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:35:15,359 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:15,385 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:35:15,389 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:15,417 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:35:15,422 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:15,458 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:35:15,462 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:35:15,514 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:35:15,523 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:35:30,047 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:35:30,050 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 14:35:30,051 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 3, 'item': 3, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 14, 35, 30, 51498)}
2025-07-15 14:35:30,060 - INFO - ✅ 数据已插入到entries表: entry_id=145179
2025-07-15 14:35:30,063 - INFO - ✅ 触发器已创建队列项: queue_id=170
2025-07-15 14:35:30,174 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:35:59,754 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:59,769 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 14:35:59,773 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:35:59,862 - INFO - 📊 Entries查询完成: 36条记录, 耗时: 0.003秒
2025-07-15 14:35:59,985 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:35:59,989 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:36:13,521 - INFO - 🗑️ 收到删除请求: entry_id=603685
2025-07-15 14:36:13,543 - INFO - 🔍 通过internal_id=603685未找到记录，尝试通过external_id查找
2025-07-15 14:36:13,561 - INFO - 📋 找到记录: internal_id=145179, external_id=603685
2025-07-15 14:36:13,702 - INFO - ✅ HTTP-only模式删除成功: internal_id=145179, external_id=603685
2025-07-15 14:36:15,645 - INFO - 📊 Entries查询完成: 35条记录, 耗时: 0.003秒
2025-07-15 14:40:10,752 - INFO - 🗑️ 收到删除请求: entry_id=603682
2025-07-15 14:40:10,768 - INFO - 🔍 通过internal_id=603682未找到记录，尝试通过external_id查找
2025-07-15 14:40:10,783 - INFO - 📋 找到记录: internal_id=145176, external_id=603682
2025-07-15 14:40:10,799 - INFO - ✅ HTTP-only模式删除成功: internal_id=145176, external_id=603682
2025-07-15 14:40:12,301 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.003秒
2025-07-15 14:53:15,573 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:15,588 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-15 14:53:15,593 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:15,681 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.003秒
2025-07-15 14:53:15,809 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:15,814 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:17,075 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:17,086 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-15 14:53:17,090 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:17,182 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-15 14:53:17,301 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:17,310 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:18,265 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:18,276 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-15 14:53:18,280 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:18,372 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-15 14:53:18,569 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:18,574 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:19,503 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:19,513 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-15 14:53:19,517 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:19,610 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-15 14:53:19,729 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:19,733 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:20,481 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:20,492 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:20,496 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 14:53:20,590 - INFO - 📊 Entries查询完成: 23条记录, 耗时: 0.004秒
2025-07-15 14:53:20,711 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-15 14:53:20,715 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:21,449 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:21,459 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 23, 不匹配: 8
2025-07-15 14:53:21,463 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 14:53:21,563 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.009秒
2025-07-15 14:53:21,674 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 14:53:21,678 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:01:25,537 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:01:25,553 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 3
2025-07-15 15:01:25,558 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:01:25,646 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.004秒
2025-07-15 15:01:25,776 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:01:25,780 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:01:58,612 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:01:58,625 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:01:58,625 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:01:58,627 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:01:58,628 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:01:58,630 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:01:58,717 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-15 15:01:58,840 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 3
2025-07-15 15:01:58,850 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:01:59,013 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:01:59,023 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 3
2025-07-15 15:01:59,027 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:01:59,121 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.002秒
2025-07-15 15:01:59,307 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:01:59,311 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:18:25,746 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:18:25,764 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 5
2025-07-15 15:18:25,768 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:18:25,855 - INFO - 📊 Entries查询完成: 33条记录, 耗时: 0.005秒
2025-07-15 15:18:25,991 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:18:25,996 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:18:56,754 - INFO - 🗑️ 收到删除请求: entry_id=603715
2025-07-15 15:18:56,876 - INFO - 🔍 通过internal_id=603715未找到记录，尝试通过external_id查找
2025-07-15 15:18:56,898 - INFO - 📋 找到记录: internal_id=145209, external_id=603715
2025-07-15 15:18:57,049 - INFO - ✅ HTTP-only模式删除成功: internal_id=145209, external_id=603715
2025-07-15 15:18:58,312 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:20:03,141 - INFO - 🗑️ 收到删除请求: entry_id=603696
2025-07-15 15:20:03,157 - INFO - 🔍 通过internal_id=603696未找到记录，尝试通过external_id查找
2025-07-15 15:20:03,172 - INFO - 📋 找到记录: internal_id=145190, external_id=603696
2025-07-15 15:20:03,305 - INFO - ✅ HTTP-only模式删除成功: internal_id=145190, external_id=603696
2025-07-15 15:20:04,689 - INFO - 📊 Entries查询完成: 31条记录, 耗时: 0.003秒
2025-07-15 15:20:31,177 - INFO - 🗑️ 收到删除请求: entry_id=603693
2025-07-15 15:20:31,193 - INFO - 🔍 通过internal_id=603693未找到记录，尝试通过external_id查找
2025-07-15 15:20:31,208 - INFO - 📋 找到记录: internal_id=145185, external_id=603693
2025-07-15 15:20:31,341 - INFO - ✅ HTTP-only模式删除成功: internal_id=145185, external_id=603693
2025-07-15 15:20:33,757 - INFO - 📊 Entries查询完成: 30条记录, 耗时: 0.003秒
2025-07-15 15:20:54,654 - INFO - 🗑️ 收到删除请求: entry_id=603698
2025-07-15 15:20:54,672 - INFO - 🔍 通过internal_id=603698未找到记录，尝试通过external_id查找
2025-07-15 15:20:54,691 - INFO - 📋 找到记录: internal_id=145191, external_id=603698
2025-07-15 15:20:54,822 - INFO - ✅ HTTP-only模式删除成功: internal_id=145191, external_id=603698
2025-07-15 15:20:56,606 - INFO - 📊 Entries查询完成: 29条记录, 耗时: 0.003秒
2025-07-15 15:21:04,163 - INFO - 🗑️ 收到删除请求: entry_id=603691
2025-07-15 15:21:04,179 - INFO - 🔍 通过internal_id=603691未找到记录，尝试通过external_id查找
2025-07-15 15:21:04,194 - INFO - 📋 找到记录: internal_id=145186, external_id=603691
2025-07-15 15:21:04,331 - INFO - ✅ HTTP-only模式删除成功: internal_id=145186, external_id=603691
2025-07-15 15:21:05,320 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.011秒
2025-07-15 15:21:10,226 - INFO - 🗑️ 收到删除请求: entry_id=603686
2025-07-15 15:21:10,258 - INFO - 🔍 通过internal_id=603686未找到记录，尝试通过external_id查找
2025-07-15 15:21:10,290 - INFO - 📋 找到记录: internal_id=145181, external_id=603686
2025-07-15 15:21:10,458 - INFO - ✅ HTTP-only模式删除成功: internal_id=145181, external_id=603686
2025-07-15 15:21:11,511 - INFO - 📊 Entries查询完成: 27条记录, 耗时: 0.003秒
2025-07-15 15:21:14,891 - INFO - 🗑️ 收到删除请求: entry_id=603697
2025-07-15 15:21:14,920 - INFO - 🔍 通过internal_id=603697未找到记录，尝试通过external_id查找
2025-07-15 15:21:14,948 - INFO - 📋 找到记录: internal_id=145192, external_id=603697
2025-07-15 15:21:15,096 - INFO - ✅ HTTP-only模式删除成功: internal_id=145192, external_id=603697
2025-07-15 15:21:16,243 - INFO - 📊 Entries查询完成: 26条记录, 耗时: 0.003秒
2025-07-15 15:21:18,962 - INFO - 🗑️ 收到删除请求: entry_id=603692
2025-07-15 15:21:18,985 - INFO - 🔍 通过internal_id=603692未找到记录，尝试通过external_id查找
2025-07-15 15:21:19,002 - INFO - 📋 找到记录: internal_id=145187, external_id=603692
2025-07-15 15:21:19,136 - INFO - ✅ HTTP-only模式删除成功: internal_id=145187, external_id=603692
2025-07-15 15:21:20,017 - INFO - 📊 Entries查询完成: 25条记录, 耗时: 0.003秒
2025-07-15 15:21:24,657 - INFO - 🗑️ 收到删除请求: entry_id=603688
2025-07-15 15:21:24,685 - INFO - 🔍 通过internal_id=603688未找到记录，尝试通过external_id查找
2025-07-15 15:21:24,714 - INFO - 📋 找到记录: internal_id=145182, external_id=603688
2025-07-15 15:21:24,867 - INFO - ✅ HTTP-only模式删除成功: internal_id=145182, external_id=603688
2025-07-15 15:21:26,226 - INFO - 📊 Entries查询完成: 24条记录, 耗时: 0.003秒
2025-07-15 15:21:39,236 - INFO - 🗑️ 收到删除请求: entry_id=603699
2025-07-15 15:21:39,249 - INFO - 🔍 通过internal_id=603699未找到记录，尝试通过external_id查找
2025-07-15 15:21:39,262 - INFO - 📋 找到记录: internal_id=145193, external_id=603699
2025-07-15 15:21:39,388 - INFO - ✅ HTTP-only模式删除成功: internal_id=145193, external_id=603699
2025-07-15 15:21:40,248 - INFO - 📊 Entries查询完成: 23条记录, 耗时: 0.003秒
2025-07-15 15:21:42,757 - INFO - 🗑️ 收到删除请求: entry_id=603694
2025-07-15 15:21:42,772 - INFO - 🔍 通过internal_id=603694未找到记录，尝试通过external_id查找
2025-07-15 15:21:42,793 - INFO - 📋 找到记录: internal_id=145188, external_id=603694
2025-07-15 15:21:42,927 - INFO - ✅ HTTP-only模式删除成功: internal_id=145188, external_id=603694
2025-07-15 15:21:44,272 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-15 15:21:47,342 - INFO - 🗑️ 收到删除请求: entry_id=603689
2025-07-15 15:21:47,363 - INFO - 🔍 通过internal_id=603689未找到记录，尝试通过external_id查找
2025-07-15 15:21:47,382 - INFO - 📋 找到记录: internal_id=145183, external_id=603689
2025-07-15 15:21:47,518 - INFO - ✅ HTTP-only模式删除成功: internal_id=145183, external_id=603689
2025-07-15 15:21:48,540 - INFO - 📊 Entries查询完成: 21条记录, 耗时: 0.003秒
2025-07-15 15:21:51,065 - INFO - 🗑️ 收到删除请求: entry_id=603700
2025-07-15 15:21:51,086 - INFO - 🔍 通过internal_id=603700未找到记录，尝试通过external_id查找
2025-07-15 15:21:51,109 - INFO - 📋 找到记录: internal_id=145194, external_id=603700
2025-07-15 15:21:51,246 - INFO - ✅ HTTP-only模式删除成功: internal_id=145194, external_id=603700
2025-07-15 15:21:52,309 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 15:21:54,575 - INFO - 🗑️ 收到删除请求: entry_id=603695
2025-07-15 15:21:54,595 - INFO - 🔍 通过internal_id=603695未找到记录，尝试通过external_id查找
2025-07-15 15:21:54,623 - INFO - 📋 找到记录: internal_id=145189, external_id=603695
2025-07-15 15:21:54,776 - INFO - ✅ HTTP-only模式删除成功: internal_id=145189, external_id=603695
2025-07-15 15:21:55,622 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.003秒
2025-07-15 15:21:58,365 - INFO - 🗑️ 收到删除请求: entry_id=603690
2025-07-15 15:21:58,384 - INFO - 🔍 通过internal_id=603690未找到记录，尝试通过external_id查找
2025-07-15 15:21:58,400 - INFO - 📋 找到记录: internal_id=145184, external_id=603690
2025-07-15 15:21:58,538 - INFO - ✅ HTTP-only模式删除成功: internal_id=145184, external_id=603690
2025-07-15 15:21:59,599 - INFO - 📊 Entries查询完成: 18条记录, 耗时: 0.003秒
2025-07-15 15:22:21,327 - INFO - 🗑️ 收到删除请求: entry_id=603713
2025-07-15 15:22:21,340 - INFO - 🔍 通过internal_id=603713未找到记录，尝试通过external_id查找
2025-07-15 15:22:21,355 - INFO - 📋 找到记录: internal_id=145207, external_id=603713
2025-07-15 15:22:21,481 - INFO - ✅ HTTP-only模式删除成功: internal_id=145207, external_id=603713
2025-07-15 15:22:22,988 - INFO - 📊 Entries查询完成: 17条记录, 耗时: 0.003秒
2025-07-15 15:22:26,636 - INFO - 🗑️ 收到删除请求: entry_id=603712
2025-07-15 15:22:26,664 - INFO - 🔍 通过internal_id=603712未找到记录，尝试通过external_id查找
2025-07-15 15:22:26,691 - INFO - 📋 找到记录: internal_id=145206, external_id=603712
2025-07-15 15:22:26,849 - INFO - ✅ HTTP-only模式删除成功: internal_id=145206, external_id=603712
2025-07-15 15:22:28,839 - INFO - 📊 Entries查询完成: 16条记录, 耗时: 0.003秒
2025-07-15 15:22:32,126 - INFO - 🗑️ 收到删除请求: entry_id=603711
2025-07-15 15:22:32,151 - INFO - 🔍 通过internal_id=603711未找到记录，尝试通过external_id查找
2025-07-15 15:22:32,181 - INFO - 📋 找到记录: internal_id=145205, external_id=603711
2025-07-15 15:22:32,334 - INFO - ✅ HTTP-only模式删除成功: internal_id=145205, external_id=603711
2025-07-15 15:22:33,582 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 15:22:39,711 - INFO - 🗑️ 收到删除请求: entry_id=603710
2025-07-15 15:22:39,740 - INFO - 🔍 通过internal_id=603710未找到记录，尝试通过external_id查找
2025-07-15 15:22:39,762 - INFO - 📋 找到记录: internal_id=145204, external_id=603710
2025-07-15 15:22:39,904 - INFO - ✅ HTTP-only模式删除成功: internal_id=145204, external_id=603710
2025-07-15 15:22:41,120 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.004秒
2025-07-15 15:22:44,049 - INFO - 🗑️ 收到删除请求: entry_id=603709
2025-07-15 15:22:44,079 - INFO - 🔍 通过internal_id=603709未找到记录，尝试通过external_id查找
2025-07-15 15:22:44,105 - INFO - 📋 找到记录: internal_id=145203, external_id=603709
2025-07-15 15:22:44,258 - INFO - ✅ HTTP-only模式删除成功: internal_id=145203, external_id=603709
2025-07-15 15:22:45,217 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-15 15:22:47,746 - INFO - 🗑️ 收到删除请求: entry_id=603707
2025-07-15 15:22:47,769 - INFO - 🔍 通过internal_id=603707未找到记录，尝试通过external_id查找
2025-07-15 15:22:47,794 - INFO - 📋 找到记录: internal_id=145202, external_id=603707
2025-07-15 15:22:47,928 - INFO - ✅ HTTP-only模式删除成功: internal_id=145202, external_id=603707
2025-07-15 15:22:48,941 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-15 15:22:51,500 - INFO - 🗑️ 收到删除请求: entry_id=603708
2025-07-15 15:22:51,517 - INFO - 🔍 通过internal_id=603708未找到记录，尝试通过external_id查找
2025-07-15 15:22:51,546 - INFO - 📋 找到记录: internal_id=145201, external_id=603708
2025-07-15 15:22:51,706 - INFO - ✅ HTTP-only模式删除成功: internal_id=145201, external_id=603708
2025-07-15 15:22:52,620 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 15:22:55,062 - INFO - 🗑️ 收到删除请求: entry_id=603706
2025-07-15 15:22:55,081 - INFO - 🔍 通过internal_id=603706未找到记录，尝试通过external_id查找
2025-07-15 15:22:55,101 - INFO - 📋 找到记录: internal_id=145200, external_id=603706
2025-07-15 15:22:55,232 - INFO - ✅ HTTP-only模式删除成功: internal_id=145200, external_id=603706
2025-07-15 15:22:56,283 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-15 15:22:59,533 - INFO - 🗑️ 收到删除请求: entry_id=603705
2025-07-15 15:22:59,559 - INFO - 🔍 通过internal_id=603705未找到记录，尝试通过external_id查找
2025-07-15 15:22:59,588 - INFO - 📋 找到记录: internal_id=145199, external_id=603705
2025-07-15 15:22:59,746 - INFO - ✅ HTTP-only模式删除成功: internal_id=145199, external_id=603705
2025-07-15 15:23:00,840 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 15:23:03,447 - INFO - 🗑️ 收到删除请求: entry_id=603704
2025-07-15 15:23:03,462 - INFO - 🔍 通过internal_id=603704未找到记录，尝试通过external_id查找
2025-07-15 15:23:03,489 - INFO - 📋 找到记录: internal_id=145198, external_id=603704
2025-07-15 15:23:03,626 - INFO - ✅ HTTP-only模式删除成功: internal_id=145198, external_id=603704
2025-07-15 15:23:04,833 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-15 15:23:07,374 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:23:07,385 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:23:07,386 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:23:07,387 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:23:07,388 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:23:07,390 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:23:07,478 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-15 15:23:07,599 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 31, 不匹配: 0
2025-07-15 15:23:07,608 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:23:07,986 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:23:07,997 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 31, 不匹配: 0
2025-07-15 15:23:08,002 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:23:08,093 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-15 15:23:08,220 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:23:08,225 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:25:50,793 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:25:50,801 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:50,802 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:50,803 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:50,804 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:50,807 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:25:50,900 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.003秒
2025-07-15 15:25:51,018 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:25:51,022 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:51,164 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:51,194 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:25:51,205 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:51,281 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.003秒
2025-07-15 15:25:51,436 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:25:51,447 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:25:55,837 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:55,849 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:25:55,853 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:55,945 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.003秒
2025-07-15 15:25:56,070 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:25:56,074 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:25:57,047 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:25:57,054 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:57,055 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:57,057 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:57,057 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:25:57,060 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:25:57,153 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-15 15:25:57,269 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:25:57,273 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:57,637 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:57,647 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:25:57,651 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:25:57,745 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.003秒
2025-07-15 15:25:57,862 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:25:57,866 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:27:35,312 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:27:35,323 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:27:35,329 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:27:35,425 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.007秒
2025-07-15 15:27:35,544 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:27:35,548 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:27:36,074 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:27:36,093 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:27:36,098 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:27:36,188 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.004秒
2025-07-15 15:27:36,312 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:27:36,317 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:02,970 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:02,983 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:02,988 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:03,078 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:28:03,207 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:03,218 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:03,808 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:03,818 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:03,822 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:03,916 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:28:04,034 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:04,039 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:04,517 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:04,528 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:04,532 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:04,625 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:28:04,744 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:04,749 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:05,658 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:05,667 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:05,672 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:05,767 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:28:05,883 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:05,887 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:07,847 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:07,857 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:07,861 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:07,955 - INFO - 📊 Entries查询完成: 32条记录, 耗时: 0.003秒
2025-07-15 15:28:08,073 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:08,078 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:30,983 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:30,993 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:30,997 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:31,093 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:31,208 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:31,212 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:31,780 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:31,790 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:31,794 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:31,893 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.004秒
2025-07-15 15:28:32,005 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:32,010 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:32,407 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:32,417 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:32,421 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:32,514 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:32,634 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:32,639 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:32,944 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:32,973 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:32,984 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,059 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:33,182 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,194 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:33,201 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,223 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:33,227 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:33,290 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:33,372 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,382 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:33,387 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,417 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:33,421 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:33,480 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:33,583 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,619 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:33,630 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,656 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:33,660 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:33,702 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:33,744 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,754 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:33,758 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,846 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:33,850 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:33,859 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:33,958 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,969 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:33,973 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:33,992 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:33,997 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:34,066 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:34,185 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:34,190 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:38,214 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:38,227 - INFO - 📊 Entries查询完成: 43条记录, 耗时: 0.003秒
2025-07-15 15:28:38,513 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:38,517 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:38,542 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:38,546 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:38,610 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:38,614 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:28:38,632 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 22, 不匹配: 8
2025-07-15 15:28:38,636 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:28:38,712 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:28:38,716 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:32:24,875 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:32:24,890 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-15 15:32:24,895 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:32:24,987 - INFO - 📊 Entries查询完成: 23条记录, 耗时: 0.004秒
2025-07-15 15:32:25,111 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:32:25,116 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:36:50,248 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:36:50,258 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-15 15:36:50,263 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:36:50,356 - INFO - 📊 Entries查询完成: 23条记录, 耗时: 0.003秒
2025-07-15 15:36:50,475 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:36:50,482 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:37:28,744 - INFO - 🗑️ 收到删除请求: entry_id=603413
2025-07-15 15:37:28,767 - INFO - 🔍 通过internal_id=603413未找到记录，尝试通过external_id查找
2025-07-15 15:37:28,783 - INFO - 📋 找到记录: internal_id=145531, external_id=603413
2025-07-15 15:37:28,931 - INFO - ✅ HTTP-only模式删除成功: internal_id=145531, external_id=603413
2025-07-15 15:37:30,367 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.004秒
2025-07-15 15:38:13,070 - INFO - 🗑️ 收到删除请求: entry_id=603406
2025-07-15 15:38:13,092 - INFO - 🔍 通过internal_id=603406未找到记录，尝试通过external_id查找
2025-07-15 15:38:13,111 - INFO - 📋 找到记录: internal_id=145540, external_id=603406
2025-07-15 15:38:13,250 - INFO - ✅ HTTP-only模式删除成功: internal_id=145540, external_id=603406
2025-07-15 15:38:14,568 - INFO - 📊 Entries查询完成: 21条记录, 耗时: 0.003秒
2025-07-15 15:38:37,121 - INFO - 🗑️ 收到删除请求: entry_id=603407
2025-07-15 15:38:37,146 - INFO - 🔍 通过internal_id=603407未找到记录，尝试通过external_id查找
2025-07-15 15:38:37,171 - INFO - 📋 找到记录: internal_id=145541, external_id=603407
2025-07-15 15:38:37,314 - INFO - ✅ HTTP-only模式删除成功: internal_id=145541, external_id=603407
2025-07-15 15:38:39,915 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 15:38:54,602 - INFO - 🗑️ 收到删除请求: entry_id=603408
2025-07-15 15:38:54,621 - INFO - 🔍 通过internal_id=603408未找到记录，尝试通过external_id查找
2025-07-15 15:38:54,639 - INFO - 📋 找到记录: internal_id=145542, external_id=603408
2025-07-15 15:38:54,793 - INFO - ✅ HTTP-only模式删除成功: internal_id=145542, external_id=603408
2025-07-15 15:38:55,936 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.003秒
2025-07-15 15:39:10,386 - INFO - 🗑️ 收到删除请求: entry_id=603680
2025-07-15 15:39:10,402 - INFO - 🔍 通过internal_id=603680未找到记录，尝试通过external_id查找
2025-07-15 15:39:10,417 - INFO - 📋 找到记录: internal_id=145619, external_id=603680
2025-07-15 15:39:10,548 - INFO - ✅ HTTP-only模式删除成功: internal_id=145619, external_id=603680
2025-07-15 15:39:11,652 - INFO - 📊 Entries查询完成: 18条记录, 耗时: 0.003秒
2025-07-15 15:39:14,714 - INFO - 🗑️ 收到删除请求: entry_id=603670
2025-07-15 15:39:14,734 - INFO - 🔍 通过internal_id=603670未找到记录，尝试通过external_id查找
2025-07-15 15:39:14,750 - INFO - 📋 找到记录: internal_id=145617, external_id=603670
2025-07-15 15:39:14,908 - INFO - ✅ HTTP-only模式删除成功: internal_id=145617, external_id=603670
2025-07-15 15:39:16,431 - INFO - 📊 Entries查询完成: 17条记录, 耗时: 0.011秒
2025-07-15 15:39:18,940 - INFO - 🗑️ 收到删除请求: entry_id=603669
2025-07-15 15:39:18,959 - INFO - 🔍 通过internal_id=603669未找到记录，尝试通过external_id查找
2025-07-15 15:39:18,978 - INFO - 📋 找到记录: internal_id=145616, external_id=603669
2025-07-15 15:39:19,125 - INFO - ✅ HTTP-only模式删除成功: internal_id=145616, external_id=603669
2025-07-15 15:39:20,018 - INFO - 📊 Entries查询完成: 16条记录, 耗时: 0.003秒
2025-07-15 15:40:43,132 - INFO - 🗑️ 收到删除请求: entry_id=603718
2025-07-15 15:40:43,157 - INFO - 🔍 通过internal_id=603718未找到记录，尝试通过external_id查找
2025-07-15 15:40:43,185 - INFO - 📋 找到记录: internal_id=145212, external_id=603718
2025-07-15 15:40:43,336 - INFO - ✅ HTTP-only模式删除成功: internal_id=145212, external_id=603718
2025-07-15 15:40:45,064 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 15:42:13,119 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:42:13,133 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 15:42:13,138 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:42:13,228 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 15:42:13,353 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:42:13,359 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:42:14,082 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:42:14,093 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 15:42:14,098 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:42:14,190 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 15:42:14,319 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:42:14,331 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:42:31,375 - INFO - 🗑️ 收到删除请求: entry_id=603661
2025-07-15 15:42:31,396 - INFO - 🔍 通过internal_id=603661未找到记录，尝试通过external_id查找
2025-07-15 15:42:31,412 - INFO - 📋 找到记录: internal_id=145611, external_id=603661
2025-07-15 15:42:31,547 - INFO - ✅ HTTP-only模式删除成功: internal_id=145611, external_id=603661
2025-07-15 15:42:35,598 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.004秒
2025-07-15 15:42:51,110 - INFO - 🗑️ 收到删除请求: entry_id=603660
2025-07-15 15:42:51,127 - INFO - 🔍 通过internal_id=603660未找到记录，尝试通过external_id查找
2025-07-15 15:42:51,146 - INFO - 📋 找到记录: internal_id=145610, external_id=603660
2025-07-15 15:42:51,282 - INFO - ✅ HTTP-only模式删除成功: internal_id=145610, external_id=603660
2025-07-15 15:42:52,472 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-15 15:43:17,232 - INFO - 🗑️ 收到删除请求: entry_id=603657
2025-07-15 15:43:17,256 - INFO - 🔍 通过internal_id=603657未找到记录，尝试通过external_id查找
2025-07-15 15:43:17,270 - INFO - 📋 找到记录: internal_id=145609, external_id=603657
2025-07-15 15:43:17,422 - INFO - ✅ HTTP-only模式删除成功: internal_id=145609, external_id=603657
2025-07-15 15:43:18,698 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.002秒
2025-07-15 15:43:29,202 - INFO - 🗑️ 收到删除请求: entry_id=603655
2025-07-15 15:43:29,219 - INFO - 🔍 通过internal_id=603655未找到记录，尝试通过external_id查找
2025-07-15 15:43:29,234 - INFO - 📋 找到记录: internal_id=145608, external_id=603655
2025-07-15 15:43:29,365 - INFO - ✅ HTTP-only模式删除成功: internal_id=145608, external_id=603655
2025-07-15 15:43:31,577 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 15:43:53,974 - INFO - 🗑️ 收到删除请求: entry_id=603643
2025-07-15 15:43:54,000 - INFO - 🔍 通过internal_id=603643未找到记录，尝试通过external_id查找
2025-07-15 15:43:54,019 - INFO - 📋 找到记录: internal_id=145598, external_id=603643
2025-07-15 15:43:54,152 - INFO - ✅ HTTP-only模式删除成功: internal_id=145598, external_id=603643
2025-07-15 15:43:55,726 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 15:44:07,419 - INFO - 🗑️ 收到删除请求: entry_id=603642
2025-07-15 15:44:07,435 - INFO - 🔍 通过internal_id=603642未找到记录，尝试通过external_id查找
2025-07-15 15:44:07,450 - INFO - 📋 找到记录: internal_id=145597, external_id=603642
2025-07-15 15:44:07,591 - INFO - ✅ HTTP-only模式删除成功: internal_id=145597, external_id=603642
2025-07-15 15:44:08,776 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.009秒
2025-07-15 15:44:13,754 - INFO - 🗑️ 收到删除请求: entry_id=603639
2025-07-15 15:44:13,771 - INFO - 🔍 通过internal_id=603639未找到记录，尝试通过external_id查找
2025-07-15 15:44:13,795 - INFO - 📋 找到记录: internal_id=145596, external_id=603639
2025-07-15 15:44:13,934 - INFO - ✅ HTTP-only模式删除成功: internal_id=145596, external_id=603639
2025-07-15 15:44:15,053 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-15 15:44:21,769 - INFO - 🗑️ 收到删除请求: entry_id=603624
2025-07-15 15:44:21,782 - INFO - 🔍 通过internal_id=603624未找到记录，尝试通过external_id查找
2025-07-15 15:44:21,794 - INFO - 📋 找到记录: internal_id=145583, external_id=603624
2025-07-15 15:44:21,932 - INFO - ✅ HTTP-only模式删除成功: internal_id=145583, external_id=603624
2025-07-15 15:44:23,117 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:47:35,682 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:47:35,695 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:47:35,699 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:47:35,791 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.003秒
2025-07-15 15:47:35,911 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:47:35,915 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:47:36,697 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:47:36,703 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:47:36,704 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:47:36,705 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:47:36,706 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:47:36,708 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:47:36,802 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-15 15:47:36,918 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:47:36,922 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:47:37,790 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:47:37,800 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:47:37,804 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:47:37,897 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:47:38,030 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:47:38,043 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:17,110 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,122 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:17,126 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,217 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:17,346 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:17,355 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:17,602 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,633 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:17,643 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,719 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:17,775 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,785 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:17,789 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,864 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:17,868 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:17,885 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.006秒
2025-07-15 15:48:17,952 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:17,961 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:17,965 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:18,000 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:18,004 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:18,059 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:18,112 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:18,122 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:18,126 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:18,176 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:18,180 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:18,219 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:18,278 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:18,288 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:18,292 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:18,339 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:18,346 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:18,385 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:18,503 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:18,507 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:19,400 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:48:19,416 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:48:19,416 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:48:19,418 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:48:19,419 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-15 15:48:19,421 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-15 15:48:19,509 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-15 15:48:19,637 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:19,642 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:20,473 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:20,482 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:20,486 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:20,580 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:20,698 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:20,702 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:21,377 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,386 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:21,390 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,483 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:21,603 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:21,607 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:21,642 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,652 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:21,656 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,749 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:21,826 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,836 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:21,840 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:21,868 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:21,872 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:21,933 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,012 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,042 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,052 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,087 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,092 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,128 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,186 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,196 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,200 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,272 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,276 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,293 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,351 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,360 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,365 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,411 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,415 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,458 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,523 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,533 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,538 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,574 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,579 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,630 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,643 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,652 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,656 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,748 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,753 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,761 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:22,823 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,833 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 30, 不匹配: 1
2025-07-15 15:48:22,837 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 15:48:22,866 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:22,870 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:48:22,931 - INFO - 📊 Entries查询完成: 7条记录, 耗时: 0.002秒
2025-07-15 15:48:23,048 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 15:48:23,052 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 15:52:12,724 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 15:52:12,724 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 15:52:12,724 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 15, 52, 12, 724446)}
2025-07-15 15:52:12,734 - INFO - ✅ 数据已插入到entries表: entry_id=145952
2025-07-15 15:52:12,737 - INFO - ✅ 触发器已创建队列项: queue_id=289
2025-07-15 15:52:12,843 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-15 16:12:04,925 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 16:12:04,925 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:10,548 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 16:12:10,548 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 16:12:10,949 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:10,949 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 16:12:35,119 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:12:35,126 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 16:12:35,132 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.006秒
2025-07-15 16:12:35,475 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:12:35,480 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:12:35,523 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:12:35,528 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:12:35,537 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:12:35,541 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:12:35,579 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:12:35,583 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:12:35,622 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:12:35,630 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:12:49,200 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:49,202 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:12:49,203 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 12, 49, 203456)}
2025-07-15 16:12:49,216 - INFO - ✅ 数据已插入到entries表: entry_id=145953
2025-07-15 16:12:49,220 - INFO - ✅ 触发器已创建队列项: queue_id=290
2025-07-15 16:12:49,326 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 16:13:31,713 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:13:31,729 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:13:31,733 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:13:31,820 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:13:31,952 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:13:31,962 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:14:26,445 - INFO - 📝 收到更新请求: entry_id=603687
2025-07-15 16:14:26,464 - INFO - 🔍 通过internal_id=603687未找到记录，尝试通过external_id查找
2025-07-15 16:14:26,481 - INFO - 📋 找到记录: internal_id=145180, external_id=603687
2025-07-15 16:14:26,504 - INFO - ✅ 更新成功: internal_id=145180, external_id=603687
2025-07-15 16:14:26,610 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 16:23:01,754 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 16:23:01,754 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:23:03,348 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 16:23:03,348 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 16:23:03,552 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:23:03,553 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 16:23:15,293 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:23:15,301 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 16:23:15,308 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.006秒
2025-07-15 16:23:15,651 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:23:15,656 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:23:15,704 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:23:15,709 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:23:15,718 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:23:15,722 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:23:15,755 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:23:15,760 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:23:15,803 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:23:15,812 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:23:47,131 - INFO - 📝 收到更新请求: entry_id=603714
2025-07-15 16:23:47,150 - INFO - 🔍 通过internal_id=603714未找到记录，尝试通过external_id查找
2025-07-15 16:23:47,167 - INFO - 📋 找到记录: internal_id=145208, external_id=603714
2025-07-15 16:23:47,199 - INFO - ✅ 更新成功: internal_id=145208, external_id=603714
2025-07-15 16:23:47,306 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 16:24:18,194 - INFO - 📝 收到更新请求: entry_id=603717
2025-07-15 16:24:18,210 - INFO - 🔍 通过internal_id=603717未找到记录，尝试通过external_id查找
2025-07-15 16:24:18,225 - INFO - 📋 找到记录: internal_id=145210, external_id=603717
2025-07-15 16:24:18,241 - INFO - ✅ 更新成功: internal_id=145210, external_id=603717
2025-07-15 16:24:18,347 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:24:27,470 - INFO - 📝 收到更新请求: entry_id=603716
2025-07-15 16:24:27,500 - INFO - 🔍 通过internal_id=603716未找到记录，尝试通过external_id查找
2025-07-15 16:24:27,527 - INFO - 📋 找到记录: internal_id=145211, external_id=603716
2025-07-15 16:24:27,547 - INFO - ✅ 更新成功: internal_id=145211, external_id=603716
2025-07-15 16:24:27,653 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:24:43,569 - INFO - 📝 收到更新请求: entry_id=603720
2025-07-15 16:24:43,587 - INFO - 🔍 通过internal_id=603720未找到记录，尝试通过external_id查找
2025-07-15 16:24:43,603 - INFO - 📋 找到记录: internal_id=145953, external_id=603720
2025-07-15 16:24:43,619 - INFO - ✅ 更新成功: internal_id=145953, external_id=603720
2025-07-15 16:24:43,725 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:24:56,322 - INFO - 📝 收到更新请求: entry_id=603719
2025-07-15 16:24:56,339 - INFO - 🔍 通过internal_id=603719未找到记录，尝试通过external_id查找
2025-07-15 16:24:56,356 - INFO - 📋 找到记录: internal_id=145952, external_id=603719
2025-07-15 16:24:56,372 - INFO - ✅ 更新成功: internal_id=145952, external_id=603719
2025-07-15 16:24:56,485 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.008秒
2025-07-15 16:25:36,344 - INFO - 📝 收到更新请求: entry_id=603702
2025-07-15 16:25:36,360 - INFO - 🔍 通过internal_id=603702未找到记录，尝试通过external_id查找
2025-07-15 16:25:36,375 - INFO - 📋 找到记录: internal_id=145195, external_id=603702
2025-07-15 16:25:36,392 - INFO - ✅ 更新成功: internal_id=145195, external_id=603702
2025-07-15 16:25:36,498 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:25:46,964 - INFO - 📝 收到更新请求: entry_id=603702
2025-07-15 16:25:46,988 - INFO - 🔍 通过internal_id=603702未找到记录，尝试通过external_id查找
2025-07-15 16:25:47,004 - INFO - 📋 找到记录: internal_id=145195, external_id=603702
2025-07-15 16:25:47,022 - INFO - ✅ 更新成功: internal_id=145195, external_id=603702
2025-07-15 16:25:47,128 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:26:28,569 - INFO - 📝 收到更新请求: entry_id=603687
2025-07-15 16:26:28,590 - INFO - 🔍 通过internal_id=603687未找到记录，尝试通过external_id查找
2025-07-15 16:26:28,615 - INFO - 📋 找到记录: internal_id=145180, external_id=603687
2025-07-15 16:26:28,634 - INFO - ✅ 更新成功: internal_id=145180, external_id=603687
2025-07-15 16:26:28,739 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:34:34,098 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:34:34,108 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.005秒
2025-07-15 16:34:34,416 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:34:34,420 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:34:34,448 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:34:34,453 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:34:34,469 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:34:34,474 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:34:34,519 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:34:34,524 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:34:34,546 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:34:34,556 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:34:49,767 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:34:49,769 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:34:49,770 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 34, 49, 770452)}
2025-07-15 16:34:49,779 - INFO - ✅ 数据已插入到entries表: entry_id=145954
2025-07-15 16:34:49,783 - INFO - ✅ 触发器已创建队列项: queue_id=300
2025-07-15 16:34:49,890 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-15 16:34:52,947 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:34:52,947 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:34:52,948 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 34, 52, 948073)}
2025-07-15 16:34:52,957 - INFO - ✅ 数据已插入到entries表: entry_id=145955
2025-07-15 16:34:52,960 - INFO - ✅ 触发器已创建队列项: queue_id=301
2025-07-15 16:34:53,067 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 16:34:55,262 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:34:55,263 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:34:55,263 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 34, 55, 263230)}
2025-07-15 16:34:55,276 - INFO - ✅ 数据已插入到entries表: entry_id=145956
2025-07-15 16:34:55,281 - INFO - ✅ 触发器已创建队列项: queue_id=302
2025-07-15 16:34:55,388 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-15 16:34:57,287 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:34:57,287 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:34:57,287 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 34, 57, 287389)}
2025-07-15 16:34:57,352 - INFO - ✅ 数据已插入到entries表: entry_id=145957
2025-07-15 16:34:57,356 - INFO - ✅ 触发器已创建队列项: queue_id=303
2025-07-15 16:34:57,463 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-15 16:34:59,053 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:34:59,053 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:34:59,053 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 34, 59, 53624)}
2025-07-15 16:34:59,062 - INFO - ✅ 数据已插入到entries表: entry_id=145958
2025-07-15 16:34:59,065 - INFO - ✅ 触发器已创建队列项: queue_id=304
2025-07-15 16:34:59,179 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.010秒
2025-07-15 16:35:01,107 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:01,107 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:01,107 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 1, 107804)}
2025-07-15 16:35:01,120 - INFO - ✅ 数据已插入到entries表: entry_id=145959
2025-07-15 16:35:01,124 - INFO - ✅ 触发器已创建队列项: queue_id=305
2025-07-15 16:35:01,230 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 16:35:02,858 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:02,858 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:02,858 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 2, 858707)}
2025-07-15 16:35:02,869 - INFO - ✅ 数据已插入到entries表: entry_id=145960
2025-07-15 16:35:02,873 - INFO - ✅ 触发器已创建队列项: queue_id=306
2025-07-15 16:35:02,983 - INFO - 📊 Entries查询完成: 16条记录, 耗时: 0.003秒
2025-07-15 16:35:04,474 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:04,474 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:04,475 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 4, 475111)}
2025-07-15 16:35:04,489 - INFO - ✅ 数据已插入到entries表: entry_id=145961
2025-07-15 16:35:04,494 - INFO - ✅ 触发器已创建队列项: queue_id=307
2025-07-15 16:35:04,601 - INFO - 📊 Entries查询完成: 17条记录, 耗时: 0.003秒
2025-07-15 16:35:06,216 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:06,216 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:06,216 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 6, 216825)}
2025-07-15 16:35:06,225 - INFO - ✅ 数据已插入到entries表: entry_id=145962
2025-07-15 16:35:06,228 - INFO - ✅ 触发器已创建队列项: queue_id=308
2025-07-15 16:35:06,339 - INFO - 📊 Entries查询完成: 18条记录, 耗时: 0.003秒
2025-07-15 16:35:07,939 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:07,939 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:07,939 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 7, 939283)}
2025-07-15 16:35:07,951 - INFO - ✅ 数据已插入到entries表: entry_id=145963
2025-07-15 16:35:07,956 - INFO - ✅ 触发器已创建队列项: queue_id=309
2025-07-15 16:35:08,062 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.003秒
2025-07-15 16:35:09,733 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:35:09,733 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:35:09,733 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 1, 'item': 1, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 35, 9, 733459)}
2025-07-15 16:35:09,743 - INFO - ✅ 数据已插入到entries表: entry_id=145964
2025-07-15 16:35:09,746 - INFO - ✅ 触发器已创建队列项: queue_id=310
2025-07-15 16:35:09,853 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:19,501 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:19,515 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:19,519 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:19,609 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:19,731 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:19,736 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:20,357 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:20,367 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:20,372 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:20,465 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:20,583 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:20,587 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:21,120 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:21,131 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:21,135 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:21,228 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:21,348 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:21,352 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:21,818 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:21,828 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:21,833 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:21,926 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:22,047 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:22,052 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:22,274 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:22,287 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:22,291 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:22,385 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:22,503 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:22,507 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:22,985 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:23,016 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:23,027 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:23,103 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:23,239 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:23,243 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:24,051 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:24,062 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:24,067 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:24,159 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:24,279 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:24,284 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:35:54,298 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:54,308 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:35:54,313 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:35:54,406 - INFO - 📊 Entries查询完成: 20条记录, 耗时: 0.003秒
2025-07-15 16:35:54,524 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:35:54,528 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:37:38,348 - INFO - 🗑️ 收到删除请求: entry_id=603721
2025-07-15 16:37:38,376 - INFO - 🔍 通过internal_id=603721未找到记录，尝试通过external_id查找
2025-07-15 16:37:38,398 - INFO - 📋 找到记录: internal_id=145954, external_id=603721
2025-07-15 16:37:38,540 - INFO - ✅ HTTP-only模式删除成功: internal_id=145954, external_id=603721
2025-07-15 16:37:40,657 - INFO - 📊 Entries查询完成: 19条记录, 耗时: 0.003秒
2025-07-15 16:38:23,333 - INFO - 🗑️ 收到删除请求: entry_id=603722
2025-07-15 16:38:23,350 - INFO - 🔍 通过internal_id=603722未找到记录，尝试通过external_id查找
2025-07-15 16:38:23,366 - INFO - 📋 找到记录: internal_id=145955, external_id=603722
2025-07-15 16:38:23,505 - INFO - ✅ HTTP-only模式删除成功: internal_id=145955, external_id=603722
2025-07-15 16:38:24,534 - INFO - 📊 Entries查询完成: 18条记录, 耗时: 0.003秒
2025-07-15 16:38:27,545 - INFO - 🗑️ 收到删除请求: entry_id=603723
2025-07-15 16:38:27,565 - INFO - 🔍 通过internal_id=603723未找到记录，尝试通过external_id查找
2025-07-15 16:38:27,585 - INFO - 📋 找到记录: internal_id=145956, external_id=603723
2025-07-15 16:38:27,730 - INFO - ✅ HTTP-only模式删除成功: internal_id=145956, external_id=603723
2025-07-15 16:38:28,845 - INFO - 📊 Entries查询完成: 17条记录, 耗时: 0.003秒
2025-07-15 16:38:32,083 - INFO - 🗑️ 收到删除请求: entry_id=603724
2025-07-15 16:38:32,114 - INFO - 🔍 通过internal_id=603724未找到记录，尝试通过external_id查找
2025-07-15 16:38:32,144 - INFO - 📋 找到记录: internal_id=145957, external_id=603724
2025-07-15 16:38:32,288 - INFO - ✅ HTTP-only模式删除成功: internal_id=145957, external_id=603724
2025-07-15 16:38:33,088 - INFO - 📊 Entries查询完成: 16条记录, 耗时: 0.004秒
2025-07-15 16:38:36,199 - INFO - 🗑️ 收到删除请求: entry_id=603725
2025-07-15 16:38:36,217 - INFO - 🔍 通过internal_id=603725未找到记录，尝试通过external_id查找
2025-07-15 16:38:36,232 - INFO - 📋 找到记录: internal_id=145958, external_id=603725
2025-07-15 16:38:36,375 - INFO - ✅ HTTP-only模式删除成功: internal_id=145958, external_id=603725
2025-07-15 16:38:37,268 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-15 16:38:40,084 - INFO - 🗑️ 收到删除请求: entry_id=603726
2025-07-15 16:38:40,109 - INFO - 🔍 通过internal_id=603726未找到记录，尝试通过external_id查找
2025-07-15 16:38:40,127 - INFO - 📋 找到记录: internal_id=145959, external_id=603726
2025-07-15 16:38:40,261 - INFO - ✅ HTTP-only模式删除成功: internal_id=145959, external_id=603726
2025-07-15 16:38:41,097 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-15 16:38:43,538 - INFO - 🗑️ 收到删除请求: entry_id=603727
2025-07-15 16:38:43,554 - INFO - 🔍 通过internal_id=603727未找到记录，尝试通过external_id查找
2025-07-15 16:38:43,568 - INFO - 📋 找到记录: internal_id=145960, external_id=603727
2025-07-15 16:38:43,695 - INFO - ✅ HTTP-only模式删除成功: internal_id=145960, external_id=603727
2025-07-15 16:38:44,733 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-15 16:38:47,435 - INFO - 🗑️ 收到删除请求: entry_id=603728
2025-07-15 16:38:47,464 - INFO - 🔍 通过internal_id=603728未找到记录，尝试通过external_id查找
2025-07-15 16:38:47,494 - INFO - 📋 找到记录: internal_id=145961, external_id=603728
2025-07-15 16:38:47,654 - INFO - ✅ HTTP-only模式删除成功: internal_id=145961, external_id=603728
2025-07-15 16:38:48,417 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-15 16:38:50,697 - INFO - 🗑️ 收到删除请求: entry_id=603729
2025-07-15 16:38:50,711 - INFO - 🔍 通过internal_id=603729未找到记录，尝试通过external_id查找
2025-07-15 16:38:50,723 - INFO - 📋 找到记录: internal_id=145962, external_id=603729
2025-07-15 16:38:50,849 - INFO - ✅ HTTP-only模式删除成功: internal_id=145962, external_id=603729
2025-07-15 16:38:51,825 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 16:38:56,418 - INFO - 🗑️ 收到删除请求: entry_id=603730
2025-07-15 16:38:56,438 - INFO - 🔍 通过internal_id=603730未找到记录，尝试通过external_id查找
2025-07-15 16:38:56,454 - INFO - 📋 找到记录: internal_id=145963, external_id=603730
2025-07-15 16:38:56,595 - INFO - ✅ HTTP-only模式删除成功: internal_id=145963, external_id=603730
2025-07-15 16:38:57,596 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-15 16:39:00,408 - INFO - 🗑️ 收到删除请求: entry_id=603731
2025-07-15 16:39:00,421 - INFO - 🔍 通过internal_id=603731未找到记录，尝试通过external_id查找
2025-07-15 16:39:00,433 - INFO - 📋 找到记录: internal_id=145964, external_id=603731
2025-07-15 16:39:00,566 - INFO - ✅ HTTP-only模式删除成功: internal_id=145964, external_id=603731
2025-07-15 16:39:01,556 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:40:24,353 - INFO - 🗑️ 收到删除请求: entry_id=603719
2025-07-15 16:40:24,376 - INFO - 🔍 通过internal_id=603719未找到记录，尝试通过external_id查找
2025-07-15 16:40:24,398 - INFO - 📋 找到记录: internal_id=145952, external_id=603719
2025-07-15 16:40:24,532 - INFO - ✅ HTTP-only模式删除成功: internal_id=145952, external_id=603719
2025-07-15 16:40:25,824 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.011秒
2025-07-15 16:43:33,338 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:43:33,338 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:43:33,338 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 8.0, 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': 0, 'item': 7, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 43, 33, 338699)}
2025-07-15 16:43:33,350 - INFO - ✅ 数据已插入到entries表: entry_id=145965
2025-07-15 16:43:33,353 - INFO - ✅ 触发器已创建队列项: queue_id=335
2025-07-15 16:43:33,459 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:43:42,288 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:43:42,300 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:43:42,304 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:43:42,395 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:43:42,517 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:43:42,522 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:49:09,548 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 16:49:09,548 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:49:11,150 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 16:49:11,151 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 16:49:11,431 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:49:11,431 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 16:49:27,605 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:49:27,618 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 16:49:27,625 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.007秒
2025-07-15 16:49:27,977 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:49:27,981 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:49:28,026 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:49:28,031 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:49:28,040 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-15 16:49:28,045 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:49:28,082 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:49:28,086 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:49:28,125 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:49:28,133 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:49:38,495 - INFO - 📝 收到更新请求: entry_id=603732
2025-07-15 16:49:38,518 - INFO - 🔍 通过internal_id=603732未找到记录，尝试通过external_id查找
2025-07-15 16:49:38,536 - INFO - 📋 找到记录: internal_id=145965, external_id=603732
2025-07-15 16:49:38,561 - INFO - ✅ 更新成功: internal_id=145965, external_id=603732
2025-07-15 16:49:38,667 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.002秒
2025-07-15 16:51:09,962 - INFO - 📝 收到更新请求: entry_id=603732
2025-07-15 16:51:09,983 - INFO - 🔍 通过internal_id=603732未找到记录，尝试通过external_id查找
2025-07-15 16:51:10,002 - INFO - 📋 找到记录: internal_id=145965, external_id=603732
2025-07-15 16:51:10,028 - INFO - ✅ 更新成功: internal_id=145965, external_id=603732
2025-07-15 16:51:10,135 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 16:52:07,383 - INFO - 📝 收到更新请求: entry_id=603732
2025-07-15 16:52:07,399 - INFO - 🔍 通过internal_id=603732未找到记录，尝试通过external_id查找
2025-07-15 16:52:07,415 - INFO - 📋 找到记录: internal_id=145965, external_id=603732
2025-07-15 16:52:07,431 - INFO - ✅ 更新成功: internal_id=145965, external_id=603732
2025-07-15 16:52:07,537 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.003秒
2025-07-15 16:52:44,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:52:44,901 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 16:52:44,902 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 11), 'employee_id': '215829', 'duration': 8.0, 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': 0, 'item': 7, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 16, 52, 44, 902449)}
2025-07-15 16:52:44,913 - INFO - ✅ 数据已插入到entries表: entry_id=145966
2025-07-15 16:52:44,917 - INFO - ✅ 触发器已创建队列项: queue_id=339
2025-07-15 16:52:45,023 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 16:53:53,898 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:53:53,913 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 16:53:53,918 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:53:54,011 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.007秒
2025-07-15 16:53:54,130 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:53:54,134 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:55:56,388 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:55:56,395 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 16:55:56,691 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 16:55:56,696 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:55:56,722 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 16:55:56,726 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:55:56,780 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 16:55:56,784 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 16:55:56,798 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:55:56,802 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:55:56,881 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 16:55:56,886 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 16:56:04,486 - INFO - 📝 收到更新请求: entry_id=603733
2025-07-15 16:56:04,506 - INFO - 🔍 通过internal_id=603733未找到记录，尝试通过external_id查找
2025-07-15 16:56:04,522 - INFO - 📋 找到记录: internal_id=145966, external_id=603733
2025-07-15 16:56:04,539 - INFO - ✅ 更新成功: internal_id=145966, external_id=603733
2025-07-15 16:56:04,646 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 16:59:45,277 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 16:59:45,281 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:50,514 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 16:59:50,514 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 16:59:50,718 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:59:50,718 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 17:00:14,392 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:00:14,397 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 17:00:14,403 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.006秒
2025-07-15 17:00:14,744 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:00:14,755 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:00:14,812 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:00:14,816 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:00:14,825 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:00:14,830 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:00:14,846 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:00:14,850 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:00:14,888 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:00:14,897 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:00:34,800 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:00:34,802 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 17:00:34,803 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 17, 0, 34, 803954)}
2025-07-15 17:00:34,813 - INFO - ✅ 数据已插入到entries表: entry_id=145967
2025-07-15 17:00:34,816 - INFO - ✅ 触发器已创建队列项: queue_id=341
2025-07-15 17:00:34,922 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 17:07:58,827 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 17:07:58,827 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:08:03,894 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 17:08:03,894 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 17:08:04,126 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:08:04,126 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 17:08:15,294 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:08:15,299 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 17:08:15,307 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.008秒
2025-07-15 17:08:15,654 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:08:15,658 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:08:15,703 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:08:15,708 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:08:15,717 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:08:15,721 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:08:15,757 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:08:15,762 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:08:15,795 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:08:15,804 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:08:23,454 - INFO - 🗑️ 收到删除请求: entry_id=603734
2025-07-15 17:08:23,473 - INFO - 🔍 通过internal_id=603734未找到记录，尝试通过external_id查找
2025-07-15 17:08:23,489 - INFO - 📋 找到记录: internal_id=145967, external_id=603734
2025-07-15 17:08:23,627 - INFO - ✅ HTTP-only模式删除成功: internal_id=145967, external_id=603734
2025-07-15 17:08:25,174 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.004秒
2025-07-15 17:08:43,291 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:08:43,293 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 17:08:43,294 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 17, 8, 43, 294554)}
2025-07-15 17:08:43,303 - INFO - ✅ 数据已插入到entries表: entry_id=145968
2025-07-15 17:08:43,306 - INFO - ✅ 触发器已创建队列项: queue_id=344
2025-07-15 17:08:43,413 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 17:10:19,712 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 17:10:19,716 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:25,617 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 17:10:25,617 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 17:10:25,840 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:10:25,840 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 17:10:43,318 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:10:43,323 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 17:10:43,329 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.006秒
2025-07-15 17:10:43,664 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:10:43,668 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:10:43,713 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:10:43,717 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:10:43,729 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:10:43,733 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:10:43,766 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:10:43,770 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:10:43,816 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:10:43,825 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:10:50,189 - INFO - 🗑️ 收到删除请求: entry_id=603735
2025-07-15 17:10:50,216 - INFO - 🔍 通过internal_id=603735未找到记录，尝试通过external_id查找
2025-07-15 17:10:50,243 - INFO - 📋 找到记录: internal_id=145968, external_id=603735
2025-07-15 17:10:50,393 - INFO - ✅ HTTP-only模式删除成功: internal_id=145968, external_id=603735
2025-07-15 17:10:51,440 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-15 17:11:02,191 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:11:02,193 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 17:11:02,207 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 17, 11, 2, 207278)}
2025-07-15 17:11:02,218 - INFO - ✅ 数据已插入到entries表: entry_id=145969
2025-07-15 17:11:02,222 - INFO - ✅ 触发器已创建队列项: queue_id=347
2025-07-15 17:11:02,328 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 17:13:39,006 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:13:39,013 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.002秒
2025-07-15 17:13:39,300 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:13:39,305 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:13:39,332 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:13:39,336 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:13:39,386 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:13:39,391 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:13:39,403 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:13:39,407 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:13:39,488 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:13:39,492 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:13:43,558 - INFO - 🗑️ 收到删除请求: entry_id=603736
2025-07-15 17:13:43,576 - INFO - 🔍 通过internal_id=603736未找到记录，尝试通过external_id查找
2025-07-15 17:13:43,592 - INFO - 📋 找到记录: internal_id=145969, external_id=603736
2025-07-15 17:13:43,725 - INFO - ✅ HTTP-only模式删除成功: internal_id=145969, external_id=603736
2025-07-15 17:13:45,751 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 17:14:26,261 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 17:14:26,266 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:35,924 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 17:14:35,924 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 17:14:36,177 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:14:36,177 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 17:14:47,708 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:14:47,719 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 17:14:47,725 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.006秒
2025-07-15 17:14:48,069 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:14:48,073 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:14:48,116 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:14:48,120 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:14:48,132 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:14:48,136 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:14:48,174 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:14:48,178 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:14:48,219 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:14:48,229 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:15:03,055 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:15:03,058 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 17:15:03,059 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 1.0, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 17, 15, 3, 59434)}
2025-07-15 17:15:03,069 - INFO - ✅ 数据已插入到entries表: entry_id=145970
2025-07-15 17:15:03,072 - INFO - ✅ 触发器已创建队列项: queue_id=350
2025-07-15 17:15:03,179 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 17:15:12,984 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:15:13,018 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:15:13,022 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:15:13,101 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-15 17:15:13,233 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:15:13,238 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:15:17,044 - INFO - 🗑️ 收到删除请求: entry_id=603737
2025-07-15 17:15:17,063 - INFO - 🔍 通过internal_id=603737未找到记录，尝试通过external_id查找
2025-07-15 17:15:17,084 - INFO - 📋 找到记录: internal_id=145970, external_id=603737
2025-07-15 17:15:17,224 - INFO - ✅ HTTP-only模式删除成功: internal_id=145970, external_id=603737
2025-07-15 17:15:20,659 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 17:21:37,719 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 17:21:37,719 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:42,503 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 17:21:42,503 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 17:21:42,732 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:21:42,732 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 17:22:00,346 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:22:00,351 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 17:22:00,357 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.005秒
2025-07-15 17:22:00,681 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:22:00,685 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:22:00,710 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:22:00,715 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:22:00,742 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:22:00,747 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:22:00,784 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:22:00,788 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:22:00,840 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:22:00,848 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:22:13,083 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:22:13,087 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 17:22:13,089 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 17, 22, 13, 89279)}
2025-07-15 17:22:13,099 - INFO - ✅ 数据已插入到entries表: entry_id=145971
2025-07-15 17:22:13,102 - INFO - ✅ 触发器已创建队列项: queue_id=353
2025-07-15 17:22:13,208 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.002秒
2025-07-15 17:32:13,082 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:32:13,096 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.008秒
2025-07-15 17:32:13,389 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:32:13,394 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:32:13,469 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:32:13,473 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:32:13,487 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 2
2025-07-15 17:32:13,491 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 17:32:13,525 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:32:13,530 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:32:13,569 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 17:32:13,578 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 17:32:18,262 - INFO - 🗑️ 收到删除请求: entry_id=603738
2025-07-15 17:32:18,283 - INFO - 🔍 通过internal_id=603738未找到记录，尝试通过external_id查找
2025-07-15 17:32:18,299 - INFO - 📋 找到记录: internal_id=145971, external_id=603738
2025-07-15 17:32:18,440 - INFO - ✅ HTTP-only模式删除成功: internal_id=145971, external_id=603738
2025-07-15 17:32:19,951 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-15 17:32:28,898 - INFO - 📝 收到更新请求: entry_id=603732
2025-07-15 17:32:28,914 - INFO - 🔍 通过internal_id=603732未找到记录，尝试通过external_id查找
2025-07-15 17:32:28,929 - INFO - 📋 找到记录: internal_id=145965, external_id=603732
2025-07-15 17:32:28,946 - INFO - ✅ 更新成功: internal_id=145965, external_id=603732
2025-07-15 17:32:29,055 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-16 07:42:51,492 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 07:42:51,492 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 07:42:51,493 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': None, 'number': None, 'factory_number': None, 'project_number': 'TEST001', 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 7, 42, 51, 493010)}
2025-07-16 07:42:51,501 - INFO - ✅ 数据已插入到entries表: entry_id=145973
2025-07-16 07:42:51,504 - INFO - ✅ 触发器已创建队列项: queue_id=359
2025-07-16 07:42:54,509 - INFO - 📝 收到更新请求: entry_id=145973
2025-07-16 07:42:54,743 - INFO - 📋 找到记录: internal_id=145973, external_id=None
2025-07-16 07:42:54,767 - INFO - ✅ 更新成功: internal_id=145973, external_id=None
2025-07-16 07:42:54,768 - INFO - 🗑️ 收到删除请求: entry_id=145973
2025-07-16 07:42:54,787 - INFO - 📋 找到记录: internal_id=145973, external_id=None
2025-07-16 07:42:54,806 - INFO - ✅ HTTP-only模式删除成功: internal_id=145973, external_id=None
2025-07-16 07:43:35,663 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 07:43:35,664 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 07:43:35,664 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.5, 'model': None, 'number': None, 'factory_number': None, 'project_number': 'TEST001', 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 7, 43, 35, 664246)}
2025-07-16 07:43:35,673 - INFO - ✅ 数据已插入到entries表: entry_id=145974
2025-07-16 07:43:35,676 - INFO - ✅ 触发器已创建队列项: queue_id=362
2025-07-16 07:43:38,800 - INFO - 📝 收到更新请求: entry_id=145974
2025-07-16 07:43:38,823 - INFO - 📋 找到记录: internal_id=145974, external_id=None
2025-07-16 07:43:38,842 - INFO - ✅ 更新成功: internal_id=145974, external_id=None
2025-07-16 07:43:38,859 - INFO - 🗑️ 收到删除请求: entry_id=145974
2025-07-16 07:43:38,874 - INFO - 📋 找到记录: internal_id=145974, external_id=None
2025-07-16 07:43:38,893 - INFO - ✅ HTTP-only模式删除成功: internal_id=145974, external_id=None
2025-07-16 07:48:07,085 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:07,094 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.004秒
2025-07-16 07:48:07,377 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 07:48:07,382 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:07,410 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 07:48:07,416 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:07,470 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 07:48:07,474 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:07,503 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 07:48:07,508 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 07:48:07,564 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 07:48:07,595 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 07:48:10,069 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 07:48:10,085 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 07:48:10,086 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 07:48:10,087 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 07:48:10,088 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 07:48:10,091 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 07:48:10,174 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 07:48:10,300 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 07:48:10,305 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:10,487 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:10,497 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 07:48:10,502 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 07:48:10,594 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 07:48:10,714 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 07:48:10,718 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:03:55,393 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:03:55,393 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:03:55,393 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 3, 55, 393449)}
2025-07-16 08:03:55,402 - INFO - ✅ 数据已插入到entries表: entry_id=145975
2025-07-16 08:03:55,405 - INFO - ✅ 触发器已创建队列项: queue_id=365
2025-07-16 08:03:55,631 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.123秒
2025-07-16 08:09:59,819 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:09:59,820 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:09:59,820 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 9, 59, 820130)}
2025-07-16 08:09:59,829 - INFO - ✅ 数据已插入到entries表: entry_id=145976
2025-07-16 08:09:59,832 - INFO - ✅ 触发器已创建队列项: queue_id=366
2025-07-16 08:10:00,036 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.099秒
2025-07-16 08:10:45,993 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:10:45,993 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:10:45,993 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 10, 45, 993482)}
2025-07-16 08:10:46,002 - INFO - ✅ 数据已插入到entries表: entry_id=145977
2025-07-16 08:10:46,005 - INFO - ✅ 触发器已创建队列项: queue_id=367
2025-07-16 08:10:46,112 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 08:26:23,265 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 08:26:23,265 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:26:29,066 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 08:26:29,066 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 08:26:29,275 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:26:29,275 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 08:27:03,180 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:27:03,184 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 08:27:03,191 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.007秒
2025-07-16 08:27:03,530 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:27:03,534 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:27:03,560 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:27:03,565 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:27:03,594 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:27:03,599 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:27:03,634 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:27:03,639 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:27:03,671 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:27:03,682 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:27:16,115 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:27:16,117 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:27:16,118 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 27, 16, 118870)}
2025-07-16 08:27:16,127 - INFO - ✅ 数据已插入到entries表: entry_id=145979
2025-07-16 08:27:16,130 - INFO - ✅ 触发器已创建队列项: queue_id=369
2025-07-16 08:27:16,237 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-16 08:28:07,222 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:28:07,223 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:28:07,223 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 28, 7, 223165)}
2025-07-16 08:28:07,232 - INFO - ✅ 数据已插入到entries表: entry_id=145980
2025-07-16 08:28:07,235 - INFO - ✅ 触发器已创建队列项: queue_id=370
2025-07-16 08:28:07,342 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.004秒
2025-07-16 08:34:14,757 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 08:34:14,757 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:34:19,594 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 08:34:19,594 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 08:34:19,796 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:34:19,796 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 08:37:53,560 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:53,565 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 08:37:53,572 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.006秒
2025-07-16 08:37:53,895 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:53,899 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:53,927 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:53,932 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:53,975 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:53,981 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:53,999 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:37:54,004 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:37:54,058 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:37:54,067 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:37:56,055 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:37:56,071 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:37:56,076 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:37:56,163 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-16 08:37:56,292 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 08:37:56,296 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:56,539 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:56,549 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:56,553 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:56,646 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-16 08:37:56,764 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:37:56,768 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:37:57,414 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 08:37:57,425 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:37:57,426 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:37:57,428 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:37:57,428 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:37:57,431 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 08:37:57,519 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 08:37:57,641 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:57,646 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:58,001 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:58,011 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:37:58,015 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:37:58,108 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-16 08:37:58,227 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:37:58,232 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:38:12,251 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:38:12,253 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 08:38:12,254 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 8, 38, 12, 254832)}
2025-07-16 08:38:12,263 - INFO - ✅ 数据已插入到entries表: entry_id=145981
2025-07-16 08:38:12,267 - INFO - ✅ 触发器已创建队列项: queue_id=371
2025-07-16 08:38:12,380 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.010秒
2025-07-16 08:44:21,464 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:21,476 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.007秒
2025-07-16 08:44:21,753 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:44:21,759 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:21,798 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:44:21,802 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:21,844 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:44:21,849 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:21,868 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:44:21,873 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:44:21,903 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:44:21,913 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:44:29,194 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:44:29,210 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:44:29,215 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:44:29,302 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-16 08:44:29,427 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 08:44:29,432 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:30,780 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:30,791 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:44:30,795 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:44:30,887 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-16 08:44:31,006 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 08:44:31,010 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 08:44:31,600 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 08:44:31,610 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:44:31,611 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:44:31,612 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:44:31,613 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 08:44:31,616 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 08:44:31,706 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 08:44:31,826 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 08:44:31,830 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 08:49:42,195 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 08:49:42,195 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 08:58:42,529 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 08:58:42,529 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 08:58:42,741 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 08:58:42,741 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 09:10:30,160 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:10:30,165 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 09:10:30,173 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.008秒
2025-07-16 09:10:30,860 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 09:10:30,866 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:10:30,943 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:10:30,947 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:10:30,966 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 09:10:30,970 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:10:31,006 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 25, 不匹配: 4
2025-07-16 09:10:31,010 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:10:31,077 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:10:31,082 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:10:40,726 - INFO - 🗑️ 收到删除请求: entry_id=603791
2025-07-16 09:10:40,745 - INFO - 🔍 通过internal_id=603791未找到记录，尝试通过external_id查找
2025-07-16 09:10:40,763 - INFO - 📋 找到记录: internal_id=145981, external_id=603791
2025-07-16 09:10:40,906 - INFO - ✅ HTTP-only模式删除成功: internal_id=145981, external_id=603791
2025-07-16 09:10:41,952 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.004秒
2025-07-16 09:10:45,705 - INFO - 🗑️ 收到删除请求: entry_id=603792
2025-07-16 09:10:45,735 - INFO - 🔍 通过internal_id=603792未找到记录，尝试通过external_id查找
2025-07-16 09:10:45,757 - INFO - 📋 找到记录: internal_id=145980, external_id=603792
2025-07-16 09:10:45,906 - INFO - ✅ HTTP-only模式删除成功: internal_id=145980, external_id=603792
2025-07-16 09:10:47,014 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-16 09:10:50,076 - INFO - 🗑️ 收到删除请求: entry_id=603790
2025-07-16 09:10:50,092 - INFO - 🔍 通过internal_id=603790未找到记录，尝试通过external_id查找
2025-07-16 09:10:50,108 - INFO - 📋 找到记录: internal_id=145979, external_id=603790
2025-07-16 09:10:50,254 - INFO - ✅ HTTP-only模式删除成功: internal_id=145979, external_id=603790
2025-07-16 09:10:51,345 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.004秒
2025-07-16 09:10:55,887 - INFO - 🗑️ 收到删除请求: entry_id=603757
2025-07-16 09:10:55,903 - INFO - 🔍 通过internal_id=603757未找到记录，尝试通过external_id查找
2025-07-16 09:10:55,920 - INFO - 📋 找到记录: internal_id=145978, external_id=603757
2025-07-16 09:10:56,051 - INFO - ✅ HTTP-only模式删除成功: internal_id=145978, external_id=603757
2025-07-16 09:10:57,071 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 09:11:00,151 - INFO - 🗑️ 收到删除请求: entry_id=603756
2025-07-16 09:11:00,173 - INFO - 🔍 通过internal_id=603756未找到记录，尝试通过external_id查找
2025-07-16 09:11:00,197 - INFO - 📋 找到记录: internal_id=145977, external_id=603756
2025-07-16 09:11:00,340 - INFO - ✅ HTTP-only模式删除成功: internal_id=145977, external_id=603756
2025-07-16 09:11:01,390 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-16 09:11:04,111 - INFO - 🗑️ 收到删除请求: entry_id=603755
2025-07-16 09:11:04,134 - INFO - 🔍 通过internal_id=603755未找到记录，尝试通过external_id查找
2025-07-16 09:11:04,155 - INFO - 📋 找到记录: internal_id=145976, external_id=603755
2025-07-16 09:11:04,302 - INFO - ✅ HTTP-only模式删除成功: internal_id=145976, external_id=603755
2025-07-16 09:11:05,255 - INFO - 📊 Entries查询完成: 9条记录, 耗时: 0.009秒
2025-07-16 09:11:08,217 - INFO - 🗑️ 收到删除请求: entry_id=603749
2025-07-16 09:11:08,244 - INFO - 🔍 通过internal_id=603749未找到记录，尝试通过external_id查找
2025-07-16 09:11:08,271 - INFO - 📋 找到记录: internal_id=145975, external_id=603749
2025-07-16 09:11:08,425 - INFO - ✅ HTTP-only模式删除成功: internal_id=145975, external_id=603749
2025-07-16 09:11:09,615 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:14:50,195 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:50,204 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:14:50,506 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:50,510 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:50,555 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:50,560 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:50,574 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:50,579 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:50,603 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:14:50,609 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:14:50,663 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:14:50,667 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:14:52,617 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:52,627 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:52,633 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:52,725 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:14:52,845 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:14:52,849 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:14:58,803 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 09:14:58,814 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:14:58,815 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:14:58,816 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:14:58,817 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:14:58,819 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 09:14:58,909 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 09:14:59,028 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:59,033 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:59,400 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:59,409 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:14:59,413 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:14:59,507 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:14:59,625 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:14:59,629 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:00,345 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,358 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:00,363 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,452 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:00,574 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:00,578 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:00,604 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,617 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:00,622 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,713 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:00,795 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,805 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:00,809 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,838 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:00,843 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:00,903 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:00,941 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:00,951 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:00,955 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,020 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,024 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:01,048 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:01,167 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,172 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:01,247 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,257 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:01,263 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,354 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:01,436 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,446 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:01,450 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,487 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,492 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:01,543 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:01,583 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,593 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:01,597 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,661 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,666 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:01,690 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:01,751 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,761 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:01,766 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,809 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,813 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:01,859 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:01,933 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,944 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:01,951 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:01,978 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:01,984 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:02,041 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:02,111 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:02,122 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:02,127 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:02,163 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:02,168 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:02,219 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:02,325 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:02,335 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:02,339 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:02,357 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:02,362 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:02,432 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:02,550 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:02,554 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:56,083 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:56,091 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.003秒
2025-07-16 09:15:56,380 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:56,386 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:56,414 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:56,420 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:56,472 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:56,477 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:56,496 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:56,500 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:56,578 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:56,583 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:15:58,697 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 09:15:58,706 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:15:58,707 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:15:58,708 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:15:58,709 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 09:15:58,711 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 09:15:58,802 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 09:15:58,920 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:58,924 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:59,182 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:59,192 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 26, 不匹配: 4
2025-07-16 09:15:59,197 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:15:59,289 - INFO - 📊 Entries查询完成: 8条记录, 耗时: 0.002秒
2025-07-16 09:15:59,409 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:15:59,413 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:21:08,694 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:21:08,705 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.006秒
2025-07-16 09:21:08,990 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:21:08,995 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:21:09,028 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:21:09,032 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:21:09,046 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:21:09,051 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:21:09,093 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:21:09,097 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:21:09,125 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:21:09,135 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:21:29,056 - INFO - 🗑️ 收到删除请求: entry_id=603720
2025-07-16 09:21:29,092 - INFO - 🔍 通过internal_id=603720未找到记录，尝试通过external_id查找
2025-07-16 09:21:29,121 - INFO - 📋 找到记录: internal_id=145953, external_id=603720
2025-07-16 09:21:29,275 - INFO - ✅ HTTP-only模式删除成功: internal_id=145953, external_id=603720
2025-07-16 09:21:30,284 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-16 09:22:01,746 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:01,753 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.003秒
2025-07-16 09:22:02,049 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:22:02,053 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:02,081 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:22:02,086 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:02,155 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:22:02,160 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:22:02,253 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:22:02,257 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:02,354 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:22:02,359 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:22:14,707 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:22:14,709 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 09:22:14,710 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 9, 22, 14, 710330)}
2025-07-16 09:22:14,719 - INFO - ✅ 数据已插入到entries表: entry_id=146377
2025-07-16 09:22:14,722 - INFO - ✅ 触发器已创建队列项: queue_id=388
2025-07-16 09:22:14,829 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 09:22:28,651 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:28,662 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:22:28,667 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:22:28,759 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.002秒
2025-07-16 09:22:28,879 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:22:28,884 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:35:16,514 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 09:35:16,515 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 09:35:21,740 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 09:35:21,741 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 09:35:21,925 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:35:21,926 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 09:36:15,665 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:15,670 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 09:36:15,677 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.006秒
2025-07-16 09:36:15,996 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:36:16,001 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:16,045 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:36:16,049 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:16,061 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:36:16,065 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:16,099 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:36:16,104 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:36:16,155 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:36:16,166 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:36:35,743 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:36:35,745 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 09:36:35,746 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.2, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 9, 36, 35, 746814)}
2025-07-16 09:36:35,755 - INFO - ✅ 数据已插入到entries表: entry_id=146378
2025-07-16 09:36:35,758 - INFO - ✅ 触发器已创建队列项: queue_id=389
2025-07-16 09:36:35,865 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 09:36:55,836 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:55,855 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:36:55,860 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:36:55,946 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 09:36:56,071 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:36:56,075 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:37:00,344 - INFO - 🗑️ 收到删除请求: entry_id=603794
2025-07-16 09:37:00,365 - INFO - 🔍 通过internal_id=603794未找到记录，尝试通过external_id查找
2025-07-16 09:37:00,387 - INFO - 📋 找到记录: internal_id=146378, external_id=603794
2025-07-16 09:37:00,527 - INFO - ✅ HTTP-only模式删除成功: internal_id=146378, external_id=603794
2025-07-16 09:37:01,594 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 09:37:04,865 - INFO - 🗑️ 收到删除请求: entry_id=603793
2025-07-16 09:37:04,889 - INFO - 🔍 通过internal_id=603793未找到记录，尝试通过external_id查找
2025-07-16 09:37:04,912 - INFO - 📋 找到记录: internal_id=146377, external_id=603793
2025-07-16 09:37:05,053 - INFO - ✅ HTTP-only模式删除成功: internal_id=146377, external_id=603793
2025-07-16 09:37:06,003 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.002秒
2025-07-16 09:44:41,480 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 09:44:41,480 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 09:44:46,592 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 09:44:46,592 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 09:44:46,799 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:44:46,799 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 09:44:58,673 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:44:58,678 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 09:44:58,684 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.005秒
2025-07-16 09:44:59,010 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:44:59,014 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:44:59,060 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:44:59,064 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:44:59,077 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 29, 不匹配: 1
2025-07-16 09:44:59,083 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:44:59,115 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:44:59,120 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:44:59,162 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:44:59,171 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:45:11,999 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:45:12,002 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 09:45:12,003 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 9, 45, 12, 3699)}
2025-07-16 09:45:12,013 - INFO - ✅ 数据已插入到entries表: entry_id=146379
2025-07-16 09:45:12,017 - INFO - ✅ 触发器已创建队列项: queue_id=394
2025-07-16 09:45:12,124 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.002秒
2025-07-16 09:47:04,885 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:47:04,900 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:47:04,904 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:47:04,994 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 09:47:05,115 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:47:05,120 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:47:08,122 - INFO - 🗑️ 收到删除请求: entry_id=603795
2025-07-16 09:47:08,143 - INFO - 🔍 通过internal_id=603795未找到记录，尝试通过external_id查找
2025-07-16 09:47:08,167 - INFO - 📋 找到记录: internal_id=146379, external_id=603795
2025-07-16 09:47:08,313 - INFO - ✅ HTTP-only模式删除成功: internal_id=146379, external_id=603795
2025-07-16 09:47:09,520 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.009秒
2025-07-16 09:51:12,668 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 09:51:12,668 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 09:51:12,668 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 9, 51, 12, 668538)}
2025-07-16 09:51:12,679 - INFO - ✅ 数据已插入到entries表: entry_id=146380
2025-07-16 09:51:12,682 - INFO - ✅ 触发器已创建队列项: queue_id=397
2025-07-16 09:51:12,801 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.012秒
2025-07-16 09:51:32,299 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:51:32,317 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 09:51:32,322 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 09:51:32,411 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 09:51:32,541 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 09:51:32,546 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 09:53:37,056 - INFO - 📝 收到更新请求: entry_id=603796
2025-07-16 09:53:37,077 - INFO - 🔍 通过internal_id=603796未找到记录，尝试通过external_id查找
2025-07-16 09:53:37,094 - INFO - 📋 找到记录: internal_id=146380, external_id=603796
2025-07-16 09:53:37,111 - INFO - ✅ 更新成功: internal_id=146380, external_id=603796
2025-07-16 09:53:37,220 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 10:07:36,323 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 10:07:36,323 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 10:07:41,479 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 10:07:41,479 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 10:07:41,706 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:07:41,706 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 10:07:51,470 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:07:51,475 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 10:07:51,482 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.007秒
2025-07-16 10:07:51,811 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:07:51,815 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:07:51,842 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:07:51,846 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:07:51,874 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:07:51,878 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:07:51,913 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:07:51,917 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:07:51,964 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:07:51,974 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:08:08,518 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:08:08,520 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:08:08,522 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.2, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 8, 8, 522109)}
2025-07-16 10:08:08,531 - INFO - ✅ 数据已插入到entries表: entry_id=146381
2025-07-16 10:08:08,534 - INFO - ✅ 触发器已创建队列项: queue_id=399
2025-07-16 10:08:08,641 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 10:15:11,532 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:15:11,547 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:15:11,551 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:15:11,641 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.004秒
2025-07-16 10:15:11,771 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:15:11,775 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:25:18,738 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:25:18,752 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 10:25:19,049 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:25:19,054 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:25:19,079 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:25:19,085 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:25:19,100 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:25:19,106 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:25:19,153 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:25:19,158 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:25:19,180 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:25:19,190 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:25:39,215 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:25:39,215 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:25:39,215 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.3, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 25, 39, 215764)}
2025-07-16 10:25:39,226 - INFO - ✅ 数据已插入到entries表: entry_id=146382
2025-07-16 10:25:39,229 - INFO - ✅ 触发器已创建队列项: queue_id=400
2025-07-16 10:25:39,338 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.003秒
2025-07-16 10:49:41,343 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:49:41,354 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.006秒
2025-07-16 10:49:41,633 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:49:41,641 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:49:41,683 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:49:41,688 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:49:41,701 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:49:41,705 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:49:41,739 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:49:41,743 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:49:41,778 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:49:41,787 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:50:10,795 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:50:10,795 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:50:10,795 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 50, 10, 795458)}
2025-07-16 10:50:10,804 - INFO - ✅ 数据已插入到entries表: entry_id=146383
2025-07-16 10:50:10,808 - INFO - ✅ 触发器已创建队列项: queue_id=401
2025-07-16 10:50:56,759 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:50:56,775 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:50:56,780 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:50:56,867 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-16 10:50:56,996 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:50:57,006 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:51:08,354 - INFO - 🗑️ 收到删除请求: entry_id=603796
2025-07-16 10:51:08,385 - INFO - 🔍 通过internal_id=603796未找到记录，尝试通过external_id查找
2025-07-16 10:51:08,402 - INFO - 📋 找到记录: internal_id=146380, external_id=603796
2025-07-16 10:51:08,541 - INFO - ✅ HTTP-only模式删除成功: internal_id=146380, external_id=603796
2025-07-16 10:51:10,009 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.002秒
2025-07-16 10:51:14,586 - INFO - 🗑️ 收到删除请求: entry_id=603797
2025-07-16 10:51:14,610 - INFO - 🔍 通过internal_id=603797未找到记录，尝试通过external_id查找
2025-07-16 10:51:14,635 - INFO - 📋 找到记录: internal_id=146381, external_id=603797
2025-07-16 10:51:14,775 - INFO - ✅ HTTP-only模式删除成功: internal_id=146381, external_id=603797
2025-07-16 10:51:15,996 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 10:51:18,777 - INFO - 🗑️ 收到删除请求: entry_id=603798
2025-07-16 10:51:18,793 - INFO - 🔍 通过internal_id=603798未找到记录，尝试通过external_id查找
2025-07-16 10:51:18,810 - INFO - 📋 找到记录: internal_id=146382, external_id=603798
2025-07-16 10:51:18,943 - INFO - ✅ HTTP-only模式删除成功: internal_id=146382, external_id=603798
2025-07-16 10:51:20,144 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.004秒
2025-07-16 10:51:22,821 - INFO - 🗑️ 收到删除请求: entry_id=603799
2025-07-16 10:51:22,837 - INFO - 🔍 通过internal_id=603799未找到记录，尝试通过external_id查找
2025-07-16 10:51:22,852 - INFO - 📋 找到记录: internal_id=146383, external_id=603799
2025-07-16 10:51:22,995 - INFO - ✅ HTTP-only模式删除成功: internal_id=146383, external_id=603799
2025-07-16 10:51:23,934 - INFO - 📊 Entries查询完成: 10条记录, 耗时: 0.011秒
2025-07-16 10:53:54,761 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:53:54,761 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:53:54,761 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 14), 'employee_id': '215829', 'duration': 9.0, 'model': '', 'number': '', 'factory_number': 'HA0484', 'project_number': '', 'unit_number': '', 'category': 3, 'item': 7, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 53, 54, 761592)}
2025-07-16 10:53:54,770 - INFO - ✅ 数据已插入到entries表: entry_id=146384
2025-07-16 10:53:54,773 - INFO - ✅ 触发器已创建队列项: queue_id=410
2025-07-16 10:56:22,400 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:56:22,410 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.004秒
2025-07-16 10:56:22,700 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:56:22,705 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:56:22,748 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:56:22,754 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:56:22,786 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 28, 不匹配: 1
2025-07-16 10:56:22,793 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:56:22,816 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:56:22,821 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:56:22,878 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:56:22,884 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:56:39,397 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:56:39,397 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:56:39,397 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 56, 39, 397422)}
2025-07-16 10:56:39,406 - INFO - ✅ 数据已插入到entries表: entry_id=146385
2025-07-16 10:56:39,410 - INFO - ✅ 触发器已创建队列项: queue_id=411
2025-07-16 10:57:05,087 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:57:05,102 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 10:57:05,106 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:57:05,194 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 10:57:05,319 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:57:05,325 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:58:57,938 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 10:58:57,942 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 10:58:59,553 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 10:58:59,553 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 10:58:59,774 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:58:59,775 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 10:59:21,408 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:59:21,415 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 10:59:21,421 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 10:59:21,746 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 10:59:21,751 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:59:21,794 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 10:59:21,798 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:59:21,810 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 10:59:21,815 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 10:59:21,849 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:59:21,854 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:59:21,899 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 10:59:21,910 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 10:59:29,098 - INFO - 🗑️ 收到删除请求: entry_id=603801
2025-07-16 10:59:29,119 - INFO - 🔍 通过internal_id=603801未找到记录，尝试通过external_id查找
2025-07-16 10:59:29,135 - INFO - 📋 找到记录: internal_id=146385, external_id=603801
2025-07-16 10:59:29,277 - INFO - ✅ HTTP-only模式删除成功: internal_id=146385, external_id=603801
2025-07-16 10:59:30,270 - INFO - 📊 Entries查询完成: 11条记录, 耗时: 0.003秒
2025-07-16 10:59:45,074 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 10:59:45,076 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 10:59:45,078 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.9, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 10, 59, 45, 78136)}
2025-07-16 10:59:45,088 - INFO - ✅ 数据已插入到entries表: entry_id=146386
2025-07-16 10:59:45,091 - INFO - ✅ 触发器已创建队列项: queue_id=414
2025-07-16 11:00:03,086 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:00:03,101 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:00:03,105 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:00:03,194 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 11:00:03,326 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:00:03,338 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:03:03,678 - ERROR - 获取entry失败: 列"created_at"は存在しません
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/entries_api.py", line 199, in get_entry
    result = await conn.fetchrow("""
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 748, in fetchrow
    data = await self._execute(
           ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 1864, in _execute
    result, _ = await self.__execute(
                ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 1961, in __execute
    result, stmt = await self._do_execute(
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 2004, in _do_execute
    stmt = await self._get_statement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py", line 432, in _get_statement
    statement = await self._protocol.prepare(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 165, in prepare
asyncpg.exceptions.UndefinedColumnError: 列"created_at"は存在しません
2025-07-16 11:03:23,268 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:23,291 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 11:03:23,579 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:03:23,583 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:23,632 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:03:23,637 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:23,665 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:03:23,669 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:23,683 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:03:23,687 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:03:23,767 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:03:23,771 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:03:35,194 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 11:03:35,194 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 11:03:35,194 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.1, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 11, 3, 35, 194558)}
2025-07-16 11:03:35,204 - INFO - ✅ 数据已插入到entries表: entry_id=146387
2025-07-16 11:03:35,207 - INFO - ✅ 触发器已创建队列项: queue_id=415
2025-07-16 11:03:45,006 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:45,019 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:03:45,024 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:03:45,115 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.002秒
2025-07-16 11:03:45,239 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:03:45,243 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:07:16,113 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:16,121 - INFO - 📊 Entries查询完成: 13条记录, 耗时: 0.004秒
2025-07-16 11:07:16,410 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:07:16,414 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:16,441 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:07:16,445 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:16,500 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:07:16,504 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:16,519 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:07:16,523 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:07:16,601 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:07:16,606 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:07:28,806 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 11:07:28,806 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 11:07:28,806 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.5, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 11, 7, 28, 806573)}
2025-07-16 11:07:28,815 - INFO - ✅ 数据已插入到entries表: entry_id=146388
2025-07-16 11:07:28,819 - INFO - ✅ 触发器已创建队列项: queue_id=416
2025-07-16 11:07:28,927 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-16 11:07:42,282 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:42,303 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:07:42,308 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:07:42,394 - INFO - 📊 Entries查询完成: 14条记录, 耗时: 0.003秒
2025-07-16 11:07:42,520 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:07:42,526 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 11:07:58,557 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 11:07:58,557 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-16 11:07:58,557 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 16), 'employee_id': '215829', 'duration': 0.2, 'model': '', 'number': '', 'factory_number': '', 'project_number': '', 'unit_number': '', 'category': 0, 'item': 0, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 16, 11, 7, 58, 557852)}
2025-07-16 11:07:58,567 - INFO - ✅ 数据已插入到entries表: entry_id=146389
2025-07-16 11:07:58,571 - INFO - ✅ 触发器已创建队列项: queue_id=417
2025-07-16 11:07:58,683 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.003秒
2025-07-16 11:13:10,238 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:13:10,255 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 11:13:10,261 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 11:13:10,348 - INFO - 📊 Entries查询完成: 15条记录, 耗时: 0.004秒
2025-07-16 11:13:10,479 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 11:13:10,484 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 13:06:17,148 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 13:06:17,148 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 13:06:22,178 - ERROR - Server5 HTTP服务启动失败: No module named 'app.auth'
2025-07-16 13:06:27,567 - ERROR - Server5 HTTP服务启动失败: No module named 'app.auth'
2025-07-16 16:36:47,093 - ERROR - Server5 HTTP服务启动失败: module 'config' has no attribute 'AUTH_HOST'
2025-07-16 16:39:19,052 - ERROR - Server5 HTTP服务启动失败: PostgreSQLClient.__init__() got an unexpected keyword argument 'host'
2025-07-16 16:44:42,378 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 16:44:42,378 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 16:44:42,727 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:44:43,010 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:44:43,010 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 16:44:43,010 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 16:44:43,014 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:44:43,018 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:45:23,425 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 16:45:23,425 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 16:45:23,676 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:45:23,906 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:45:23,907 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 16:45:45,038 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:45:45,055 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 16:45:45,062 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.007秒
2025-07-16 16:45:45,413 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:45:45,418 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:45:45,472 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:45:45,477 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:45:45,488 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:45:45,493 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:45:45,535 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:45:45,540 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:45:45,562 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:45:45,592 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:45:46,459 - INFO - Redis连接成功
2025-07-16 16:45:46,459 - INFO - 通知网关Redis连接成功
2025-07-16 16:45:46,459 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 16:48:31,213 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 16:49:26,211 - INFO - WebSocket断开: 215829
2025-07-16 16:49:26,211 - INFO - 通知订阅任务已取消: 215829
2025-07-16 16:49:26,211 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 16:49:31,539 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:49:31,561 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:49:32,250 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:49:32,255 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:49:32,346 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:49:32,351 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:49:39,739 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:49:52,502 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 16:49:52,508 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:49:52,512 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:49:54,395 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 16:49:54,395 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 16:49:54,645 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:49:54,884 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:49:54,885 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 16:50:11,753 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:50:11,758 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 16:50:11,765 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 16:50:12,525 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:50:12,535 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:50:12,619 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:50:12,623 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:50:22,890 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:51:03,873 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 16:51:03,877 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:51:03,880 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 16:51:05,687 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 16:51:05,687 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 16:51:05,947 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:51:06,199 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 16:51:06,199 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 16:51:44,169 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:51:44,178 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 16:51:44,185 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.007秒
2025-07-16 16:51:44,935 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:51:44,945 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:51:45,030 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:51:45,035 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:51:50,261 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:53:54,655 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:53:54,669 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:53:55,337 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:53:55,342 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:53:55,436 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:53:55,446 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:01,147 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:01,161 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:54:01,166 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:01,258 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-16 16:54:01,386 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 16:54:01,391 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:02,668 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:02,679 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:54:02,684 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:02,776 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.002秒
2025-07-16 16:54:02,898 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:54:02,904 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:03,710 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:03,722 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:54:03,726 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:03,825 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.010秒
2025-07-16 16:54:03,937 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 16:54:03,941 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:05,178 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:05,190 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 16:54:05,195 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:05,286 - INFO - 📊 Entries查询完成: 6条记录, 耗时: 0.003秒
2025-07-16 16:54:05,412 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 19, 匹配: 30, 不匹配: 0
2025-07-16 16:54:05,419 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:06,853 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:06,865 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:54:06,869 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:06,963 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:54:07,082 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:54:07,087 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:54:07,665 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 16:54:07,677 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 16:54:07,678 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 16:54:07,679 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 16:54:07,680 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 16:54:07,683 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 16:54:07,770 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.001秒
2025-07-16 16:54:07,898 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:54:07,904 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:08,784 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:08,794 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:54:08,798 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:54:08,891 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.002秒
2025-07-16 16:54:09,012 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:54:09,018 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:57:50,155 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 16:58:15,484 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:58:15,493 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 16:58:15,507 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 16:58:15,778 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:58:15,783 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:58:15,811 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:58:15,816 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:58:15,873 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 16:58:15,878 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 16:58:15,892 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:58:15,897 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:58:15,975 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 16:58:15,980 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 16:58:16,826 - INFO - Redis连接成功
2025-07-16 16:58:16,826 - INFO - 通知网关Redis连接成功
2025-07-16 16:58:16,826 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 16:58:25,949 - INFO - WebSocket断开: 215829
2025-07-16 16:58:25,950 - INFO - 通知订阅任务已取消: 215829
2025-07-16 16:58:25,950 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:02:17,509 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:02:38,057 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:38,076 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:02:38,092 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:02:38,373 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:38,377 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:38,404 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:38,408 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:38,448 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:38,452 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:38,471 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:02:38,476 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:02:38,546 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:02:38,550 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:02:39,417 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:02:50,394 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:50,406 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:50,410 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:50,503 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:02:50,624 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:02:50,630 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:02:53,779 - INFO - WebSocket断开: 215829
2025-07-16 17:02:53,780 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:02:53,781 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:02:55,839 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:55,847 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:02:55,871 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:02:56,144 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:56,148 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:56,177 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:56,181 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:56,230 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:02:56,235 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:02:56,250 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:02:56,255 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:02:56,331 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:02:56,336 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:02:57,189 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:03:03,031 - INFO - WebSocket断开: 215829
2025-07-16 17:03:03,032 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:03:03,032 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:04:29,676 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:54,532 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:54,546 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:04:54,561 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:54,831 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:54,835 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:54,873 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:54,878 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:54,914 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:54,918 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:54,937 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:54,942 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:55,014 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:55,019 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:55,372 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:55,620 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:55,625 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:55,652 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:55,659 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:55,719 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:55,724 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:55,911 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:04:56,136 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:56,391 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:56,401 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:56,437 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:56,441 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:56,467 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:56,488 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:56,493 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:56,704 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:56,709 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:56,720 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:56,726 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:56,806 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:56,810 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:56,923 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:57,175 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,180 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,193 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,197 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,216 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:57,279 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:57,284 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:57,447 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,451 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,465 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,470 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,549 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:57,554 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:57,687 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:57,926 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,937 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,966 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:57,973 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:57,991 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:58,019 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:58,023 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:58,233 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:58,238 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:58,252 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:58,258 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:58,331 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:58,335 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:58,455 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:58,697 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:58,703 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:58,723 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:58,727 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:58,747 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:58,797 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:58,801 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:58,997 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,008 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,029 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,034 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,090 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:59,095 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:59,215 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:59,463 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,469 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,482 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,487 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,525 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:04:59,569 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:59,575 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:59,759 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,764 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,774 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:04:59,778 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:04:59,861 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:04:59,866 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:04:59,976 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:00,214 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:00,219 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:00,233 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:00,238 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:00,272 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:00,314 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:00,319 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:00,515 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:00,520 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:00,531 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:00,535 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:00,616 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:00,620 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:00,726 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:00,968 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:00,975 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:00,999 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:01,005 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:01,027 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:01,070 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:01,075 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:01,260 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:01,265 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:01,279 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:01,283 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:01,360 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:01,365 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:01,487 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:01,723 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:01,730 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:01,743 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:01,752 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:01,777 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:01,820 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:01,825 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:02,013 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:02,018 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:02,031 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:02,036 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:02,113 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:02,118 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:02,242 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:02,385 - INFO - WebSocket断开: 215829
2025-07-16 17:05:02,385 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:05:02,385 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:05:46,715 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:46,722 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.002秒
2025-07-16 17:05:46,737 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:05:47,001 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:47,009 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:47,055 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:47,060 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:47,101 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:47,106 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:47,201 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:47,205 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:47,302 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:47,307 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:48,065 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:05:51,010 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 17:05:51,016 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 17:05:51,016 - WARNING - 查询timeprotab表失败，可能分区不存在: リレーション"timeprotab_2508"は存在しません
2025-07-16 17:05:51,018 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
          ... 错误: リレーション"timeprotab_2508"は存在しません
2025-07-16 17:05:51,019 - WARNING - 查询timeprotab显示数据失败: リレーション"timeprotab_2508"は存在しません
2025-07-16 17:05:51,022 - INFO - 📊 Timeprotab查询完成: 0条记录
2025-07-16 17:05:51,115 - INFO - 📊 Entries查询完成: 0条记录, 耗时: 0.002秒
2025-07-16 17:05:51,233 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:51,239 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:51,563 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:51,574 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:05:51,578 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:51,671 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:05:51,792 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:51,798 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:51,992 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:52,010 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:05:52,019 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:05:52,103 - INFO - 📊 Entries查询完成: 22条记录, 耗时: 0.003秒
2025-07-16 17:05:52,237 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-16 17:05:52,242 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:05:54,215 - INFO - WebSocket断开: 215829
2025-07-16 17:05:54,216 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:05:54,216 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:18:07,621 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:18:10,014 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:18:22,220 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 17:18:22,220 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 17:18:22,224 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 17:18:24,013 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 17:18:24,013 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 17:18:24,280 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 17:18:24,491 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 17:18:24,491 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 17:18:42,527 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:18:42,536 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 17:18:42,542 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 17:18:42,560 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:18:42,906 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:18:42,911 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:18:42,962 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:18:42,967 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:18:42,980 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:18:42,985 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:18:43,011 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:18:43,017 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:18:43,082 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:18:43,092 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:18:43,988 - INFO - Redis连接成功
2025-07-16 17:18:43,988 - INFO - 通知网关Redis连接成功
2025-07-16 17:18:43,988 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:18:57,039 - INFO - WebSocket断开: 215829
2025-07-16 17:18:57,040 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:18:57,041 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:21:52,340 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:21:52,354 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.003秒
2025-07-16 17:21:52,371 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:21:52,650 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:21:52,657 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:21:52,700 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:21:52,707 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:21:52,746 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:21:52,752 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:21:52,767 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:21:52,773 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:21:52,842 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:21:52,848 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:21:53,706 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:21:57,684 - INFO - WebSocket断开: 215829
2025-07-16 17:21:57,685 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:21:57,685 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:22:38,626 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-16 17:22:38,629 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 17:22:38,633 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 17:22:40,236 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-16 17:22:40,236 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-16 17:22:40,452 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 17:22:40,669 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 17:22:40,669 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-16 17:22:53,837 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:22:53,843 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-16 17:22:53,850 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 17:22:53,870 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:22:54,227 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:22:54,232 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:22:54,279 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:22:54,284 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:22:54,293 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:22:54,297 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:22:54,332 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:22:54,337 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:22:54,374 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:22:54,383 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:22:55,291 - INFO - Redis连接成功
2025-07-16 17:22:55,291 - INFO - 通知网关Redis连接成功
2025-07-16 17:22:55,291 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:23:15,183 - INFO - WebSocket断开: 215829
2025-07-16 17:23:15,184 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:23:15,184 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
2025-07-16 17:28:31,106 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:28:57,278 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:28:57,289 - INFO - 📊 Entries查询完成: 12条记录, 耗时: 0.006秒
2025-07-16 17:28:57,304 - INFO - ✅ 从auth数据库获取员工信息成功: 215829, name=mike, department=131
2025-07-16 17:28:57,656 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:28:57,660 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:28:57,709 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:28:57,714 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:28:57,724 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 1
2025-07-16 17:28:57,729 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-16 17:28:57,761 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:28:57,766 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:28:57,810 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-16 17:28:57,819 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-16 17:28:58,701 - INFO - 用户通知连接成功: 215829 (总连接数: 1)
2025-07-16 17:29:00,004 - INFO - WebSocket断开: 215829
2025-07-16 17:29:00,005 - INFO - 通知订阅任务已取消: 215829
2025-07-16 17:29:00,005 - INFO - 用户通知连接断开: 215829 (剩余连接数: 0)
