INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server']
INFO:     Uvicorn running on http://0.0.0.0:8003 (Press CTRL+C to quit)
INFO:     Started reloader process [2766498] using WatchFiles
INFO:     Started server process [2766502]
INFO:     Waiting for application startup.
/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server/app/main.py:100: RuntimeWarning: coroutine 'Redis.execute_command' was never awaited
  await redis_service.connect()
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
INFO:     Application startup complete.
非Windows平台，ODBC功能将被禁用
尝试连接MongoDB: ************:27017
MongoDB连接成功: ************:27017
=== Server Starting Up ===
尝试连接Redis: localhost:6379
Redis连接成功: localhost:6379
Testing database connection...
Database connection test successful
✅ Database connection successful!
Database tables initialized successfully
✅ Database initialization complete!
IMDB database tables (add25, change25, del25) initialized successfully
✅ IMDB database tables initialization complete!
✅ Task checker started
✅ XML processor started
🚀 Server startup complete - Running in Full Mode
=== Server Ready ===
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
INFO:     127.0.0.1:39678 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39690 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.Clj33VTXk7s-rfcFpDURYDozmXH2jF72f5yRWVba1zE" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server/app/routers/extra_actions.py:295: RuntimeWarning: coroutine 'Redis.execute_command' was never awaited
  redis_cached = await redis_service.set_sensor_data(sensor_id, sensor_value)
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1NjI4OC4xOTE3ODQ2fQ.m76Fbn_6lamwwAw_GUWVUCVoj9zLnTxl91MNj6-3GwA" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
WebSocket connection closed for user: client_user
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1NjU1MC44NDk2NDI4fQ.5stqpkOc3Cl8WHsbLQeN1WGLJmQkzXGDmlkWSi9hNZE" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1NjY4Ny4yOTQyNzd9.hKw-yAvwqY9-2gikUwtEHfbZe1qA8bA5K-jIyh0yakQ" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1Njc0MC4yMjQ4Njk1fQ.WVac_9hSScQzxj4hQq6kOjtG6qDE8oQp5_l1DHuFYIM" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1NzUxNi4zNjkwMX0.DdcWvahRiVBEOBM80rAKQv2F8TWcRcBd6yoMUpnSYPI" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1NzcwNC45NDY1MTd9.omkFbCLs-zMYq3GKSmnuF6MBgdWtiwWCOIIgqxWhVqI" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1Nzc2Ny41ODY2MzkyfQ.yio-hRw5MWow8c3Kuzl0yDBv7lVGPCHNyw6rXkpMi2w" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     connection closed
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作
WebSocket connection closed for user: client_user
INFO:     127.0.0.1:0 - "GET /api/feature_flags/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "WebSocket /ws/feature_flags?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjbGllbnRfdXNlciIsImV4cCI6MTc1MjY1ODEzMS45MDI3OX0._G63GNbl2bEg-C80DnXSn8WqudAEXxVggIzU4e0UzjE" [accepted]
INFO:     connection open
WebSocket connection established for user: client_user
INFO:     127.0.0.1:0 - "GET / HTTP/1.1" 200 OK
Database connection test successful
INFO:     127.0.0.1:0 - "GET /api/db/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/odbc/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/extra/sensor_data HTTP/1.1" 200 OK
