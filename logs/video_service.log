INFO:     Started server process [2766664]
INFO:     Waiting for application startup.
2025-07-16 16:53:27,865 - app.main - INFO - Starting MySuite Video Monitoring Microservice...
2025-07-16 16:53:27,865 - app.core.services.video_service - INFO - Initializing video service with camera index: 1
2025-07-16 16:53:27,865 - app.core.services.video_service - INFO - Trying backend: 200
[ WARN:0@1.286] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
[ WARN:0@1.286] global cap.cpp:478 open VIDEOIO(V4L2): backend is generally available but can't be used to capture by index
2025-07-16 16:53:27,865 - app.core.services.video_service - ERROR - Failed to open camera with backend 200
2025-07-16 16:53:27,865 - app.core.services.video_service - INFO - Trying backend: 1800
2025-07-16 16:53:27,868 - app.core.services.video_service - ERROR - Failed to open camera with backend 1800
2025-07-16 16:53:27,869 - app.core.services.video_service - INFO - Trying backend: 0
[ WARN:0@1.290] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
2025-07-16 16:53:27,871 - app.core.services.video_service - ERROR - Failed to open camera with backend 0
2025-07-16 16:53:27,871 - app.core.services.video_service - ERROR - All camera backends failed
2025-07-16 16:53:27,871 - app.core.services.video_service - ERROR - Failed to initialize video service
2025-07-16 16:53:27,871 - app.main - WARNING - Failed to initialize video service - camera may not be available
2025-07-16 16:53:27,871 - app.main - INFO - Background tasks started
2025-07-16 16:53:27,871 - app.main - INFO - Video Monitoring Microservice started successfully on port 8007
2025-07-16 16:53:27,871 - app.routers.video_websocket - INFO - Starting video broadcast task
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8007 (Press CTRL+C to quit)
Video Monitoring Service Configuration validated successfully
Service will run on port: 8007
Camera index: 1
Video resolution: 640x480
Starting MySuite Video Monitoring Microservice...
Version: 1.0.0
Host: 0.0.0.0
Port: 8007
Camera Index: 1
Video Resolution: 640x480
Video FPS: 30
--------------------------------------------------
INFO:     127.0.0.1:41970 - "GET /health HTTP/1.1" 404 Not Found
