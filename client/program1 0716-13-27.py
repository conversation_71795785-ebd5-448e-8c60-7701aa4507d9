# client/program1.py
# MySuite 员工操作界面程序 - 独立运行版本
# 2025/06/26.11:50+分离ui操作 - 从client_fixed.py分离出的员工界面程序

import sys
import json
import threading
import asyncio
import requests
import os
import platform  # 修改10 -多平台: 添加平台检测
from pathlib import Path  # 修改10 -多平台: 使用跨平台路径处理
from datetime import datetime, timedelta
from PyQt6 import QtWidgets, QtCore, QtGui
from websockets.client import connect
import urllib3
import uuid  # 250618.18：39加入注册 - 用于生成硬件指纹
import subprocess  # 250618.18：39加入注册 - 用于获取硬件信息
import hashlib  # 250618.18：39加入注册 - 用于哈希计算
import base64  # 250618.19：15 密码记住功能 - 用于密码编码
from cryptography.fernet import Fernet  # 250618.19：15 密码记住功能 - 用于密码加密
import mimetypes
import aiohttp  # 20250708 + Server5 API客户端支持
import weakref  # 用于弱引用管理
import atexit  # 用于程序退出时清理

# 忽略SSL警告（如开发环境自签名证书）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 修改10 -多平台: 不直接导入http_client，而是使用内置实现
# from http_client import AsyncHTTPClient, SyncHTTPClient

# 全局HTTP客户端管理器
class HTTPClientManager:
    """全局HTTP客户端管理器 - 避免重复创建session和连接泄露"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        
        # 连接池管理
        self._sessions = {}  # {base_url: session}
        self._sessions_lock = threading.Lock()
        
        # 注册程序退出时的清理
        atexit.register(self.cleanup_all)
    
    def get_session(self, base_url: str) -> requests.Session:
        """获取或创建指定URL的session"""
        with self._sessions_lock:
            if base_url not in self._sessions:
                session = requests.Session()
                session.trust_env = False
                session.verify = False
                
                # 配置连接池
                adapter = requests.adapters.HTTPAdapter(
                    pool_connections=10,
                    pool_maxsize=20,
                    max_retries=3
                )
                session.mount('http://', adapter)
                session.mount('https://', adapter)
                
                self._sessions[base_url] = session
                print(f"创建新的HTTP session: {base_url}")
            
            return self._sessions[base_url]
    
    def close_session(self, base_url: str):
        """关闭指定URL的session"""
        with self._sessions_lock:
            if base_url in self._sessions:
                self._sessions[base_url].close()
                del self._sessions[base_url]
                print(f"关闭HTTP session: {base_url}")
    
    def cleanup_all(self):
        """清理所有session"""
        with self._sessions_lock:
            for base_url, session in self._sessions.items():
                try:
                    session.close()
                    print(f"清理HTTP session: {base_url}")
                except Exception as e:
                    print(f"清理session时出错 {base_url}: {e}")
            self._sessions.clear()

# 全局HTTP客户端管理器实例
http_client_manager = HTTPClientManager()

# 2025/06/26.11:50+分离ui操作 - 添加必要的平台相关类
class PlatformConfig:
    """跨平台配置管理"""
    
    def __init__(self):
        self.system = platform.system().lower()
        
    def get_default_watch_folder(self):
        """获取默认监控文件夹路径"""
        if self.system == "windows":
            return Path("C:/MySuite/InputExcels")
        else:  # Linux和其他系统
            return Path.home() / "MySuite" / "InputExcels"
    
    def get_default_serial_port(self):
        """获取默认串口"""
        if self.system == "windows":
            return "COM3"
        else:  # Linux
            return "/dev/ttyUSB0"
    
    def get_serial_ports(self):
        """获取可用串口列表"""
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    def create_directories(self, path: Path):
        """创建目录（如果不存在）"""
        path.mkdir(parents=True, exist_ok=True)

class SimpleAsyncHTTPClient(QtCore.QObject):
    """简化的异步HTTP客户端，使用全局连接池"""
    
    request_finished = QtCore.pyqtSignal(str, dict, object)
    
    def __init__(self, base_url: str = "https://localhost"):
        super().__init__()
        self.base_url = base_url
        self.loop: asyncio.AbstractEventLoop = None
        self._closed = False
        
    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """设置事件循环"""
        self.loop = loop
    
    def _make_sync_request(self, method: str, endpoint: str, request_id: str, **kwargs):
        """使用全局管理的session进行同步请求，然后发送信号"""
        if self._closed:
            result = {
                'status_code': 0,
                'data': None,
                'headers': {},
                'ok': False,
                'error': 'Client已关闭'
            }
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result", 
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, result),
                QtCore.Q_ARG(object, Exception("Client已关闭"))
            )
            return
        
        try:
            import threading
            # 使用全局管理的session
            session = http_client_manager.get_session(self.base_url)
            
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = session.get(url, timeout=10, **kwargs)
            elif method.upper() == 'POST':
                response = session.post(url, timeout=10, **kwargs)
            elif method.upper() == 'PUT':
                response = session.put(url, timeout=10, **kwargs)
            elif method.upper() == 'DELETE':
                response = session.delete(url, timeout=10, **kwargs)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            try:
                data = response.json()
            except:
                data = response.text
            
            result = {
                'status_code': response.status_code,
                'data': data,
                'headers': dict(response.headers),
                'ok': response.status_code < 400
            }
            
            # 在主线程中发射信号
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result", 
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, result),
                QtCore.Q_ARG(object, None)
            )
            
        except Exception as e:
            # 20250714+09:30+ 修复异常处理：提供更好的错误信息
            error_result = {
                'status_code': 0,
                'data': None,
                'headers': {},
                'ok': False,
                'error': str(e)
            }
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, error_result),
                QtCore.Q_ARG(object, e)
            )
    
    @QtCore.pyqtSlot(str, dict, object)
    def _emit_result(self, request_id: str, result: dict, error: object):
        """在主线程中发射结果信号"""
        self.request_finished.emit(request_id, result, error)
    
    def post_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步POST请求"""
        import time
        import threading
        if request_id is None:
            request_id = f"post_{int(time.time() * 1000)}"
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('POST', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        
        return request_id
    
    def get_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步GET请求"""
        import threading
        if request_id is None:
            request_id = f"get_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("GET", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    def put_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步PUT请求"""
        import threading
        if request_id is None:
            request_id = f"put_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("PUT", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    def delete_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步DELETE请求"""
        import threading
        if request_id is None:
            request_id = f"delete_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("DELETE", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    async def close(self):
        """关闭客户端"""
        self._closed = True
        print(f"SimpleAsyncHTTPClient已关闭: {self.base_url}")

class SimpleSyncHTTPClient:
    """简化的同步HTTP客户端，使用全局连接池"""
    
    def __init__(self, base_url: str = "https://localhost"):
        self.base_url = base_url
        self._closed = False
    
    def _get_session(self):
        """获取全局管理的session"""
        if self._closed:
            raise RuntimeError("Client已关闭")
        return http_client_manager.get_session(self.base_url)
    
    def post(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步POST请求"""
        if self._closed:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': 'Client已关闭'
            }
        
        try:
            session = self._get_session()
            url = f"{self.base_url}{endpoint}"
            response = session.post(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def get(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步GET请求"""
        if self._closed:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': 'Client已关闭'
            }
        
        try:
            session = self._get_session()
            url = f"{self.base_url}{endpoint}"
            response = session.get(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def close(self):
        """关闭客户端"""
        self._closed = True
        print(f"SimpleSyncHTTPClient已关闭: {self.base_url}")

# 20250708 + Server5 API客户端
class Server5APIClient:
    """Server5 HTTP API客户端 - 使用全局连接池，避免session泄露"""

    def __init__(self, base_url: str = "http://localhost:8009"):
        self.base_url = base_url
        self._closed = False

    def _get_session(self):
        """获取全局管理的session"""
        if self._closed:
            raise RuntimeError("Client已关闭")
        return http_client_manager.get_session(self.base_url)

    def close(self):
        """关闭客户端 - 标记为已关闭但不立即关闭session（由管理器统一管理）"""
        self._closed = True
        print(f"Server5APIClient已关闭: {self.base_url}")

    def get_entries(self, employee_id: str, start_date: str, end_date: str, limit: int = 1000):
        """获取entries数据 - 支持Table3"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/entries/"
            params = {
                "employee_id": employee_id,
                "start_date": start_date,
                "end_date": end_date,
                "limit": limit
            }

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                return {"ok": True, "data": data}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

    def get_available_months(self, employee_id: str):
        """获取可用月份列表 - 支持Chart月份选择，使用正确的API路径"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/chart/months"
            params = {"employee_id": employee_id}

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                # 修复数据格式：保持Server5原有格式，直接传递给客户端
                server5_data = data.get("data", [])
                return {"ok": True, "data": {"months": server5_data}}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

    def get_chart_data(self, employee_id: str, start_date: str, end_date: str, chart_type: str = "daily"):
        """获取图表数据 - 支持Chart显示，使用正确的API路径"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/chart/generate"
            params = {
                "employee_id": employee_id,
                "start_date": start_date,
                "end_date": end_date,
                "chart_type": chart_type
            }

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                return {"ok": True, "data": data.get("data", {})}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

    def get_timeprotab_data(self, employee_id: str, year: int, month: int):
        """20250708 + 从Server5获取timeprotab数据 - 支持Table1"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/timeprotab/"
            params = {
                "employee_id": employee_id,
                "year": year,
                "month": month,
                "limit": 1000
            }

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                return {"ok": True, "data": data.get('data', [])}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

    def get_timeprotab_months(self, employee_id: str):
        """获取timeprotab可用月份列表"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/timeprotab/months"
            params = {"employee_id": employee_id}

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                return {"ok": True, "data": data}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

    def get_timeprotab_stats(self, employee_id: str, year: int, month: int):
        """获取timeprotab统计信息"""
        if self._closed:
            return {"ok": False, "error": "Client已关闭"}
        
        try:
            session = self._get_session()
            url = f"{self.base_url}/api/timeprotab/stats"
            params = {
                "employee_id": employee_id,
                "year": year,
                "month": month
            }

            response = session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                return {"ok": True, "data": data}
            else:
                return {"ok": False, "status_code": response.status_code, "error": response.text}

        except Exception as e:
            return {"ok": False, "error": str(e)}

# 2025/06/26.11:50+分离ui操作 - 添加必要的平台相关类
class PlatformConfig:
    """跨平台配置管理"""
    
    def __init__(self):
        self.system = platform.system().lower()
        
    def get_default_watch_folder(self):
        """获取默认监控文件夹路径"""
        if self.system == "windows":
            return Path("C:/MySuite/InputExcels")
        else:  # Linux和其他系统
            return Path.home() / "MySuite" / "InputExcels"
    
    def get_default_serial_port(self):
        """获取默认串口"""
        if self.system == "windows":
            return "COM3"
        else:  # Linux
            return "/dev/ttyUSB0"
    
    def get_serial_ports(self):
        """获取可用串口列表"""
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    def create_directories(self, path: Path):
        """创建目录（如果不存在）"""
        path.mkdir(parents=True, exist_ok=True)

class SimpleAsyncHTTPClient(QtCore.QObject):
    """简化的异步HTTP客户端，使用全局连接池"""
    
    request_finished = QtCore.pyqtSignal(str, dict, object)
    
    def __init__(self, base_url: str = "https://localhost"):
        super().__init__()
        self.base_url = base_url
        self.loop: asyncio.AbstractEventLoop = None
        self._closed = False
        
    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """设置事件循环"""
        self.loop = loop
    
    def _make_sync_request(self, method: str, endpoint: str, request_id: str, **kwargs):
        """使用全局管理的session进行同步请求，然后发送信号"""
        if self._closed:
            result = {
                'status_code': 0,
                'data': None,
                'headers': {},
                'ok': False,
                'error': 'Client已关闭'
            }
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result", 
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, result),
                QtCore.Q_ARG(object, Exception("Client已关闭"))
            )
            return
        
        try:
            import threading
            # 使用全局管理的session
            session = http_client_manager.get_session(self.base_url)
            
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = session.get(url, timeout=10, **kwargs)
            elif method.upper() == 'POST':
                response = session.post(url, timeout=10, **kwargs)
            elif method.upper() == 'PUT':
                response = session.put(url, timeout=10, **kwargs)
            elif method.upper() == 'DELETE':
                response = session.delete(url, timeout=10, **kwargs)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            try:
                data = response.json()
            except:
                data = response.text
            
            result = {
                'status_code': response.status_code,
                'data': data,
                'headers': dict(response.headers),
                'ok': response.status_code < 400
            }
            
            # 在主线程中发射信号
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result", 
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, result),
                QtCore.Q_ARG(object, None)
            )
            
        except Exception as e:
            # 20250714+09:30+ 修复异常处理：提供更好的错误信息
            error_result = {
                'status_code': 0,
                'data': None,
                'headers': {},
                'ok': False,
                'error': str(e)
            }
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, error_result),
                QtCore.Q_ARG(object, e)
            )
    
    @QtCore.pyqtSlot(str, dict, object)
    def _emit_result(self, request_id: str, result: dict, error: object):
        """在主线程中发射结果信号"""
        self.request_finished.emit(request_id, result, error)
    
    def post_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步POST请求"""
        import time
        import threading
        if request_id is None:
            request_id = f"post_{int(time.time() * 1000)}"
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('POST', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        
        return request_id
    
    def get_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步GET请求"""
        import threading
        if request_id is None:
            request_id = f"get_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("GET", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    def put_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步PUT请求"""
        import threading
        if request_id is None:
            request_id = f"put_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("PUT", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    def delete_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """发起异步DELETE请求"""
        import threading
        if request_id is None:
            request_id = f"delete_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        # 在单独线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=("DELETE", endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        return request_id
    
    async def close(self):
        """关闭客户端"""
        self._closed = True
        print(f"SimpleAsyncHTTPClient已关闭: {self.base_url}")

class SimpleSyncHTTPClient:
    """简化的同步HTTP客户端，使用全局连接池"""
    
    def __init__(self, base_url: str = "https://localhost"):
        self.base_url = base_url
        self._closed = False
    
    def _get_session(self):
        """获取全局管理的session"""
        if self._closed:
            raise RuntimeError("Client已关闭")
        return http_client_manager.get_session(self.base_url)
    
    def post(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步POST请求"""
        if self._closed:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': 'Client已关闭'
            }
        
        try:
            session = self._get_session()
            url = f"{self.base_url}{endpoint}"
            response = session.post(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def get(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步GET请求"""
        if self._closed:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': 'Client已关闭'
            }
        
        try:
            session = self._get_session()
            url = f"{self.base_url}{endpoint}"
            response = session.get(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def close(self):
        """关闭客户端"""
        self._closed = True
        print(f"SimpleSyncHTTPClient已关闭: {self.base_url}")

class _BaseModule:
    def __init__(self):
        self._is_running = False
    def start(self):
        self._is_running = True
        print(f"Started {self.__class__.__name__}")
    def stop(self):
        self._is_running = False
        print(f"Stopped {self.__class__.__name__}")
    def is_running(self):
        return self._is_running

class FloatingReminderModule(_BaseModule): 
    pass

class ExcelMonitorModule(_BaseModule):
    def __init__(self, watch_folder): 
        super().__init__()
        self.watch_folder = Path(watch_folder)  # 修改10 -多平台: 使用Path对象

class SerialListenerModule(_BaseModule):
    def __init__(self, port, baudrate): 
        super().__init__()
        self.port = port
        self.baudrate = baudrate

# 修改为Nginx监听的地址和端口
# 如果Nginx在同一台机器上，监听443端口，且域名是localhost，则如下
SERVER_HTTP_BASE = "https://localhost" # 默认HTTPS端口443可以省略
SERVER_WS_URL = "wss://localhost/ws/feature_flags?token=" # 默认WSS端口443可以省略
CLIENT_VERSION = "1.0.0"

class FeatureManager:
    def __init__(self):
        # 修改10 -多平台: 使用平台配置
        self.platform_config = PlatformConfig()
        
        self.modules = {
            "floating_reminder": FloatingReminderModule(),
            "excel_monitor": ExcelMonitorModule(
                watch_folder=self.platform_config.get_default_watch_folder()
            ),
            "serial_listener": SerialListenerModule(
                port=self.platform_config.get_default_serial_port(), 
                baudrate=9600
            ),
        }
        self.current_flags = {name: False for name in self.modules.keys()}

    def apply_flags(self, flags: dict):
        for name, enabled in flags.items():
            module = self.modules.get(name)
            if not module:
                continue
            
            is_running = module.is_running()
            if enabled and not is_running:
                module.start()
            elif not enabled and is_running:
                module.stop()
        
        # Also update our internal state record
        self.current_flags.update(flags)

    def stop_all(self):
        for module in self.modules.values():
            if module.is_running():
                module.stop()

# 2025/06/26.11:50+分离ui操作 - MainWindow已移至Launcher.py

# 2025/06/26.11:50+分离ui操作 - Token验证和独立启动函数
def verify_token(token: str) -> bool:
    """验证token是否有效"""
    if not token:
        print("Token为空，验证失败")
        return False
    
    try:
        # 2025/06/26.11:50+分离ui操作 - 修复token验证端点
        import requests
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get("http://localhost:8006/api/verify", headers=headers, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            success = result.get('status') == 'success'
            if success:
                print(f"Token验证成功: {result.get('message', '')}")
            return success
        else:
            print(f"トークン認証失敗: HTTP {response.status_code}")
            if response.status_code == 401:
                print("Token无效或已过期，请重新登录")
            return False
    except Exception as e:
        print(f"トークン認証失敗: {e}")
        return False

def parse_startup_args():
    """解析启动参数"""
    if len(sys.argv) < 4:
        print("使用方法: python program1.py <token> <employee_id> <employee_name>")
        print("パラメータが不足しています。プログラムを開始できません")
        sys.exit(1)
    
    token = sys.argv[1]
    employee_id = sys.argv[2]  
    employee_name = sys.argv[3]
    
    return token, employee_id, employee_name



class EmployeeInterfaceWindow(QtWidgets.QMainWindow):
    
    """修改14 增加id 密码和跳转界面 - 员工操作界面B"""
    
    def __init__(self, employee_id: str, employee_name: str, token: str = None, main_window=None):
        super().__init__()
        self.employee_id = employee_id
        self.employee_name = employee_name
        self.token = token
        self.main_window = main_window

        # 20250708 + Server5 API客户端配置（本地运行）
        self.server5_base_url = "http://localhost:8009"
        self.server5_client = Server5APIClient(self.server5_base_url)
        self.is_server5_enabled = True  # 是否启用Server5功能

        # 20250708 + 删除Server5-2相关配置，不再需要timeprotab采集凭据
        
        # HTTP客户端初始化 - 简化为仅使用必要的客户端
        # 修改4：支持https和http
        base_url = "https://localhost"
        if hasattr(self, 'main_window') and self.main_window:
            # 从主窗口获取配置
            pass
        
        # 主要的HTTP客户端
        self.async_http_client = SimpleAsyncHTTPClient(base_url)
        self.sync_http_client = SimpleSyncHTTPClient(base_url)
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 使用Server5专用客户端
        if self.is_server5_enabled:
            self.server5_async_client = SimpleAsyncHTTPClient(self.server5_base_url)
        
        # 连接异步请求完成信号
        self.async_http_client.request_finished.connect(self.on_async_request_finished)
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 连接Server5客户端信号
        if self.is_server5_enabled:
            self.server5_async_client.request_finished.connect(self.on_server5_request_finished)
        
        # 表格3相关属性
        self.table3_current_month = QtCore.QDate.currentDate().addDays(-QtCore.QDate.currentDate().day() + 1)          # 月初
        self.table3_data_source = 'entries'  # #20250711+10：15+修改的主题 - 默认使用entries，不再使用xml
        self.table3_current_data = []  # 当前Table3显示的数据
        
        # #20250711+12：45+修改的主题@program1.py - 添加各区域的当前月份追踪变量
        self.table1_current_month = datetime.now()  # Table1当前显示的月份
        self.chart_current_month = datetime.now()   # Chart当前显示的月份
        
        # #20250711+13：10+修改的主题 - 添加按键状态跟踪变量，初始化时当月按键是激活状态
        self.table1_active_button = 'curr'  # Table1当前激活的按键：'prev', 'curr', 'next'
        self.table3_active_button = 'curr'  # Table3当前激活的按键：'prev', 'curr', 'next'  
        self.chart_active_button = 'curr'   # Chart当前激活的按键：'prev', 'curr', 'next'
        
        # #20250711+17：00+修改的主题 - 添加全局月份偏移量跟踪变量
        self.global_active_button = 'curr'  # 全局当前激活的按键：'prev', 'curr', 'next'
        self.global_month_offset = 0  # 全局月份偏移量（0=当月，-1=上个月，-2=2个月前，+1=下个月，+2=2个月后）
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - entries操作状态管理
        self.current_editing_db_id = None  # 当前编辑中的entry ID
        self.is_editing_entry = False  # 是否处于编辑模式
        self.pending_operations = []  # 待处理的操作队列
        
        # 2025/06/26.11:50+分离ui操作 - 判断是否为独立运行模式
        self.is_standalone = (main_window is None)  # 如果没有主窗口引用，则为独立运行
        
        # 通知相关 - 2025/07/16 新增：MDB同步状态通知
        self.notification_connected = False
        self.notification_websocket = None
        self.notification_connect_thread = None
        
        self.setup_ui()
        
        # 修改 b2 布局微调 , 使用比例: 设置窗口初始尺寸
        self.setWindowTitle(f"従業員操作画面 - {self.employee_name}")
        self.resize(int(1920 * 0.7), int(1080 * 1.00)) # 设置初始尺寸为1152x864
        
        # 监听窗口关闭事件
        self.installEventFilter(self)
        
        # 设置传感器数据定时器
        self.sensor_timer = QtCore.QTimer()
        self.sensor_timer.timeout.connect(self.fetch_sensor_data)
        self.sensor_timer.start(500)  # 每5秒更新一次传感器数据
        
        # 立即获取一次传感器数据
        self.fetch_sensor_data()

        # 修改 b10/b11: 登录后自动加载数据
        #  修改 b20 跳转界面
        QtCore.QTimer.singleShot(200, self.auto_load_initial_data)
    
    def auto_load_initial_data(self):
        """#20250710 卡顿修复 - 异步分段加载初始数据，避免阻塞主线程"""
        try:
            self.log_employee_message("🔄 开始自动加载初始数据...")
            
            # #20250710 卡顿修复 - 添加全局加载状态指示器
            self._show_loading_overlay("正在加载初始数据...")
            
            # #20250710 卡顿修复 - 初始化加载状态跟踪
            self.loading_steps = {
                'table1': {'status': 'pending', 'name': 'Table1数据'},
                'table3': {'status': 'pending', 'name': 'Table3数据'},
                'department': {'status': 'pending', 'name': '部门信息'},
                'months': {'status': 'pending', 'name': '可用月份'},
                'chart': {'status': 'pending', 'name': '图表数据'}
            }
            
            # #20250710 卡顿修复 - 开始分段异步加载
            self._start_step_by_step_loading()
            
        except Exception as e:
            self.log_employee_message(f"❌ 自动加载初始数据失败: {e}")
            self._hide_loading_overlay()
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")
    
    def _show_loading_overlay(self, message: str):
        """#20250710 卡顿修复 - 显示加载遮罩"""
        if not hasattr(self, 'loading_overlay'):
            self.loading_overlay = QtWidgets.QWidget(self)
            self.loading_overlay.setStyleSheet("""
                QWidget {
                    background-color: rgba(0, 0, 0, 0.7);
                    border-radius: 10px;
                }
                QLabel {
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
            
            layout = QtWidgets.QVBoxLayout(self.loading_overlay)
            self.loading_label = QtWidgets.QLabel("加载中...")
            self.loading_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            
            # 添加进度条
            self.loading_progress = QtWidgets.QProgressBar()
            self.loading_progress.setRange(0, 0)  # 不确定进度
            self.loading_progress.setStyleSheet("""
                QProgressBar {
                    border: 2px solid white;
                    border-radius: 5px;
                    text-align: center;
                    color: white;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 3px;
                }
            """)
            
            layout.addWidget(self.loading_label)
            layout.addWidget(self.loading_progress)
            
            # 添加重试按钮
            self.retry_button = QtWidgets.QPushButton("重试")
            self.retry_button.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            self.retry_button.clicked.connect(self._retry_loading)
            self.retry_button.hide()  # 初始隐藏
            
            layout.addWidget(self.retry_button)
        
        self.loading_label.setText(message)
        self.loading_overlay.show()
        self.loading_overlay.resize(self.size())
        self.loading_overlay.move(0, 0)
        self.retry_button.hide()
    
    def _hide_loading_overlay(self):
        """#20250710 卡顿修复 - 隐藏加载遮罩"""
        if hasattr(self, 'loading_overlay'):
            self.loading_overlay.hide()
    
    def _start_step_by_step_loading(self):
        """#20250710 卡顿修复 - 开始分段异步加载"""
        self.log_employee_message("📋 步骤1: 开始加载Table1数据...")
        self.loading_steps['table1']['status'] = 'loading'
        self._update_loading_progress()
        
        # 异步加载Table1数据
        self._load_table1_data_async()
    
    def _load_table1_data_async(self):
        """#20250710 卡顿修复 - 异步加载Table1数据，使用与按钮点击相同的方法"""
        try:
            self.log_employee_message("📋 步骤1: 开始加载Table1数据...")
            target_month = datetime.now()
            
            # 使用与按钮点击相同的方法，确保一致性
            QtCore.QTimer.singleShot(100, lambda: self._load_timeprotab_from_server5_api_for_init(target_month))
            
        except Exception as e:
            self.log_employee_message(f"❌ Table1数据加载异常: {e}")
            self._on_step_failed('table1', str(e))
    
    def _load_table3_data_async(self):
        """#20250711+10：15+修改的主题 - 异步加载Table3数据，改用entries而非xml"""
        try:
            self.log_employee_message("📋 步骤2: 开始加载Table3数据...")
            self.loading_steps['table3']['status'] = 'loading'
            self._update_loading_progress()
            
            #20250711+10：15+修改的主题 - 修正数据源定义和使用统一的方法
            self.table3_data_source = 'entries'  # 应该使用entries而不是xml
            
            target_month = datetime.now()
            start_date = target_month.replace(day=1).strftime('%Y-%m-%d')
            
            # 获取月末日期
            if target_month.month == 12:
                end_date = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
            end_date = end_date.strftime('%Y-%m-%d')
            
            self.log_employee_message(f"📡 初始化：调用Server5 API: get_entries({self.employee_id}, {start_date}, {end_date})")
            
            # #20250711+10：15+修改的主题 - 使用与按钮点击相同的方法，确保一致性
            # 使用同步客户端调用正确的API
            result = self.server5_client.get_entries(
                employee_id=self.employee_id,
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )
            
            # 处理响应
            if result.get("ok"):
                entries = result.get("data", [])
                self.table3_current_data = entries
                
                if entries:
                    self.log_employee_message(f"✅ 初始化：Table3数据加载成功 {len(entries)} 条记录")
                    # 转换为Table3显示格式 - 与按钮点击保持一致的字段映射
                    table3_records = []
                    for entry in entries:
                        record = {
                            'DB_ID': entry.get('external_id', ''),  #20250711+10：15+修改的主题 - 初始化时DB_ID应该映射到external_id
                            '従業員ｺｰﾄﾞ': entry.get('employee_id', ''),
                            '日付': entry.get('entry_date', ''),
                            '機種': entry.get('model', ''),  #20250711+10：15+修改的主题 - 应该是model而不是project_code
                            '号機': entry.get('number', ''),  #20250711+10：15+修改的主题 - 添加number字段
                            '工場製番': entry.get('factory_number', ''),  #20250711+10：15+修改的主题 - 添加factory_number字段
                            '工事番号': entry.get('project_number', ''),  #20250711+10：15+修改的主题 - 添加project_number字段
                            'ﾕﾆｯﾄ番号': entry.get('unit_number', ''),  #20250711+10：15+修改的主题 - 添加unit_number字段
                            '区分': str(entry.get('category', '')),  #20250711+10：15+修改的主题 - 应该是category而不是status
                            '項目': str(entry.get('item', '')),  #20250711+10：15+修改的主题 - 应该是item而不是description
                            '時間': str(entry.get('duration', 0)),  #20250711+10：15+修改的主题 - 转换为字符串
                            '所属ｺｰﾄﾞ': entry.get('department', ''),
                            # 内部使用字段
                            'external_id': entry.get('external_id', ''),
                            'db_id': entry.get('id', '')
                        }
                        table3_records.append(record)
                    
                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table3_records)
                    )
                else:
                    self.log_employee_message("⚠️ 初始化：Table3没有数据")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
                
                # 标记步骤完成
                self._on_step_completed('table3')
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ 初始化：Server5 API调用失败: {error_msg}")
                self._on_step_failed('table3', error_msg)
            
        except Exception as e:
            self.log_employee_message(f"❌ 初始化：Table3数据加载异常: {e}")
            self._on_step_failed('table3', str(e))
    
    def _load_department_data_async(self):
        """#20250710 卡顿修复 - 异步加载部门数据"""
        try:
            self.log_employee_message("📋 步骤3: 开始加载部门信息...")
            self.loading_steps['department']['status'] = 'loading'
            self._update_loading_progress()
            
            # 使用异步HTTP客户端，修复API路径
            request_id = f"department_load_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
            self.server5_async_client.get_async(f"/api/department/employee/{self.employee_id}", request_id)
            
            # 设置超时定时器
            self._setup_request_timeout(request_id, 5000, self._on_department_timeout)
            
        except Exception as e:
            self.log_employee_message(f"❌ 部门信息加载异常: {e}")
            self._on_step_failed('department', str(e))
    
    def _load_months_data_async(self):
        """#20250710 卡顿修复 - 异步加载可用月份数据，使用与按钮点击相同的方法"""
        try:
            self.log_employee_message("📋 步骤4: 开始加载可用月份...")
            self.loading_steps['months']['status'] = 'loading'
            self._update_loading_progress()
            
            # 使用与按钮点击相同的方法，确保一致性
            QtCore.QTimer.singleShot(100, lambda: self._load_available_months_from_server5_api_for_init())
            
        except Exception as e:
            self.log_employee_message(f"❌ 可用月份加载异常: {e}")
            self._on_step_failed('months', str(e))
    
    def _load_chart_data_async(self):
        """#20250710 卡顿修复 - 异步加载图表数据，使用与按钮点击相同的方法"""
        try:
            self.log_employee_message("📋 步骤5: 开始加载图表数据...")
            self.loading_steps['chart']['status'] = 'loading'
            self._update_loading_progress()
            
            # 使用与按钮点击相同的方法，确保一致性
            QtCore.QTimer.singleShot(100, lambda: self._load_chart_data_from_server5_api_for_init())
            
        except Exception as e:
            self.log_employee_message(f"❌ 图表数据加载异常: {e}")
            self._on_step_failed('chart', str(e))
    
    def _setup_request_timeout(self, request_id: str, timeout_ms: int, callback):
        """#20250710 卡顿修复 - 设置请求超时"""
        if not hasattr(self, 'request_timeouts'):
            self.request_timeouts = {}
        
        timer = QtCore.QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(lambda: callback(request_id))
        timer.start(timeout_ms)
        self.request_timeouts[request_id] = timer
    
    def _clear_request_timeout(self, request_id: str):
        """#20250710 卡顿修复 - 清除请求超时"""
        if hasattr(self, 'request_timeouts') and request_id in self.request_timeouts:
            self.request_timeouts[request_id].stop()
            del self.request_timeouts[request_id]
    
    def _on_table1_timeout(self, request_id: str):
        """#20250710 卡顿修复 - Table1加载超时"""
        self.log_employee_message("⏰ Table1数据加载超时")
        self._on_step_failed('table1', "加载超时")
    
    def _on_table3_timeout(self, request_id: str):
        """#20250710 卡顿修复 - Table3加载超时"""
        self.log_employee_message("⏰ Table3数据加载超时")
        self._on_step_failed('table3', "加载超时")
    
    def _on_department_timeout(self, request_id: str):
        """#20250710 卡顿修复 - 部门信息加载超时"""
        self.log_employee_message("⏰ 部门信息加载超时")
        self._on_step_failed('department', "加载超时")
    
    def _on_months_timeout(self, request_id: str):
        """#20250710 卡顿修复 - 可用月份加载超时"""
        self.log_employee_message("⏰ 可用月份加载超时")
        self._on_step_failed('months', "加载超时")
    
    def _on_chart_timeout(self, request_id: str):
        """#20250710 卡顿修复 - 图表数据加载超时"""
        self.log_employee_message("⏰ 图表数据加载超时")
        self._on_step_failed('chart', "加载超时")
    
    def _on_step_completed(self, step_name: str):
        """#20250710 卡顿修复 - 步骤完成回调"""
        self.loading_steps[step_name]['status'] = 'completed'
        self._update_loading_progress()
        
        # 继续下一步
        if step_name == 'table1':
            self._load_table3_data_async()
        elif step_name == 'table3':
            self._load_department_data_async()
        elif step_name == 'department':
            self._load_months_data_async()
        elif step_name == 'months':
            self._load_chart_data_async()
        elif step_name == 'chart':
            self._on_all_steps_completed()
    
    def _on_step_failed(self, step_name: str, error_msg: str):
        """#20250710 卡顿修复 - 步骤失败回调"""
        self.loading_steps[step_name]['status'] = 'failed'
        self.loading_steps[step_name]['error'] = error_msg
        self._update_loading_progress()
        
        # 显示重试选项
        self._show_retry_options(step_name)
    
    def _show_retry_options(self, failed_step: str):
        """#20250710 卡顿修复 - 显示重试选项"""
        step_info = self.loading_steps[failed_step]
        
        # 更新加载界面显示失败信息
        self.loading_label.setText(f"❌ {step_info['name']}加载失败: {step_info.get('error', '未知错误')}")
        self.retry_button.show()
        
        # 弹出选择对话框
        reply = QtWidgets.QMessageBox.question(
            self, 
            "加载失败", 
            f"{step_info['name']}加载失败: {step_info.get('error', '未知错误')}\n\n请选择操作：",
            QtWidgets.QMessageBox.StandardButton.Retry | 
            QtWidgets.QMessageBox.StandardButton.Ignore | 
            QtWidgets.QMessageBox.StandardButton.Cancel
        )
        
        if reply == QtWidgets.QMessageBox.StandardButton.Retry:
            self._retry_step(failed_step)
        elif reply == QtWidgets.QMessageBox.StandardButton.Ignore:
            self._skip_step(failed_step)
        else:
            self._cancel_loading()
    
    def _retry_step(self, step_name: str):
        """#20250710 卡顿修复 - 重试指定步骤"""
        self.log_employee_message(f"🔄 重试加载 {self.loading_steps[step_name]['name']}...")
        self.loading_steps[step_name]['status'] = 'pending'
        self.retry_button.hide()
        
        # 根据步骤名称调用对应的加载方法
        if step_name == 'table1':
            self._load_table1_data_async()
        elif step_name == 'table3':
            self._load_table3_data_async()
        elif step_name == 'department':
            self._load_department_data_async()
        elif step_name == 'months':
            self._load_months_data_async()
        elif step_name == 'chart':
            self._load_chart_data_async()
    
    def _skip_step(self, step_name: str):
        """#20250710 卡顿修复 - 跳过指定步骤"""
        self.log_employee_message(f"⏭️ 跳过 {self.loading_steps[step_name]['name']}，继续下一步...")
        self.loading_steps[step_name]['status'] = 'skipped'
        self.retry_button.hide()
        self._on_step_completed(step_name)
    
    def _retry_loading(self):
        """#20250710 卡顿修复 - 重试整个加载过程"""
        self.log_employee_message("🔄 重试整个加载过程...")
        
        # 重置所有步骤状态
        for step in self.loading_steps.values():
            step['status'] = 'pending'
            step.pop('error', None)
        
        self._start_step_by_step_loading()
    
    def _cancel_loading(self):
        """#20250710 卡顿修复 - 取消加载"""
        self.log_employee_message("❌ 用户取消了数据加载")
        self._hide_loading_overlay()
        
        # 清除所有超时定时器
        if hasattr(self, 'request_timeouts'):
            for timer in self.request_timeouts.values():
                timer.stop()
            self.request_timeouts.clear()
    
    def _update_loading_progress(self):
        """#20250710 卡顿修复 - 更新加载进度"""
        completed = sum(1 for step in self.loading_steps.values() if step['status'] in ['completed', 'skipped'])
        total = len(self.loading_steps)
        
        # 更新进度条
        if hasattr(self, 'loading_progress'):
            self.loading_progress.setRange(0, total)
            self.loading_progress.setValue(completed)
        
        # 更新状态文本
        loading_step = None
        for name, step in self.loading_steps.items():
            if step['status'] == 'loading':
                loading_step = step['name']
                break
        
        if loading_step:
            self.loading_label.setText(f"正在加载: {loading_step} ({completed}/{total})")
        else:
            self.loading_label.setText(f"加载进度: {completed}/{total}")
    
    def _on_all_steps_completed(self):
        """#20250710 卡顿修复 - 所有步骤完成"""
        self.log_employee_message("✅ 所有初始数据加载完成")
        self._hide_loading_overlay()
        
        # 显示加载摘要
        success_count = sum(1 for step in self.loading_steps.values() if step['status'] == 'completed')
        skipped_count = sum(1 for step in self.loading_steps.values() if step['status'] == 'skipped')
        failed_count = sum(1 for step in self.loading_steps.values() if step['status'] == 'failed')
        
        summary = f"数据加载完成 - 成功: {success_count}, 跳过: {skipped_count}, 失败: {failed_count}"
        self.log_employee_message(summary)
        
        # 如果有失败的步骤，显示详细信息
        if failed_count > 0:
            failed_steps = [f"{step['name']}: {step.get('error', '未知错误')}" 
                          for step in self.loading_steps.values() if step['status'] == 'failed']
            self.log_employee_message(f"失败详情: {'; '.join(failed_steps)}")
        
        # 2025/07/16 新增：启动通知WebSocket连接
        self.log_employee_message("📢 启动MDB同步状态通知服务...")
        QtCore.QTimer.singleShot(1000, self.connect_to_notifications)  # 延迟1秒启动通知连接

    def _fetch_timeprotab_data_for_table1(self):
        """#20250709+15:40+修复主题 Table1数据只用Server5 API"""
        target_month = datetime.now()
        QtCore.QTimer.singleShot(100, lambda: self._load_timeprotab_from_server5_api(target_month))

    def _fetch_entries_data_for_table3(self):
        """#20250711+10：15+修改的主题 Table3数据只用Server5 API的entries端点"""
        # #20250711+10：15+修改的主题 - 确保使用entries而不是xml
        self.table3_data_source = 'entries'  # 设置数据源为entries
        self._fetch_5xml_data_for_table3()

    def _delayed_load_chart(self):
        """延迟加载图表数据，确保月份数据已加载完成 #20250709+15:40+修复主题"""
        try:
            self.log_employee_message("📋 步骤5: 准备加载图表数据...")
            if self.month_combo.count() > 0:
                self.load_selected_chart()
            else:
                self.log_employee_message("⚠️ 没有可用的月份数据，跳过图表加载")
        except Exception as e:
            self.log_employee_message(f"❌ 延迟加载图表失败: {e}")

    def on_show_prev_month_clicked(self):
        """ 修改 b21 增加table3的交互处理。 - 显示上月数据 """
        # 修改table3的按键：修复QtCore.QDate类型转换问题，避免闪退
        try:
            if isinstance(self.table3_current_month, QtCore.QDate):
                # 如果是QtCore.QDate类型，先转换为datetime
                current_date = self.table3_current_month.toPyDate()
                current_datetime = datetime(current_date.year, current_date.month, 1)
            else:
                # 如果已经是datetime类型
                current_datetime = self.table3_current_month
            
            # 计算上个月
            if current_datetime.month == 1:
                # 如果是1月，需要回到上一年12月
                prev_month = current_datetime.replace(year=current_datetime.year - 1, month=12)
            else:
                # 其他月份直接减1
                prev_month = current_datetime.replace(month=current_datetime.month - 1)
            
            # 更新当前月份
            self.table3_current_month = prev_month
            
            # #20250711+12：45+修改的主题@program1.py - 更新Table3月份显示文本框
            #self.t3_month_display.setText(f"{prev_month.strftime('%Y/%m')} の mdb工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{prev_month.strftime('%Y/%m')}</span><br>"
                f"の mdb工時登録情報"
            )
            self.t3_month_display.setText(display_text)
            
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table3', 'prev')
            
            self.log_employee_message(f"🔄 切换到上个月: {prev_month.strftime('%Y/%m')}")
            self._fetch_5xml_data_for_table3()
            
        except Exception as e:
            self.log_employee_message(f"❌ 切换到上个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_show_curr_month_clicked(self):
        """ 修改 b21 增加table3的交互处理。 - 显示当月数据 """
        # 修改table3的按键：修复QtCore.QDate类型转换问题，避免闪退
        try:
            # 设置为当前月份
            current_datetime = datetime.now().replace(day=1)
            self.table3_current_month = current_datetime
            
            # #20250711+12：45+修改的主题@program1.py - 更新Table3月份显示文本框
            #self.t3_month_display.setText(f"{current_datetime.strftime('%Y/%m')} の mdb工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{current_datetime.strftime('%Y/%m')}</span><br>"
                f"の mdb工時登録情報"
            )
            self.t3_month_display.setText(display_text)
            
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table3', 'curr')
            
            self.log_employee_message(f"🔄 切换到当前月: {current_datetime.strftime('%Y/%m')}")
            self._fetch_5xml_data_for_table3()
            
        except Exception as e:
            self.log_employee_message(f"❌ 切换到当前月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_show_next_month_clicked(self):
        """ 修改 b21 增加table3的交互处理。 - 显示下月数据 """
        # 修改table3的按键：修复QtCore.QDate类型转换问题，避免闪退
        try:
            if isinstance(self.table3_current_month, QtCore.QDate):
                # 如果是QtCore.QDate类型，先转换为datetime
                current_date = self.table3_current_month.toPyDate()
                current_datetime = datetime(current_date.year, current_date.month, 1)
            else:
                # 如果已经是datetime类型
                current_datetime = self.table3_current_month
            
            # 计算下个月
            if current_datetime.month == 12:
                # 如果是12月，需要到下一年1月
                next_month = current_datetime.replace(year=current_datetime.year + 1, month=1)
            else:
                # 其他月份直接加1
                next_month = current_datetime.replace(month=current_datetime.month + 1)
            
            # 更新当前月份
            self.table3_current_month = next_month
            
            # #20250711+12：45+修改的主题@program1.py - 更新Table3月份显示文本框
            #self.t3_month_display.setText(f"{next_month.strftime('%Y/%m')} の mdb工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{next_month.strftime('%Y/%m')}</span><br>"
                f"の mdb工時登録情報"
            )
            self.t3_month_display.setText(display_text)
            
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table3', 'next')
            
            self.log_employee_message(f"🔄 切换到下个月: {next_month.strftime('%Y/%m')}")
            self._fetch_5xml_data_for_table3()
            
        except Exception as e:
            self.log_employee_message(f"❌ 切换到下个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")
    
    # #20250711+12：45+修改的主题@program1.py - 添加Table1的月份切换方法
    def on_table1_prev_month_clicked(self):
        """Table1 - 显示上月数据"""
        try:
            # 计算上个月
            if self.table1_current_month.month == 1:
                # 如果是1月，需要回到上一年12月
                prev_month = self.table1_current_month.replace(year=self.table1_current_month.year - 1, month=12)
            else:
                # 其他月份直接减1
                prev_month = self.table1_current_month.replace(month=self.table1_current_month.month - 1)
            
            # 更新当前月份
            self.table1_current_month = prev_month
            
                        # 更新Table1月份显示文本框
            #self.t1_month_display.setText(f"{prev_month.strftime('%Y/%m')}\nの timepro工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{prev_month.strftime('%Y/%m')}</span><br>"
                f"の timepro工時登録情報"
            )
            self.t1_month_display.setText(display_text)
            
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table1', 'prev')
            
            self.log_employee_message(f"🔄 Table1切换到上个月: {prev_month.strftime('%Y/%m')}")
            self._load_timeprotab_from_server5_api(prev_month)
            
        except Exception as e:
            self.log_employee_message(f"❌ Table1切换到上个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_table1_curr_month_clicked(self):
        """Table1 - 显示当月数据"""
        try:
            # 设置为当前月份
            current_month = datetime.now().replace(day=1)
            self.table1_current_month = current_month
            
            # 更新Table1月份显示文本框
            #self.t1_month_display.setText(f"{current_month.strftime('%Y/%m')}\nの timepro工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{current_month.strftime('%Y/%m')}</span><br>"
                f"の timepro工時登録情報"
            )
            self.t1_month_display.setText(display_text)
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table1', 'curr')            
            
            self.log_employee_message(f"🔄 Table1切换到当前月: {current_month.strftime('%Y/%m')}")
            self._load_timeprotab_from_server5_api(current_month)
            
        except Exception as e:
            self.log_employee_message(f"❌ Table1切换到当前月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_table1_next_month_clicked(self):
        """Table1 - 显示下月数据"""
        try:
            # 计算下个月
            if self.table1_current_month.month == 12:
                # 如果是12月，需要到下一年1月
                next_month = self.table1_current_month.replace(year=self.table1_current_month.year + 1, month=1)
            else:
                # 其他月份直接加1
                next_month = self.table1_current_month.replace(month=self.table1_current_month.month + 1)
            
            # 更新当前月份
            self.table1_current_month = next_month
            
            # 更新Table1月份显示文本框
            #self.t1_month_display.setText(f"{next_month.strftime('%Y/%m')}\nの timepro工時登録情報")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{next_month.strftime('%Y/%m')}</span><br>"
                f"の timepro工時登録情報"
            )
            self.t1_month_display.setText(display_text)
            # #20250711+13：10+修改的主题 - 更新按键颜色
            self._update_button_colors('table1', 'next')              
            
            self.log_employee_message(f"🔄 Table1切换到下个月: {next_month.strftime('%Y/%m')}")
            self._load_timeprotab_from_server5_api(next_month)
            
        except Exception as e:
            self.log_employee_message(f"❌ Table1切换到下个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    # #20250711+12：45+修改的主题@program1.py - 添加Chart的月份切换方法  
    def on_chart_prev_month_clicked(self):
        """Chart - 显示上月数据"""
        try:
            # 计算上个月
            if self.chart_current_month.month == 1:
                # 如果是1月，需要回到上一年12月
                prev_month = self.chart_current_month.replace(year=self.chart_current_month.year - 1, month=12)
            else:
                # 其他月份直接减1
                prev_month = self.chart_current_month.replace(month=self.chart_current_month.month - 1)
            
            # 更新当前月份
            self.chart_current_month = prev_month
            
            # 更新Chart月份显示文本框
            #self.chart_month_display.setText(f"{prev_month.strftime('%Y/%m')} の 工時比較")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{prev_month.strftime('%Y/%m')}</span><br>"
                f"の 工時比較"
            )
            self.chart_month_display.setText(display_text)
            
            # #20250711+16：20+修改的主题@program1.py - 更新按键颜色
            self._update_button_colors('chart', 'prev')
            
            # #20250711+16：20+修改的主题@program1.py - 同步month_combo选中项
            self._sync_month_combo_selection(prev_month)
            
            self.log_employee_message(f"🔄 Chart切换到上个月: {prev_month.strftime('%Y/%m')}")
            prev_month_display = prev_month.strftime("%Y年%m月")
            self._load_chart_data_from_server5_api(prev_month, prev_month_display, is_current=True)
            
            # #20250711+16：40+修改的主题 - 修复左边小Chart不联动问题：同时更新左边的小Chart
            try:
                small_chart_month = datetime(prev_month.year, prev_month.month, 1) - timedelta(days=1)
                small_chart_display = small_chart_month.strftime("%Y年%m月")
                self.log_employee_message(f"🔄 同时更新左边小Chart: {small_chart_display}")
                QtCore.QTimer.singleShot(200, lambda: self._load_chart_data_from_server5_api(small_chart_month, small_chart_display, is_current=False))
            except Exception as e:
                self.log_employee_message(f"❌ 更新左边小Chart失败: {e}")
            
        except Exception as e:
            self.log_employee_message(f"❌ Chart切换到上个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_chart_curr_month_clicked(self):
        """Chart - 显示当月数据"""
        try:
            # 设置为当前月份
            current_month = datetime.now().replace(day=1)
            self.chart_current_month = current_month
            
            # 更新Chart月份显示文本框
            #self.chart_month_display.setText(f"{current_month.strftime('%Y/%m')} の 工時比較")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{current_month.strftime('%Y/%m')}</span><br>"
                f"の 工時比較"
            )
            self.chart_month_display.setText(display_text)
            
            # #20250711+16：20+修改的主题@program1.py - 更新按键颜色
            self._update_button_colors('chart', 'curr')
            
            # #20250711+16：20+修改的主题@program1.py - 同步month_combo选中项
            self._sync_month_combo_selection(current_month)
            
            self.log_employee_message(f"🔄 Chart切换到当前月: {current_month.strftime('%Y/%m')}")
            current_month_display = current_month.strftime("%Y年%m月")
            self._load_chart_data_from_server5_api(current_month, current_month_display, is_current=True)
            
            # #20250711+16：40+修改的主题 - 修复左边小Chart不联动问题：同时更新左边的小Chart
            try:
                small_chart_month = datetime(current_month.year, current_month.month, 1) - timedelta(days=1)
                small_chart_display = small_chart_month.strftime("%Y年%m月")
                self.log_employee_message(f"🔄 同时更新左边小Chart: {small_chart_display}")
                QtCore.QTimer.singleShot(200, lambda: self._load_chart_data_from_server5_api(small_chart_month, small_chart_display, is_current=False))
            except Exception as e:
                self.log_employee_message(f"❌ 更新左边小Chart失败: {e}")
            
        except Exception as e:
            self.log_employee_message(f"❌ Chart切换到当前月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_chart_next_month_clicked(self):
        """Chart - 显示下月数据"""
        try:
            # 计算下个月
            if self.chart_current_month.month == 12:
                # 如果是12月，需要到下一年1月
                next_month = self.chart_current_month.replace(year=self.chart_current_month.year + 1, month=1)
            else:
                # 其他月份直接加1
                next_month = self.chart_current_month.replace(month=self.chart_current_month.month + 1)
            
            # 更新当前月份
            self.chart_current_month = next_month
            
            # 更新Chart月份显示文本框
            #self.chart_month_display.setText(f"{next_month.strftime('%Y/%m')} の 工時比較")
            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{next_month.strftime('%Y/%m')}</span><br>"
                f"の 工時比較"
            )
            self.chart_month_display.setText(display_text)
            
            # #20250711+16：20+修改的主题@program1.py - 更新按键颜色
            self._update_button_colors('chart', 'next')
            
            # #20250711+16：20+修改的主题@program1.py - 同步month_combo选中项
            self._sync_month_combo_selection(next_month)
            
            self.log_employee_message(f"🔄 Chart切换到下个月: {next_month.strftime('%Y/%m')}")
            next_month_display = next_month.strftime("%Y年%m月")
            self._load_chart_data_from_server5_api(next_month, next_month_display, is_current=True)
            
            # #20250711+16：40+修改的主题 - 修复左边小Chart不联动问题：同时更新左边的小Chart
            try:
                small_chart_month = datetime(next_month.year, next_month.month, 1) - timedelta(days=1)
                small_chart_display = small_chart_month.strftime("%Y年%m月")
                self.log_employee_message(f"🔄 同时更新左边小Chart: {small_chart_display}")
                QtCore.QTimer.singleShot(200, lambda: self._load_chart_data_from_server5_api(small_chart_month, small_chart_display, is_current=False))
            except Exception as e:
                self.log_employee_message(f"❌ 更新左边小Chart失败: {e}")
            
        except Exception as e:
            self.log_employee_message(f"❌ Chart切换到下个月失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.log_employee_message(f"❌ 详细错误信息: {error_details}")

    def on_t3_change_clicked(self):
        """ 修改，25061801: '更改'按钮现在触发UI进入编辑模式 """
        # #20250711+16：20+修改的主题@program1.py - 修改数据提取逻辑适应QTableView
        try:
            selected_indexes = self.table3.selectionModel().selectedRows()
            if not selected_indexes:
                return

            selected_row_index = selected_indexes[0].row()
            
            # 从模型中提取数据
            record_data = self.table3_model.get_row_data(selected_row_index)
            if not record_data:
                self.log_employee_message("❌ 无法获取选中行的数据")
                return
                
            # 映射字段名
            headers_map = {
                'DB_ID': 'db_id', '従業員ｺｰﾄﾞ': 'employee_id', '日付': 'date', '機種': 'model',
                '号機': 'number', '工場製番': 'factory_number', '工事番号': 'project_number',
                'ﾕﾆｯﾄ番号': 'unit_number', '区分': 'category', '項目': 'item',
                '時間': 'time', '所属ｺｰﾄﾞ': 'department'
            }
            
            # 转换数据格式
            edit_data = {}
            for display_name, field_key in headers_map.items():
                if display_name in record_data:
                    edit_data[field_key] = record_data[display_name]
            
            db_id = record_data.get('DB_ID', '')
            if db_id:
                self._enter_edit_mode(edit_data, db_id)
            else:
                self.log_employee_message("❌ 无法获取记录的DB_ID")
                
        except Exception as e:
            self.log_employee_message(f"❌ 编辑模式进入失败: {e}")

    def on_update_data_clicked(self):
        """ 修改，25061803  更改删除。
        #250624/修改数据库change存入逻辑 - 直接更新数据，服务器会自动记录更改后的新数据
        """
        if not self.current_editing_db_id:
            return
        
        # #250624/修改数据库change存入逻辑 - 直接发送更新请求，不再需要单独的日志记录
        self.log_employee_message(f"更新リクエストを送信しています, DB_ID: {self.current_editing_db_id}")
        self._trigger_update_action()

    # 修改，25061804  输入面板
    def _change_date(self, days_delta):
        """ Helper function to change the date in the date input field. """
        date_widget = self.input_fields.get('date')
        if not date_widget:
            return
        
        try:
            current_date = datetime.strptime(date_widget.text(), "%Y/%m/%d")
            new_date = current_date + timedelta(days=days_delta)
            date_widget.setText(new_date.strftime("%Y/%m/%d"))
        except ValueError:
            self.log_employee_message("日付形式が無効です。YYYY/MM/DD形式を使用してください。")

    # 修改，25061804  输入面板
    def on_prev_day_clicked(self):
        """Go to the previous day."""
        self._change_date(-1)

    # 修改，25061804  输入面板
    def on_next_day_clicked(self):
        """Go to the next day."""
        self._change_date(1)

    # 修改，25061804  输入面板
    def on_calendar_clicked(self):
        """Open a calendar to select a date."""
        date_widget = self.input_fields.get('date')
        if not date_widget:
            return

        calendar_dialog = QtWidgets.QDialog(self)
        calendar_dialog.setWindowTitle("日付選択")
        
        cal_layout = QtWidgets.QVBoxLayout(calendar_dialog)
        calendar = QtWidgets.QCalendarWidget(calendar_dialog)
        cal_layout.addWidget(calendar)
        
        # Set calendar to current date from input
        try:
            current_date = datetime.strptime(date_widget.text(), "%Y/%m/%d")
            calendar.setSelectedDate(QtCore.QDate(current_date.year, current_date.month, current_date.day))
        except ValueError:
            pass # Keep calendar at default if format is bad

        def date_selected():
            selected_qdate = calendar.selectedDate()
            date_widget.setText(selected_qdate.toString("yyyy/MM/dd"))
            calendar_dialog.accept()

        calendar.clicked.connect(date_selected)
        
        calendar_dialog.exec()

    # 修改，25061803  更改删除。
    def _trigger_update_action(self):
        """
        修改，25061803  更改删除。
        处理 "更改数据(按钮5)" 的核心逻辑，在日志记录成功后被调用。
        20250626.s5，6 - 进行客户端的修改，使用Server5的entries API
        """
        if not self.current_editing_db_id:
            return

        # 收集当前输入框的数据并转换为entries格式
        form_data = {field: widget.text().strip() for field, widget in self.input_fields.items()}
        
        # 转换为entries API期望的格式 - 修复字段映射，与插入操作保持一致
        entry_data = {
            "entry_date": form_data.get('date', '').replace('/', '-'),  # 转换日期格式
            "employee_id": form_data.get('employee_id', ''),
            "duration": float(form_data.get('time', '0')),
            "model": form_data.get('model', ''),  # 机种 - 直接映射到model字段
            "number": form_data.get('number', ''),  # 号机 - 直接映射到number字段
            "factory_number": form_data.get('factory_number', ''),  # 工场製番 - 直接映射到factory_number字段
            "project_number": form_data.get('project_number', ''),  # 工事番号 - 直接映射到project_number字段
            "unit_number": form_data.get('unit_number', ''),  # ユニット番号 - 直接映射到unit_number字段
            "category": form_data.get('category', ''),  # 区分 - 直接映射到category字段
            "item": form_data.get('item', ''),  # 项目 - 直接映射到item字段
            "department": form_data.get('department', ''),
            "source": "user"  # 2025 07/04 +  16：30 + 相关主题: 明确标记为用户操作，以触发同步
        }
        
        # 发送更新请求到Server5的entries API
        self.log_employee_message(f"发送更新请求到entries分区表, ID: {self.current_editing_db_id}")
        req_id = f"update_progress_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        
        if self.is_server5_enabled:
            # 使用Server5专用客户端调用entries API
            self.server5_async_client.put_async(f"/api/entries/{self.current_editing_db_id}", req_id, json=entry_data)
            self.log_employee_message(f"更新リクエストを送信しました (Server5 entries APIへ)")
        else:
            # 回退到原来的API
            self.async_http_client.put_async(f"/sync/api/entries/{self.current_editing_db_id}", req_id, json=entry_data)
            self.log_employee_message(f"更新リクエストを送信しました (旧APIへ)")

    # 修改，25061801  更改输入面板
    def on_cancel_update_clicked(self):
        """ 修改，25061801: '取消更改' 按钮的处理逻辑 """
        self._exit_edit_mode()

    # 修改，25061801  更改输入面板
    def on_input_field_changed(self):
        """ 修改，25061801: 检查输入框内容是否变化以激活更新按钮 """
        if self.current_editing_db_id is None:
            return # 不在编辑模式下，不做任何事

        is_changed = False
        for field, widget in self.input_fields.items():
            if field in self.original_input_data:
                if self.original_input_data[field] != widget.text():
                    is_changed = True
                    break
        
        self.update_data_btn.setEnabled(is_changed)

    # 修改，25061801  更改输入面板
    def _enter_edit_mode(self, record_data, db_id):
        """ 修改，25061801: 进入编辑模式的UI设置 """
        self.log_employee_message(f"編集モードに入ります, DB_ID: {db_id}")
        
        # 保存当前状态
        self.current_editing_db_id = db_id
        self.original_input_data = record_data.copy()

        # 填充表单
        for field_key, widget in self.input_fields.items():
            if field_key in record_data:
                widget.setText(record_data[field_key])
        
        # 更新UI
        self.progress_status_label.setText(f"変更可能、変更を行ってください (DB_ID: {db_id})")
        self.upload_progress_btn.setVisible(False)
        self.update_data_btn.setVisible(True)
        self.update_data_btn.setEnabled(False) # 初始不可用，需用户修改
        self.cancel_update_btn.setVisible(True)

    # 修改，25061801  更改输入面板
    def _exit_edit_mode(self):
        """ 修改，25061801: 退出编辑模式的UI设置 """
        self.log_employee_message("編集モードを終了します")
        
        # 清理状态
        self.current_editing_db_id = None
        self.original_input_data = {}

        # 清空表单 (除了只读的员工ID)
        for field_key, widget in self.input_fields.items():
            if not widget.isReadOnly():
                widget.clear()

        # 恢复UI
        self.progress_status_label.setText("編集可能")
        self.upload_progress_btn.setVisible(True)
        self.update_data_btn.setVisible(False)
        self.cancel_update_btn.setVisible(False)

    def on_t3_delete_clicked(self):
        """ 修改，25061803  更改删除。 - 删除按钮逻辑 """
        # 20250715 - 修复：使用QTableView的正确选择方法
        try:
            selected_indexes = self.table3.selectionModel().selectedRows()
            if not selected_indexes:
                self.log_employee_message("❌ 削除する行を選択してください")
                return

            selected_row_index = selected_indexes[0].row()
            
            # 从模型中提取数据
            record_data = self.table3_model.get_row_data(selected_row_index)
            if not record_data:
                self.log_employee_message("❌ 无法获取选中行的数据")
                return
            
            # 获取DB_ID（使用external_id作为删除目标）
            db_id = record_data.get('DB_ID', '')
            if not db_id:
                self.log_employee_message("❌ 无法获取记录的DB_ID")
                return
                
        except Exception as e:
            self.log_employee_message(f"❌ 删除选择失败: {e}")
            return
            
        reply = QtWidgets.QMessageBox.question(self, "削除確認",
            f"ID: {db_id} のレコードを削除してもよろしいですか？この操作は元に戻せません。",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.No)

        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            # 20250715 - 修复：使用Server5专用客户端调用正确的API
            self.log_employee_message(f"削除リクエストを送信しています, ID: {db_id}")
            req_id = f"delete_progress_{QtCore.QDateTime.currentSecsSinceEpoch()}"
            
            # 立即禁用删除按钮，防止重复点击
            self.t3_delete_btn.setEnabled(False)
            
            if self.is_server5_enabled:
                # 使用Server5专用客户端调用entries API
                self.server5_async_client.delete_async(f"/api/entries/{db_id}", req_id)
                self.log_employee_message(f"削除リクエストを送信しました (Server5 entries APIへ)")
            else:
                # 回退到原来的API
                self.async_http_client.delete_async(f"/sync/api/entries/{db_id}", req_id)
                self.log_employee_message(f"削除リクエストを送信しました (旧APIへ)")

    def on_table3_selection_changed(self, selected, deselected):
        """ 修改 b21 增加table3的交互处理。 - 表格选择变化时更新按钮状态 """
        # #20250711+16：20+修改的主题@program1.py - 修改选择处理逻辑适应QTableView
        try:
            # 获取选中的行
            selected_indexes = self.table3.selectionModel().selectedRows()
            is_selection = bool(selected_indexes)
            self.t3_change_btn.setEnabled(is_selection)
            self.t3_delete_btn.setEnabled(is_selection)
        except Exception as e:
            self.log_employee_message(f"❌ Table3选择处理失败: {e}")

    def _fetch_5xml_data_for_table3(self):
        """#20250711+10：15+修改的主题 - 使用Server5 entries API加载数据到table3"""
        self.table3_data_source = 'entries'  #20250711+10：15+修改的主题 - 确保使用entries数据源
        if isinstance(self.table3_current_month, QtCore.QDate):
            py_date = self.table3_current_month.toPyDate()
            display_month = py_date.strftime("%Y/%m")
            target_month = datetime(py_date.year, py_date.month, 1)
        else:
            display_month = self.table3_current_month.strftime("%Y/%m")
            target_month = self.table3_current_month
        self.log_employee_message(f"Table3に {display_month} のデータを読み込んでいます (Server5 entries APIから)...")
        QtCore.QTimer.singleShot(100, lambda: self._load_entries_from_server5_api(target_month))

    def _load_entries_from_server5_api(self, target_month: datetime):
        """2025/07/08 + 使用Server5 API加载entries数据到Table3"""
        try:
            # 计算日期范围
            start_date = target_month.strftime('%Y-%m-01')
            # 计算月末日期
            if target_month.month == 12:
                end_date = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
            end_date_str = end_date.strftime('%Y-%m-%d')

            self.log_employee_message(f"📡 调用Server5 API: /api/entries?employee_id={self.employee_id}&start_date={start_date}&end_date={end_date_str}")

            # 调用Server5 API
            result = self.server5_client.get_entries(
                employee_id=self.employee_id,
                start_date=start_date,
                end_date=end_date_str,
                limit=1000
            )

            if result.get("ok"):
                entries = result.get("data", [])
                self.table3_current_data = entries

                if entries:
                    self.log_employee_message(f"✅ 从Server5 API加载了 {len(entries)} 条entries记录")
                    # 转换为Table3显示格式
                    table3_records = []
                    for entry in entries:
                        # 字段映射：Server5 entries -> Table3显示
                        # 根据entries分区表的字段结构进行正确映射
                        record = {
                            'db_id': entry.get('id'),  # 内部ID
                            'external_id': entry.get('external_id'),  # MDB ID
                            '従業員ｺｰﾄﾞ': entry.get('employee_id', ''),
                            '日付': entry.get('entry_date', ''),  # 日期字段
                            '機種': entry.get('model', ''),  # model字段
                            '号機': entry.get('number', ''),  # number字段
                            '工場製番': entry.get('factory_number', ''),  # factory_number字段
                            '工事番号': entry.get('project_number', ''),  # project_number字段
                            'ﾕﾆｯﾄ番号': entry.get('unit_number', ''),  # unit_number字段
                            '区分': str(entry.get('category', '')),  # category字段
                            '項目': str(entry.get('item', '')),  # item字段
                            '時間': str(entry.get('duration', 0)),  # duration字段
                            '所属ｺｰﾄﾞ': entry.get('department', ''),  # department字段
                            'DB_ID': entry.get('external_id', '')  # 修复：应该映射到external_id，而不是id
                        }
                        table3_records.append(record)

                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table3_records)
                    )
                else:
                    self.log_employee_message("⚠️ Server5 API返回空数据")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ Server5 API调用失败: {error_msg}")
                # 可以在这里添加回退到直接数据库连接的逻辑

        except Exception as e:
            self.log_employee_message(f"❌ Server5 API调用异常: {e}")
            # 可以在这里添加回退到直接数据库连接的逻辑

    @QtCore.pyqtSlot(list)
    def _update_table3_ui(self, records):
        """在主线程中更新Table3 UI"""
        try:
            # #20250711+16：20+修改的主题@program1.py - 使用新的Model更新数据
            self.table3_model.update_data(records)
            # #20250711+16：40+修改的主题 - 数据更新后重新调整列宽度
            # #20250712+10：30+修改的主题 - 在数据加载后强制重新排序
            self.table3.sortByColumn(2, QtCore.Qt.SortOrder.AscendingOrder) # <--- 请添加这一行

            QtCore.QTimer.singleShot(100, self._adjust_table3_column_widths)
            self.log_employee_message(f"✅ Table3 UI更新完成 ({len(records)}条记录)")
        except Exception as e:
            self.log_employee_message(f"❌ Table3 UI更新失败: {e}")

    def setup_ui(self):
        """设置员工界面UI"""
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # 2506181810 tab: 创建整体垂直布局
        overall_layout = QtWidgets.QVBoxLayout(central_widget)
        
        # 2506181810 tab: 创建Tab容器
        self.tab_widget = QtWidgets.QTabWidget()
        overall_layout.addWidget(self.tab_widget)
        
        # 2506181810 tab: 创建Tab1 - 主要功能
        tab1_widget = QtWidgets.QWidget()
        self.tab_widget.addTab(tab1_widget, "メイン機能")
        
        # 2506181810 tab: 创建Tab1的布局
        tab1_layout = QtWidgets.QVBoxLayout(tab1_widget)
        
        # --- 上方区域 ---
        top_widget = QtWidgets.QWidget()
        top_layout = QtWidgets.QHBoxLayout(top_widget)
        
        # --- 左侧面板 ---
        left_widget = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout(left_widget)
        
        # --- 右侧面板 ---
        right_widget = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout(right_widget)

        # 欢迎标题 (放在左侧)
        welcome_label = QtWidgets.QLabel(f"ようこそ、{self.employee_name}さん！")
        welcome_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        welcome_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(welcome_label)
        
        # 员工信息显示区 (放在左侧)
        info_group = QtWidgets.QGroupBox("従業員情報")
        info_layout = QtWidgets.QFormLayout(info_group)
        
        # #20250711+16：20+修改的主题@program1.py - 改为QLabel和富HTML文本
        # 创建员工ID标签和显示内容
        id_label = QtWidgets.QLabel()
        id_label.setText("<span style='font-size: 12px; font-weight: bold; color: #333;'>従業員ID:</span>")
        id_content = QtWidgets.QLabel()
        id_content.setText(f"<span style='font-size: 14px; font-weight: bold; color: #0066cc; background-color: #f0f8ff; padding: 5px; border: 1px solid #ccc;'>{self.employee_id}</span>")
        id_content.setStyleSheet("QLabel { background-color: #f0f8ff; border: 1px solid #ccc; padding: 5px; }")
        info_layout.addRow(id_label, id_content)
        
        # 创建员工名称标签和显示内容
        name_label = QtWidgets.QLabel()
        name_label.setText("<span style='font-size: 12px; font-weight: bold; color: #333;'>従業員名:</span>")
        name_content = QtWidgets.QLabel()
        name_content.setText(f"<span style='font-size: 14px; font-weight: bold; color: #0066cc; background-color: #f0f8ff; padding: 5px; border: 1px solid #ccc;'>{self.employee_name}</span>")
        name_content.setStyleSheet("QLabel { background-color: #f0f8ff; border: 1px solid #ccc; padding: 5px; }")
        info_layout.addRow(name_label, name_content)
        
        left_layout.addWidget(info_group)
        
        # ================= 进度上传组 (放在左侧) =================
        progress_group = QtWidgets.QGroupBox("プロジェクト進捗アップロード")
        progress_layout = QtWidgets.QVBoxLayout(progress_group)
        
        # 修改，25061801  更改输入面板: 添加状态标签
        self.progress_status_label = QtWidgets.QLabel("編集可能")
        self.progress_status_label.setStyleSheet("font-weight: bold; color: blue;")
        self.progress_status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        progress_layout.addWidget(self.progress_status_label)
        
        input_panel = QtWidgets.QWidget()
        



        # —— 让 input_panel 能水平扩展，否则 grid 列伸缩不起作用
        input_panel.setSizePolicy(
            QtWidgets.QSizePolicy.Policy.Expanding,
            QtWidgets.QSizePolicy.Policy.Preferred
        )
        form_layout = QtWidgets.QGridLayout(input_panel)



        
        self.input_fields = {}
        labels = {
            'employee_id': '従業員ID:', 'date': '日付:',
            'model': '機種:', 'number': '号機:', 'factory_number': '工場製番:',
            'project_number': '工事番号:', 'unit_number': 'ﾕﾆｯﾄ番号:',
            'category': '区分 (必須):', 'item': '項目 (必須):',
            'time': '時間 (必須):', 'department': '部門:'
        }
        # 修改，25061804  输入面板
        row = 0
        for field, label_text in labels.items():
            # #20250711+16：20+修改的主题@program1.py - 改为富HTML文本的QLabel
            label = QtWidgets.QLabel()
            if '(必須)' in label_text:
                # 必须字段用红色高亮
                base_text = label_text.replace(' (必須)', '')
                html_text = f"<span style='font-size: 11px; font-weight: bold; color: #333;'>{base_text}</span> <span style='font-size: 10px; font-weight: bold; color: #ff0000; background-color: #ffe6e6;'>(必須)</span>"
            else:
                # 可选字段用普通样式
                html_text = f"<span style='font-size: 11px; font-weight: bold; color: #666;'>{label_text}</span>"
            label.setText(html_text)
            form_layout.addWidget(label, row, 0, 1, 1) # span 1 row, 1 col
            
            if field == 'date':
                date_entry = QtWidgets.QLineEdit()
                # 修改，25061804 输入面板 - 明确设置尺寸策略以确保输入框会扩展
                date_entry.setSizePolicy(QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Fixed)
                # 修改，25061804 输入面板 - 设置最小宽度确保可见性
                date_entry.setMinimumWidth(60)
                date_entry.setText(datetime.now().strftime("%Y/%m/%d"))
                self.input_fields[field] = date_entry
                date_entry.textChanged.connect(self.on_input_field_changed)
                
                prev_day_btn = QtWidgets.QPushButton("前日")
                prev_day_btn.setMinimumWidth(50)
                prev_day_btn.clicked.connect(self.on_prev_day_clicked)
                # —— 让按钮也能按 stretch 拉伸
                prev_day_btn.setSizePolicy(
                    QtWidgets.QSizePolicy.Policy.Expanding,
                    QtWidgets.QSizePolicy.Policy.Fixed
                )
                
                next_day_btn = QtWidgets.QPushButton("翌日")
                next_day_btn.setMinimumWidth(50)
                next_day_btn.clicked.connect(self.on_next_day_clicked)
                next_day_btn.setSizePolicy(
                    QtWidgets.QSizePolicy.Policy.Expanding,
                    QtWidgets.QSizePolicy.Policy.Fixed
                )                
                
                calendar_btn = QtWidgets.QPushButton("📅")
                calendar_btn.setFixedSize(prev_day_btn.sizeHint().height(), prev_day_btn.sizeHint().height())
                calendar_btn.clicked.connect(self.on_calendar_clicked)
                
                date_layout = QtWidgets.QHBoxLayout()
                # 修改，25061804 输入面板 - 重新设置布局，不使用addWidget的stretch参数
                date_layout.addWidget(date_entry)
                date_layout.addWidget(prev_day_btn)
                date_layout.addWidget(next_day_btn)
                date_layout.addWidget(calendar_btn)
                
                # 修改，25061804 输入面板 - 明确设置各控件的伸缩比例为 6:3:3:1
                date_layout.setStretch(0, 6)  # date_entry
                date_layout.setStretch(1, 3)  # prev_day_btn
                date_layout.setStretch(2, 3)  # next_day_btn
                date_layout.setStretch(3, 1)  # calendar_btn
                
                form_layout.addLayout(date_layout, row, 1, 1, 1) # span 1 row, 1 col

            else:
                entry = QtWidgets.QLineEdit()
                if field == 'employee_id':
                    entry.setText(self.employee_id)
                    entry.setReadOnly(True)
                
                self.input_fields[field] = entry
                form_layout.addWidget(entry, row, 1, 1, 1)
                entry.textChanged.connect(self.on_input_field_changed)
            
            row += 1        

        # —— 给 form_layout 的第 1 列（index=1）分配所有剩余空间
        form_layout.setColumnStretch(1, 1)
        progress_layout.addWidget(input_panel)

        
        # 修改，25061801  更改输入面板: 创建一个水平布局来放置按钮
        progress_button_layout = QtWidgets.QHBoxLayout()

        self.upload_progress_btn = QtWidgets.QPushButton("データ送信（ボタン4）")
        self.upload_progress_btn.clicked.connect(self.on_upload_progress_clicked)
        progress_button_layout.addWidget(self.upload_progress_btn)

        # 修改，25061801  更改输入面板: 添加新的编辑按钮
        self.update_data_btn = QtWidgets.QPushButton("データ変更（ボタン5）")
        self.update_data_btn.clicked.connect(self.on_update_data_clicked)
        self.update_data_btn.setEnabled(False)
        self.update_data_btn.setVisible(False)
        progress_button_layout.addWidget(self.update_data_btn)

        self.cancel_update_btn = QtWidgets.QPushButton("変更キャンセル")
        self.cancel_update_btn.clicked.connect(self.on_cancel_update_clicked)
        self.cancel_update_btn.setVisible(False)
        progress_button_layout.addWidget(self.cancel_update_btn)

        progress_layout.addLayout(progress_button_layout)
        
        # 修改 b1 布局移动: table3 被移出此模块
        left_layout.addWidget(progress_group)

                # ================= 传感器数据显示组 (放在左侧) =================
        sensor_group0 = QtWidgets.QGroupBox("センサーデータ")
        sensor_layout0 = QtWidgets.QVBoxLayout(sensor_group0)
        
        left_layout.addWidget(sensor_group0)
        left_layout.addStretch()



        # 操作日志区 (放在右侧顶部)
        log_group = QtWidgets.QGroupBox("操作ログ")
        log_layout = QtWidgets.QVBoxLayout(log_group)
        self.employee_log = QtWidgets.QTextEdit()
        self.employee_log.setReadOnly(True)
        log_layout.addWidget(self.employee_log)
        #right_layout.addWidget(log_group)
        # ================= 修改 b1 布局移动: 将图表模块移到右侧 =================
        chart_area = self._create_chart_area()
        #right_layout.addWidget(chart_area)

        # ================= 修改 b1 布局移动: 创建新的进度表格模块 =================
        completed_progress_group = QtWidgets.QGroupBox("ログイン完了済み進捗")
        completed_progress_layout = QtWidgets.QHBoxLayout(completed_progress_group)

        #  修改 b21 增加table3的交互处理。 - Left Controls
        left_controls = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout(left_controls)
        left_layout.setSpacing(10)

        self.t3_prev_month_btn = QtWidgets.QPushButton("先月データ表示")
        self.t3_prev_month_btn.setToolTip("Server5 entries APIから先月のデータを読み込み")  #20250711+10：15+修改的主题
        self.t3_prev_month_btn.clicked.connect(self.on_show_prev_month_clicked)
        #left_layout.addWidget(self.t3_prev_month_btn)

        self.t3_curr_month_btn = QtWidgets.QPushButton("今月データ表示")
        self.t3_curr_month_btn.setToolTip("Server5 entries APIから今月のデータを読み込み")  #20250711+10：15+修改的主题
        self.t3_curr_month_btn.clicked.connect(self.on_show_curr_month_clicked)
        #left_layout.addWidget(self.t3_curr_month_btn)
        
        self.t3_next_month_btn = QtWidgets.QPushButton("来月データ表示")
        self.t3_next_month_btn.setToolTip("Server5 entries APIから来月のデータを読み込み")  #20250711+10：15+修改的主题
        self.t3_next_month_btn.clicked.connect(self.on_show_next_month_clicked)
        #left_layout.addWidget(self.t3_next_month_btn)
        
        left_layout.addSpacing(20)

        self.t3_change_btn = QtWidgets.QPushButton("変更")
        self.t3_change_btn.setEnabled(False)
        self.t3_change_btn.clicked.connect(self.on_t3_change_clicked)
        left_layout.addWidget(self.t3_change_btn)
        
        self.t3_delete_btn = QtWidgets.QPushButton("削除")
        self.t3_delete_btn.setEnabled(False)
        self.t3_delete_btn.clicked.connect(self.on_t3_delete_clicked)
        left_layout.addWidget(self.t3_delete_btn)
        
        # #20250711+12：45+修改的主题@program1.py - 添加Table3月份显示文本框
        left_layout.addSpacing(10)
        
        #self.t3_month_display = QtWidgets.QLineEdit()
        self.t3_month_display = QtWidgets.QLabel()
        #self.t3_month_display.setReadOnly(True)
        self.t3_month_display.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.t3_month_display.setStyleSheet("QLabel { background-color: #f0f0f0; font-weight: bold; }")

        # 构建 HTML 字符串
        display_text = (
            f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{datetime.now().strftime('%Y/%m')}</span><br>"
            f"の mdb工時登録情報"
        )
        #self.t3_month_display.setText(display_text) 
        #self.t3_month_display.setText(f"{datetime.now().strftime('%Y/%m')} の mdb工時登録情報")

        # 初始化文本也使用换行符
        #left_layout.addWidget(self.t3_month_display)

        left_layout.addStretch()
        left_controls.setLayout(left_layout)
        
        #  修改 b21 增加table3的交互处理。 - Right Table
        # #20250711+16：20+修改的主题@program1.py - 改为QTableView + 自定义Model
        self.table3 = QtWidgets.QTableView()
        self.table3_headers = ['DB_ID', '従業員ｺｰﾄﾞ', '日付', '機種', '号機', '工場製番', '工事番号', 'ﾕﾆｯﾄ番号', '区分', '項目', '時間', '所属ｺｰﾄﾞ']
        self.table3_model = Table3Model(data=[], headers=self.table3_headers)
        self.table3.setModel(self.table3_model)
        
        # #20250711+16：20+修改的主题@program1.py - 启用排序功能
        self.table3.setSortingEnabled(True)
        self.table3.sortByColumn(2, QtCore.Qt.SortOrder.AscendingOrder)  # 默认按日期正序排列   DescendingOrder 倒序
        
        # 设置选择模式和行为
        self.table3.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table3.setSelectionMode(QtWidgets.QAbstractItemView.SelectionMode.SingleSelection)
        self.table3.setAlternatingRowColors(True)
        
        # 连接选择改变信号
        self.table3.selectionModel().selectionChanged.connect(self.on_table3_selection_changed)
        
        # #20250711+16：40+修改的主题 - 调整列宽度规则
        self._adjust_table3_column_widths()

        completed_progress_layout.addWidget(left_controls, 1) # 比例 1
        completed_progress_layout.addWidget(self.table3, 5)     # 比例 5
        
        # 修改 b2 布局微调: 使用固定像素高度来设置右侧面板内各模块的高度
        log_group.setFixedHeight(80)  # 传感器组固定高度100像素
        chart_area.setFixedHeight(430)    # 图表区域固定高度300像素
        completed_progress_group.setFixedHeight(300)  # 进度表格固定高度600像素
        
        
        # ================= 修改 b1 布局移动: 将扩展功能模块定义并移到下方 =================
        ext_group = QtWidgets.QGroupBox("拡張機能")
        ext_layout = QtWidgets.QHBoxLayout(ext_group)  # Changed to QHBoxLayout for left-right arrangement
        
        # Left side controls
        left_controls = QtWidgets.QWidget()
        left_controls_layout = QtWidgets.QVBoxLayout(left_controls)
        
        # #20250711+12：45+修改的主题@program1.py - 删除"输入n"相关UI，添加3个月份切换按键
        # Table1 月份切换按钮（垂直排列）
        self.t1_prev_month_btn = QtWidgets.QPushButton("先月データ表示")
        self.t1_prev_month_btn.setToolTip("Server5 timeprotab APIから先月のデータを読み込み")
        self.t1_prev_month_btn.clicked.connect(self.on_table1_prev_month_clicked)
        #left_controls_layout.addWidget(self.t1_prev_month_btn)

        self.t1_curr_month_btn = QtWidgets.QPushButton("今月データ表示")
        self.t1_curr_month_btn.setToolTip("Server5 timeprotab APIから今月のデータを読み込み")
        self.t1_curr_month_btn.clicked.connect(self.on_table1_curr_month_clicked)
        #left_controls_layout.addWidget(self.t1_curr_month_btn)
        
        self.t1_next_month_btn = QtWidgets.QPushButton("来月データ表示")
        self.t1_next_month_btn.setToolTip("Server5 timeprotab APIから来月のデータを読み込み")
        self.t1_next_month_btn.clicked.connect(self.on_table1_next_month_clicked)
        #left_controls_layout.addWidget(self.t1_next_month_btn)
        
        # #20250711+12：45+修改的主题@program1.py - 添加Table1月份显示文本框
        left_controls_layout.addSpacing(10)
        #self.t1_month_display = QtWidgets.QLineEdit()
        #self.t1_month_display.setReadOnly(True)
        #self.t1_month_display.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        #self.t1_month_display.setStyleSheet("QLineEdit { background-color: #e8f4f8; font-weight: bold; }")
        #self.t1_month_display.setText(f"{datetime.now().strftime('%Y/%m')}\nの timepro工時登録情報")
                
        # 将 QLineEdit 更改为 QLabel
        self.t1_month_display = QtWidgets.QLabel() 
        # QLabel 本身就是只读的，所以不需要 setReadOnly(True)
        self.t1_month_display.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter) 
        # 样式表也需要从 QLineEdit 对应修改为 QLabel
        self.t1_month_display.setStyleSheet("QLabel { background-color: #e8f4f8; font-weight: bold; }")

                # 2. 初始化文本也使用 HTML 格式
        # 提取日期部分
        current_date_str = datetime.now().strftime('%Y/%m')
        # 构建 HTML 字符串
        initial_display_text = (
            f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{current_date_str}</span><br>"
            f"の timepro工時登録情報"
        )
        #self.t1_month_display.setText(initial_display_text)
        
        # 初始文本也使用换行符
        #self.t1_month_display.setText(f"{datetime.now().strftime('%Y/%m')}\nの timepro工時登録情報")
        #left_controls_layout.addWidget(self.t1_month_display)
        
        left_controls_layout.addSpacing(10)
        
        # Add left controls to main layout with stretch
        ext_layout.addWidget(left_controls, 1)  # 1:8 ratio
        
        # #20250711+16：40+修改的主题 - 将table1改为QTableView + 自定义模型
        # Right side table
        self.table1 = QtWidgets.QTableView()
        self.table1_headers = [
            '日付', '曜日', 'カレンダ', '不在', '事由', '出勤時刻', '退勤時刻',
            '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業',
            '外出時間', '戻り時間', 'コメント', 'MC_出勤', 'MC_退勤', '勤務区分'
        ]
        self.table1_model = Table1Model([], self.table1_headers, self)
        self.table1.setModel(self.table1_model)
        self.table1.setSortingEnabled(True)
        self.table1.setAlternatingRowColors(True)
        self.table1.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table1.setSelectionMode(QtWidgets.QAbstractItemView.SelectionMode.SingleSelection)
        self._adjust_table1_column_widths()
        ext_layout.addWidget(self.table1, 12)  # 1:8 ratio
        

        # --- 右边布局和，左边布局 ---

        
        right_layout.addWidget(chart_area, 8)
        right_layout.addWidget(completed_progress_group, 5)        
        right_layout.addWidget(ext_group, 3)
        #right_layout.addWidget(log_group)
        # 组合左右面板
        # 修改 b2 布局微调 , 使用比例: 设置左右面板的宽度比例为1:2
        top_layout.addWidget(left_widget, 2)
        top_layout.addWidget(right_widget, 5)


        # --- 底部控制按钮 ---
        control_buttons = QtWidgets.QHBoxLayout()
        self.back_btn = QtWidgets.QPushButton("メイン画面に戻る")
        self.back_btn.clicked.connect(self.return_to_main)
        self.logout_btn = QtWidgets.QPushButton("ログアウト")
        self.logout_btn.clicked.connect(self.logout)
        control_buttons.addStretch()
        control_buttons.addWidget(self.back_btn)
        control_buttons.addWidget(self.logout_btn)
        
        # 2506181810 tab: 将原有内容添加到Tab1布局
        # 修改 b2 布局微调 , 使用比例: 设置上下布局的比例为4:3
        tab1_layout.addWidget(top_widget, 4)
        #tab1_layout.addWidget(ext_group, 3)
        tab1_layout.addWidget(log_group, 3)
        tab1_layout.addLayout(control_buttons)
        
        
        # 20250618.19:20 实时信息交流 - 创建聊天Tab
        chat_tab = QtWidgets.QWidget()
        self.tab_widget.addTab(chat_tab, "リアルタイム情報交換")
        
        # 20250618.19:20 实时信息交流 - 聊天Tab的主布局
        chat_main_layout = QtWidgets.QHBoxLayout(chat_tab)
        
        # 20250618.19:20 实时信息交流 - 左侧用户列表区域
        left_panel = QtWidgets.QWidget()
        left_panel.setFixedWidth(250)
        left_layout = QtWidgets.QVBoxLayout(left_panel)
        
        # 20250618.19:20 实时信息交流 - 连接状态显示
        self.chat_status_label = QtWidgets.QLabel("接続状態：未接続")
        self.chat_status_label.setStyleSheet("font-weight: bold; color: red; padding: 5px; border: 1px solid #ccc;")
        left_layout.addWidget(self.chat_status_label)
        
        # 20250618.19:20 实时信息交流 - 在线用户计数
        self.online_count_label = QtWidgets.QLabel("オンラインユーザー：0")
        self.online_count_label.setStyleSheet("font-weight: bold; color: blue; padding: 5px;")
        left_layout.addWidget(self.online_count_label)
        
        # 20250618.19:20 实时信息交流 - 用户列表
        user_list_group = QtWidgets.QGroupBox("オンラインユーザーリスト")
        user_list_layout = QtWidgets.QVBoxLayout(user_list_group)
        
        self.user_list_widget = QtWidgets.QListWidget()
        self.user_list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                background-color: #f9f9f9;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        user_list_layout.addWidget(self.user_list_widget)
        left_layout.addWidget(user_list_group)
        
        # 20250618.19:20 实时信息交流 - 连接控制按钮
        chat_controls = QtWidgets.QVBoxLayout()
        
        # 连接/断开按钮行
        connect_row = QtWidgets.QHBoxLayout()
        self.connect_chat_btn = QtWidgets.QPushButton("チャット接続")
        self.connect_chat_btn.clicked.connect(self.connect_to_chat)
        self.connect_chat_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        
        # 2025/06/26.11:50+分离ui操作 - 添加调试按钮
        self.test_chat_btn = QtWidgets.QPushButton("チャット機能テスト")
        self.test_chat_btn.clicked.connect(self.test_chat_function)
        self.test_chat_btn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 6px;")
        
        self.disconnect_chat_btn = QtWidgets.QPushButton("接続切断")
        self.disconnect_chat_btn.clicked.connect(self.disconnect_from_chat)
        self.disconnect_chat_btn.setEnabled(False)
        self.disconnect_chat_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        
        connect_row.addWidget(self.connect_chat_btn)
        connect_row.addWidget(self.disconnect_chat_btn)
        connect_row.addWidget(self.test_chat_btn)  # 2025/06/26.11:50+分离ui操作 - 添加测试按钮
        chat_controls.addLayout(connect_row)
        
        # 20250618.20:30 增加加密和文件共享以及数据库 - 新功能按钮
        new_features_row = QtWidgets.QHBoxLayout()
        
        self.private_chat_btn = QtWidgets.QPushButton("プライベートチャット")
        self.private_chat_btn.clicked.connect(self.send_private_message)
        self.private_chat_btn.setEnabled(False)
        self.private_chat_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 6px;")
        
        self.file_upload_btn = QtWidgets.QPushButton("ファイルアップロード")
        self.file_upload_btn.clicked.connect(self.upload_file_to_chat)
        self.file_upload_btn.setEnabled(False)
        self.file_upload_btn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 6px;")
        
        new_features_row.addWidget(self.private_chat_btn)
        new_features_row.addWidget(self.file_upload_btn)
        chat_controls.addLayout(new_features_row)
        
        # 聊天室管理按钮
        room_mgmt_row = QtWidgets.QHBoxLayout()
        
        self.room_mgmt_btn = QtWidgets.QPushButton("チャットルーム管理")
        self.room_mgmt_btn.clicked.connect(self.show_room_management)
        self.room_mgmt_btn.setStyleSheet("background-color: #607D8B; color: white; font-weight: bold; padding: 6px;")
        
        room_mgmt_row.addWidget(self.room_mgmt_btn)
        chat_controls.addLayout(room_mgmt_row)
        
        left_layout.addLayout(chat_controls)
        
        # 20250618.19:20 实时信息交流 - 右侧聊天区域
        right_panel = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout(right_panel)
        
        # 20250618.19:20 实时信息交流 - 聊天标题
        chat_title = QtWidgets.QLabel("MySuite リアルタイムチャットルーム")
        chat_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        chat_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; padding: 10px; border-bottom: 2px solid #2E86AB;")
        right_layout.addWidget(chat_title)
        
        # 20250618.19:20 实时信息交流 - 聊天消息显示区
        self.chat_display = QtWidgets.QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                background-color: white;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        right_layout.addWidget(self.chat_display)
        
        # 20250618.19:20 实时信息交流 - 消息输入区
        input_panel = QtWidgets.QWidget()
        input_layout = QtWidgets.QHBoxLayout(input_panel)
        
        self.message_input = QtWidgets.QLineEdit()
        self.message_input.setPlaceholderText("メッセージを入力...")
        self.message_input.setEnabled(False)
        self.message_input.setStyleSheet("padding: 8px; font-size: 12px; border: 1px solid #ccc;")
        self.message_input.returnPressed.connect(self.send_message)
        
        self.send_btn = QtWidgets.QPushButton("送信")
        self.send_btn.setEnabled(False)
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px 15px;")
        
        input_layout.addWidget(self.message_input, 4)
        input_layout.addWidget(self.send_btn, 1)
        right_layout.addWidget(input_panel)
        
        # 20250618.19:20 实时信息交流 - 组合左右面板
        chat_main_layout.addWidget(left_panel)
        chat_main_layout.addWidget(right_panel, 1)
        
        # 20250618.19:20 实时信息交流 - 初始化聊天相关变量
        self.chat_websocket = None
        self.chat_connected = False
        self.jwt_token = None
        
        # 20250619.17:00 视频监控 - 创建视频监控Tab
        video_tab = QtWidgets.QWidget()
        #self.tab_widget.addTab(video_tab, "ビデオ監視")
        
        # 20250619.17:00 视频监控 - 视频Tab的主布局
        video_main_layout = QtWidgets.QVBoxLayout(video_tab)
        
        # 20250619.17:00 视频监控 - 顶部控制面板
        video_control_panel = QtWidgets.QWidget()
        video_control_layout = QtWidgets.QHBoxLayout(video_control_panel)
        
        # 服务器状态显示
        self.video_status_label = QtWidgets.QLabel("サービス状態：未接続")
        self.video_status_label.setStyleSheet("font-weight: bold; color: red; padding: 5px; border: 1px solid #ccc;")
        video_control_layout.addWidget(self.video_status_label)
        
        # 连接控制按钮
        self.connect_video_btn = QtWidgets.QPushButton("ビデオ接続")
        self.connect_video_btn.clicked.connect(self.connect_to_video)
        self.connect_video_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        
        self.disconnect_video_btn = QtWidgets.QPushButton("接続切断")
        self.disconnect_video_btn.clicked.connect(self.disconnect_from_video)
        self.disconnect_video_btn.setEnabled(False)
        self.disconnect_video_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        
        # 快照按钮
        self.snapshot_btn = QtWidgets.QPushButton("获取快照")
        self.snapshot_btn.clicked.connect(self.get_video_snapshot)
        self.snapshot_btn.setEnabled(False)
        self.snapshot_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px;")
        
        # 重启摄像头按钮（解决显示问题）
        self.restart_camera_btn = QtWidgets.QPushButton("重启摄像头")
        self.restart_camera_btn.clicked.connect(self.restart_video_camera)
        self.restart_camera_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        
        # YOLO AI检测按钮
        self.yolo_init_btn = QtWidgets.QPushButton("初始化YOLO")
        self.yolo_init_btn.clicked.connect(self.initialize_yolo_model)
        self.yolo_init_btn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 8px;")
        
        self.yolo_toggle_btn = QtWidgets.QPushButton("启用AI检测")
        self.yolo_toggle_btn.clicked.connect(self.toggle_yolo_detection)
        self.yolo_toggle_btn.setEnabled(False)
        self.yolo_toggle_btn.setStyleSheet("background-color: #673AB7; color: white; font-weight: bold; padding: 8px;")
        
        video_control_layout.addWidget(self.connect_video_btn)
        video_control_layout.addWidget(self.disconnect_video_btn)
        video_control_layout.addWidget(self.snapshot_btn)
        video_control_layout.addWidget(self.restart_camera_btn)
        video_control_layout.addWidget(self.yolo_init_btn)
        video_control_layout.addWidget(self.yolo_toggle_btn)
        video_control_layout.addStretch()
        
        video_main_layout.addWidget(video_control_panel)
        
        # 20250619.17:00 视频监控 - 视频显示区域
        video_display_group = QtWidgets.QGroupBox("实时视频画面 - Server4")
        video_display_layout = QtWidgets.QVBoxLayout(video_display_group)
        
        # 视频显示标签
        self.video_display_label = QtWidgets.QLabel("等待视频连接...")
        self.video_display_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.video_display_label.setMinimumSize(640, 480)
        self.video_display_label.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                background-color: #f0f0f0;
                font-size: 16px;
                color: #666;
            }
        """)
        self.video_display_label.setScaledContents(True)
        video_display_layout.addWidget(self.video_display_label)
        
        video_main_layout.addWidget(video_display_group)
        
        # 20250619.17:00 视频监控 - 底部信息面板
        video_info_panel = QtWidgets.QWidget()
        video_info_layout = QtWidgets.QHBoxLayout(video_info_panel)
        
        # 统计信息显示
        self.video_stats_label = QtWidgets.QLabel("FPS: 0 | 总帧数: 0 | 连接数: 0")
        self.video_stats_label.setStyleSheet("font-size: 12px; color: #666; padding: 5px;")
        video_info_layout.addWidget(self.video_stats_label)
        
        # 视频配置信息
        self.video_config_label = QtWidgets.QLabel("分辨率: - | 质量: -")
        self.video_config_label.setStyleSheet("font-size: 12px; color: #666; padding: 5px;")
        video_info_layout.addWidget(self.video_config_label)
        
        # YOLO检测信息
        self.yolo_stats_label = QtWidgets.QLabel("AI检测: 未初始化")
        self.yolo_stats_label.setStyleSheet("font-size: 12px; color: #666; padding: 5px;")
        video_info_layout.addWidget(self.yolo_stats_label)
        
        video_info_layout.addStretch()
        video_main_layout.addWidget(video_info_panel)
        
        # 20250619.17:00 视频监控 - 初始化视频相关变量
        self.video_websocket = None
        self.video_connected = False
        self.video_client_id = None
        
        # YOLO AI检测相关变量
        self.yolo_initialized = False
        self.yolo_enabled = False
        self.yolo_loading = False
        
        # 20250708 + 添加Tab4 - 以后的扩展
        tab4_widget = QtWidgets.QWidget()
        self.tab_widget.addTab(tab4_widget, "今後の拡張")
        
        # Tab4的布局
        tab4_layout = QtWidgets.QVBoxLayout(tab4_widget)
        
        # 添加说明标签
        expansion_label = QtWidgets.QLabel("今後の拡張機能")
        expansion_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 20px;")
        expansion_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        tab4_layout.addWidget(expansion_label)
        # ================= 传感器数据显示组 (放在左侧) =================
        sensor_group = QtWidgets.QGroupBox("センサーデータ")
        sensor_layout = QtWidgets.QVBoxLayout(sensor_group)
        
        sensor_info_layout = QtWidgets.QGridLayout()
        sensor_info_layout.addWidget(QtWidgets.QLabel("センサーID:"), 0, 0)
        self.sensor_id_display = QtWidgets.QLineEdit()
        self.sensor_id_display.setReadOnly(True)
        sensor_info_layout.addWidget(self.sensor_id_display, 0, 1)
        
        sensor_info_layout.addWidget(QtWidgets.QLabel("センサー値:"), 1, 0)
        self.sensor_value_display = QtWidgets.QLineEdit()
        self.sensor_value_display.setReadOnly(True)
        sensor_info_layout.addWidget(self.sensor_value_display, 1, 1)
        
        sensor_info_layout.addWidget(QtWidgets.QLabel("更新時刻:"), 2, 0)
        self.sensor_timestamp_display = QtWidgets.QLineEdit()
        self.sensor_timestamp_display.setReadOnly(True)
        sensor_info_layout.addWidget(self.sensor_timestamp_display, 2, 1)
        
        sensor_layout.addLayout(sensor_info_layout)
        
        status_layout = QtWidgets.QHBoxLayout()
        self.mongo_status_btn = QtWidgets.QPushButton("MongoDB状態確認")
        self.mongo_status_btn.clicked.connect(self.check_mongo_status)
        status_layout.addWidget(self.mongo_status_btn)
        self.redis_status_btn = QtWidgets.QPushButton("Redis状態確認")
        self.redis_status_btn.clicked.connect(self.check_redis_status)
        status_layout.addWidget(self.redis_status_btn)
        sensor_layout.addLayout(status_layout)
        #left_layout.addWidget(sensor_group)
        #left_layout.addStretch()
        tab4_layout.addWidget(sensor_group)

        # 添加弹性空间
        tab4_layout.addStretch()
        
        # 添加详细说明
        description_label = QtWidgets.QLabel(
            "このタブは今後の機能拡張のために予約されています。\n"
            "新しい機能が追加される際に、このタブに実装されます。"
        )
        description_label.setStyleSheet("font-size: 14px; color: #666; margin: 20px; line-height: 1.5;")
        description_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        description_label.setWordWrap(True)
        tab4_layout.addWidget(description_label)
        
        # 添加弹性空间
        tab4_layout.addStretch()
        
        self.log_employee_message(f"従業員 {self.employee_name} ({self.employee_id}) がログインしました")
        
        # #20250711+13：10+修改的主题 - 初始化按键颜色，当月按键设为浅绿色
        QtCore.QTimer.singleShot(100, self._initialize_button_colors)
        
        # #20250711+16：40+修改的主题 - 初始化全局时间显示
        QtCore.QTimer.singleShot(150, lambda: self._update_global_time_display())
    
    # #20250711+13：10+修改的主题 - 添加按键颜色管理方法
    def _update_button_colors(self, area: str, active_button: str):
        """更新指定区域的按键颜色
        
        Args:
            area: 'table1', 'table3', 'chart'
            active_button: 'prev', 'curr', 'next'
        """
        # 定义按键样式
        active_style = "QPushButton { background-color: #90EE90; font-weight: bold; }"  # 浅绿色
        default_style = ""  # 默认样式
        
        if area == 'table1':
            # 更新Table1按键颜色
            self.t1_prev_month_btn.setStyleSheet(active_style if active_button == 'prev' else default_style)
            self.t1_curr_month_btn.setStyleSheet(active_style if active_button == 'curr' else default_style)
            self.t1_next_month_btn.setStyleSheet(active_style if active_button == 'next' else default_style)
            self.table1_active_button = active_button
            
        elif area == 'table3':
            # 更新Table3按键颜色
            self.t3_prev_month_btn.setStyleSheet(active_style if active_button == 'prev' else default_style)
            self.t3_curr_month_btn.setStyleSheet(active_style if active_button == 'curr' else default_style)
            self.t3_next_month_btn.setStyleSheet(active_style if active_button == 'next' else default_style)
            self.table3_active_button = active_button
            
        elif area == 'chart':
            # 更新Chart按键颜色
            self.chart_prev_month_btn.setStyleSheet(active_style if active_button == 'prev' else default_style)
            self.chart_curr_month_btn.setStyleSheet(active_style if active_button == 'curr' else default_style)
            self.chart_next_month_btn.setStyleSheet(active_style if active_button == 'next' else default_style)
            self.chart_active_button = active_button
            
        elif area == 'global':
            # #20250711+16：40+修改的主题 - 更新全局控制面板按键颜色
            global_active_style = "background-color: #90EE90; color: #333; font-size: 14px; font-weight: bold; padding: 8px 16px; border-radius: 4px; border: 1px solid #4caf50;"
            global_default_style = "background-color: #ddd; color: #333; font-size: 14px; font-weight: bold; padding: 8px 16px; border-radius: 4px; border: 1px solid #ccc;"
            
            self.global_prev_btn.setStyleSheet(global_active_style if active_button == 'prev' else global_default_style)
            self.global_current_btn.setStyleSheet(global_active_style if active_button == 'curr' else global_default_style)
            self.global_next_btn.setStyleSheet(global_active_style if active_button == 'next' else global_default_style)
            self.global_active_button = active_button
    
    def _initialize_button_colors(self):
        """#20250711+13：10+修改的主题 - 初始化所有按键颜色，当月按键设为浅绿色"""
        self._update_button_colors('table1', 'curr')
        self._update_button_colors('table3', 'curr')
        self._update_button_colors('chart', 'curr')
        # #20250711+16：40+修改的主题 - 初始化全局控制面板按键颜色
        self._update_button_colors('global', 'curr')
        
    # #20250711+16：20+修改的主题@program1.py - 添加month_combo同步方法
    def _sync_month_combo_selection(self, target_month: datetime):
        """同步month_combo的选中项到指定月份"""
        try:
            target_month_code = target_month.strftime('%Y%m')
            
            # 遍历month_combo，寻找匹配的月份
            for i in range(self.month_combo.count()):
                item_data = self.month_combo.itemData(i)
                if item_data and item_data.get("month_code") == target_month_code:
                    # 暂时断开信号连接，避免触发load_selected_chart
                    self.month_combo.currentIndexChanged.disconnect()
                    self.month_combo.setCurrentIndex(i)
                    # 重新连接信号
                    self.month_combo.currentIndexChanged.connect(self.load_selected_chart)
                    self.log_employee_message(f"📅 Month_combo已同步到: {target_month.strftime('%Y年%m月')}")
                    return True
            
            self.log_employee_message(f"⚠️ 在month_combo中未找到 {target_month.strftime('%Y年%m月')}")
            return False
                    
        except Exception as e:
            self.log_employee_message(f"❌ 同步month_combo失败: {e}")
            return False
            
    # #20250711+16：40+修改的主题 - 添加Table3列宽度调整方法
    def _adjust_table3_column_widths(self):
        """根据内容调整Table3的列宽度"""
        try:
            # 计算基准字体宽度
            font_metrics = self.table3.fontMetrics()
            char_width = font_metrics.averageCharWidth()
            
            # 设置最小和最大宽度（以字符数计算）
            min_char_count = 3  # 最小3个字符宽度
            max_char_count = 20 # 最大20个字符宽度
            min_width = char_width * min_char_count
            max_width = char_width * max_char_count
            
            # 为每列设置合适的宽度
            column_configs = {
                'DB_ID': 10,           # 数字ID，中等宽度
                '従業員ｺｰﾄﾞ': 10,        # 员工代码，中等宽度
                '日付': 12,            # 日期，稍宽
                '機種': 8,             # 机种，中等宽度
                '号機': 5,             # 号机，较窄
                '工場製番': 10,         # 工厂编号，中等宽度
                '工事番号': 10,         # 工事编号，中等宽度
                'ﾕﾆｯﾄ番号': 8,          # 单元编号，中等宽度
                '区分': 6,             # 区分，较窄
                '項目': 15,            # 项目，较宽
                '時間': 6,             # 时间，较窄
                '所属ｺｰﾄﾞ': 8           # 所属代码，中等宽度
            }
            
            # 应用列宽度设置
            for col, header in enumerate(self.table3_headers):
                if header in column_configs:
                    char_count = column_configs[header]
                    # 确保在最小和最大范围内
                    char_count = max(min_char_count, min(max_char_count, char_count))
                    width = char_width * char_count
                else:
                    # 默认宽度
                    width = char_width * 8
                    
                self.table3.setColumnWidth(col, width)
                
            self.log_employee_message(f"✅ Table3列宽度调整完成")
            
        except Exception as e:
            self.log_employee_message(f"❌ Table3列宽度调整失败: {e}")
            
    # #20250711+16：40+修改的主题 - 添加Table1列宽度调整方法
    def _adjust_table1_column_widths(self):
        """根据内容调整Table1的列宽度"""
        try:
            # 计算基准字体宽度
            font_metrics = self.table1.fontMetrics()
            char_width = font_metrics.averageCharWidth()
            
            # 设置最小和最大宽度（以字符数计算）
            min_char_count = 3  # 最小3个字符宽度
            max_char_count = 20 # 最大20个字符宽度
            
            # 为每列设置合适的宽度
            column_configs = {
                '日付': 12,            # 日期，稍宽
                '曜日': 3,             # 曜日，较窄
                'カレンダ': 8,          # 日历，中等宽度
                '不在': 4,             # 不在，较窄
                '勤務区分': 8,          # 勤务区分，中等宽度
                '事由': 4,            # 事由，稍宽
                '出勤時刻': 10,         # 出勤时刻，中等宽度
                '退勤時刻': 10,         # 退勤时刻，中等宽度
                '所定時間': 8,          # 所定时间，中等宽度
                '早出残業': 8,          # 早出残业，中等宽度
                '内深夜残業': 10,       # 内深夜残业，中等宽度
                '遅刻早退': 8,          # 迟到早退，中等宽度
                '休出時間': 8,          # 休出时间，中等宽度
                '出張残業': 8,          # 出张残业，中等宽度
                '外出時間': 8,          # 外出时间，中等宽度
                '戻り時間': 8,          # 戻り时间，中等宽度
                'コメント': 20,         # 注释，最宽
                'MC_出勤': 8,          # MC_出勤，中等宽度
                'MC_退勤': 8           # MC_退勤，中等宽度
            }
            
            # 应用列宽度设置
            for col, header in enumerate(self.table1_headers):
                if header in column_configs:
                    char_count = column_configs[header]
                    # 确保在最小和最大范围内
                    char_count = max(min_char_count, min(max_char_count, char_count))
                    width = char_width * char_count
                else:
                    # 默认宽度
                    width = char_width * 8
                    
                self.table1.setColumnWidth(col, width)
                
            self.log_employee_message(f"✅ Table1列宽度调整完成")
            
        except Exception as e:
            self.log_employee_message(f"❌ Table1列宽度调整失败: {e}")
            
    # #20250711+16：40+修改的主题 - 添加全局月份控制按键的处理函数
    def on_global_prev_clicked(self):
        """全局上个月按键点击事件"""
        try:
            # #20250711+17：00+修改的主题 - 更新全局月份偏移量
            self.global_month_offset -= 1
            self.log_employee_message(f"🔄 全局切换到上个月 (偏移量: {self.global_month_offset})")
            
            # #20250711+16：40+修改的主题 - 更新全局按键状态
            self._update_button_colors('global', 'prev')
            
            # 同时触发Table1、Table3、Chart的上个月按键
            self.on_table1_prev_month_clicked()
            self.on_show_prev_month_clicked()
            self.on_chart_prev_month_clicked()
            
            # 更新全局时间显示
            self._update_global_time_display()
            
        except Exception as e:
            self.log_employee_message(f"❌ 全局上个月切换失败: {e}")
            
    def on_global_current_clicked(self):
        """全局当月按键点击事件"""
        try:
            # #20250711+17：00+修改的主题 - 重置全局月份偏移量
            self.global_month_offset = 0
            self.log_employee_message(f"🔄 全局切换到当月 (偏移量: {self.global_month_offset})")
            
            # #20250711+16：40+修改的主题 - 更新全局按键状态
            self._update_button_colors('global', 'curr')
            
            # 同时触发Table1、Table3、Chart的当月按键
            self.on_table1_curr_month_clicked()
            self.on_show_curr_month_clicked()
            self.on_chart_curr_month_clicked()
            
            # 更新全局时间显示
            self._update_global_time_display()
            
        except Exception as e:
            self.log_employee_message(f"❌ 全局当月切换失败: {e}")
            
    def on_global_next_clicked(self):
        """全局下个月按键点击事件"""
        try:
            # #20250711+17：00+修改的主题 - 增加全局月份偏移量
            self.global_month_offset += 1
            self.log_employee_message(f"🔄 全局切换到下个月 (偏移量: {self.global_month_offset})")
            
            # #20250711+16：40+修改的主题 - 更新全局按键状态
            self._update_button_colors('global', 'next')
            
            # 同时触发Table1、Table3、Chart的下个月按键
            self.on_table1_next_month_clicked()
            self.on_show_next_month_clicked()
            self.on_chart_next_month_clicked()
            
            # 更新全局时间显示
            self._update_global_time_display()
            
        except Exception as e:
            self.log_employee_message(f"❌ 全局下个月切换失败: {e}")
            
    def _update_global_time_display(self):
        """#20250711+17：00+修改的主题 - 更新全局时间显示标签，根据偏移量计算月份和描述"""
        try:
            # 从当前月份开始计算目标月份
            base_month = datetime.now()
            
            # 根据偏移量计算目标月份
            if self.global_month_offset == 0:
                target_month = base_month
            else:
                # 计算目标月份 - 处理月份跨年的情况
                year = base_month.year
                month = base_month.month + self.global_month_offset
                
                # 处理月份超出范围的情况
                while month < 1:
                    month += 12
                    year -= 1
                while month > 12:
                    month -= 12
                    year += 1
                
                target_month = datetime(year, month, 1)
            
            # 根据偏移量生成描述文本
            if self.global_month_offset == 0:
                description = "今月"
            elif self.global_month_offset == 1:
                description = "来月"
            elif self.global_month_offset == -1:
                description = "先月"
            elif self.global_month_offset > 1:
                description = f"{self.global_month_offset}ヶ月後"
            elif self.global_month_offset < -1:
                description = f"{abs(self.global_month_offset)}ヶ月前"
            else:
                description = f"オフセット{self.global_month_offset}"
            # 生成显示文本
            #display_text = f"{target_month.strftime('%Y')}/{target_month.strftime('%m')}-{description}"

            # 构建 HTML 字符串
            display_text = (
                f"<span style='font-size: 20px; font-weight: bold; color: blue;'>{target_month.strftime('%Y')}/{target_month.strftime('%m')}</span><br>"
                f"-{description}"
            )

            self.global_time_label.setText(display_text)
            
        except Exception as e:
            self.log_employee_message(f"❌ 全局时间显示更新失败: {e}")
            
    def _create_chart_area(self):
        """创建图表显示区域"""
        # #20250711+16：40+修改的主题 - 创建主容器，包含全局控制面板和图表区域
        main_container = QtWidgets.QWidget()
        main_container_layout = QtWidgets.QVBoxLayout(main_container)
        
        # 创建全局月份控制面板
        global_control_panel = QtWidgets.QWidget()
        global_control_layout = QtWidgets.QHBoxLayout(global_control_panel)
        
        # 创建3个控制按钮
        self.global_prev_btn = QtWidgets.QPushButton("先月")
        self.global_prev_btn.setStyleSheet("background-color: #ddd; color: #333; font-size: 14px; font-weight: bold; padding: 8px 16px; border-radius: 4px; border: 1px solid #ccc;")
        self.global_prev_btn.clicked.connect(self.on_global_prev_clicked)
        
        self.global_current_btn = QtWidgets.QPushButton("今月")
        self.global_current_btn.setStyleSheet("background-color: #90EE90; color: #333; font-size: 14px; font-weight: bold; padding: 8px 16px; border-radius: 4px; border: 1px solid #4caf50;")
        self.global_current_btn.clicked.connect(self.on_global_current_clicked)
        
        self.global_next_btn = QtWidgets.QPushButton("次月")
        self.global_next_btn.setStyleSheet("background-color: #ddd; color: #333; font-size: 14px; font-weight: bold; padding: 8px 16px; border-radius: 4px; border: 1px solid #ccc;")
        self.global_next_btn.clicked.connect(self.on_global_next_clicked)
        
        # #20250711+16：40+修改的主题 - 创建时间显示标签，参考table3的QLabel样式
        #self.global_time_label = QtWidgets.QLabel("2025/07-今月")
        #self.global_time_label.setStyleSheet("QLabel { background-color: #f0f0f0; font-weight: bold; font-size: 16px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }")
        self.global_time_label = QtWidgets.QLabel()
        #self.t3_month_display.setReadOnly(True)
        self.global_time_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.global_time_label.setStyleSheet("QLabel { background-color: #f0f0f0; font-weight: bold; }")

        # 添加到布局
        global_control_layout.addWidget(self.global_prev_btn)
        global_control_layout.addWidget(self.global_current_btn)
        global_control_layout.addWidget(self.global_next_btn)
        global_control_layout.addWidget(self.global_time_label)
        global_control_layout.addStretch()
        
        main_container_layout.addWidget(global_control_panel)
        
        # 创建图表区域
        chart_group = QtWidgets.QGroupBox("勤務時間グラフ")
        
        # 修改 b3 布局进一步细分，分叉。: 主布局变为水平
        main_layout = QtWidgets.QHBoxLayout(chart_group)

        # 左侧控制面板
        left_controls_widget = QtWidgets.QWidget()
        left_controls_layout = QtWidgets.QVBoxLayout(left_controls_widget)
        
        # #20250711+13：10+修改的主题 - 保留月选择下拉框，删除读取chart和更新月列表按键
        #left_controls_layout.addWidget(QtWidgets.QLabel("月選択:"))
        self.month_combo = QtWidgets.QComboBox()
        #20250710+17：30 - 添加月份切换时自动加载Chart的信号连接
        self.month_combo.currentIndexChanged.connect(self.load_selected_chart)
        #left_controls_layout.addWidget(self.month_combo)
        
        # #20250711+12：45+修改的主题@program1.py - 添加Chart区域的3个月份切换按键
        left_controls_layout.addSpacing(10)
        
        self.chart_prev_month_btn = QtWidgets.QPushButton("先月データ表示")
        self.chart_prev_month_btn.setToolTip("Chart APIから先月のデータを読み込み")
        self.chart_prev_month_btn.clicked.connect(self.on_chart_prev_month_clicked)
        #left_controls_layout.addWidget(self.chart_prev_month_btn)

        self.chart_curr_month_btn = QtWidgets.QPushButton("今月データ表示")
        self.chart_curr_month_btn.setToolTip("Chart APIから今月のデータを読み込み")
        self.chart_curr_month_btn.clicked.connect(self.on_chart_curr_month_clicked)
        #left_controls_layout.addWidget(self.chart_curr_month_btn)
        
        self.chart_next_month_btn = QtWidgets.QPushButton("来月データ表示")
        self.chart_next_month_btn.setToolTip("Chart APIから来月のデータを読み込み")
        self.chart_next_month_btn.clicked.connect(self.on_chart_next_month_clicked)
        #left_controls_layout.addWidget(self.chart_next_month_btn)
        
        # #20250711+12：45+修改的主题@program1.py - 添加Chart月份显示文本框
        left_controls_layout.addSpacing(10)

        #self.chart_month_display = QtWidgets.QLineEdit()
        self.chart_month_display = QtWidgets.QLabel()
        #self.chart_month_display.setReadOnly(True)
        self.chart_month_display.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.chart_month_display.setStyleSheet("QLabel { background-color: #f8f0e8; font-weight: bold; }")
        #self.chart_month_display.setText(f"{datetime.now().strftime('%Y/%m')} の 工時比較")
        # 构建 HTML 字符串
        display_text = (
            f"<span style='font-size: 18px; font-weight: bold; color: blue;'>{datetime.now().strftime('%Y/%m')}</span><br>"
            f"の 工時比較"
        )
        #self.chart_month_display.setText(display_text)
        #left_controls_layout.addWidget(self.chart_month_display)
        
        # b4 增加上月图: 添加上月图表显示区域
        left_controls_layout.addSpacing(10)
        self.prev_month_label = QtWidgets.QLabel("上月勤務時間グラフ") #b5 修改崩溃错误
        self.prev_month_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter) #b5 修改崩溃错误
        self.prev_chart_widget = ChartWidget()
        # I've removed the setFixedWidth call as it was making the chart too small.
        # self.prev_chart_widget.setFixedWidth(load_chart_btn.sizeHint().width())
        
        left_controls_layout.addWidget(self.prev_month_label) #b5 修改崩溃错误
        left_controls_layout.addWidget(self.prev_chart_widget)
        left_controls_layout.addStretch()

        # 右侧图表区
        right_chart_widget = QtWidgets.QWidget()
        right_chart_layout = QtWidgets.QVBoxLayout(right_chart_widget)
        
        self.chart_month_label = QtWidgets.QLabel("読込中...")
        self.chart_month_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        self.chart_widget = ChartWidget()
        
        right_chart_layout.addWidget(self.chart_month_label)
        right_chart_layout.addWidget(self.chart_widget)

        # 修改 b3 布局进一步细分，分叉。: 添加左右到主布局,比例1:5
        main_layout.addWidget(left_controls_widget, 1)
        main_layout.addWidget(right_chart_widget, 4)
        
        # 将图表区域添加到主容器
        main_container_layout.addWidget(chart_group)
        
        # #20250711+12：45+修改的主题@program1.py - 由于删除了refresh_available_months按键，需要自动刷新
        QtCore.QTimer.singleShot(500, self.refresh_available_months)
        
        return main_container
    
    def refresh_available_months(self):
        """2025/07/08 + 使用Server5 API刷新可用的图表月份"""
        self.log_employee_message("利用可能月をリフレッシュしています（Server5 APIから）...")
        self.month_combo.setEnabled(False)
        self.chart_month_label.setText("勤務時間グラフ（月リスト読み込み中...）")

        # 使用QTimer调度任务，调用Server5 API
        QtCore.QTimer.singleShot(100, lambda: self._load_available_months_from_server5_api())

    def _load_available_months_from_server5_api(self):
        """2025/07/08 + 使用Server5 API加载可用月份"""
        try:
            self.log_employee_message(f"📡 调用Server5 API: /api/chart/months?employee_id={self.employee_id}")

            # 调用Server5 API
            result = self.server5_client.get_available_months(self.employee_id)

            if result.get("ok"):
                data = result.get("data", {})
                months = data.get("months", [])

                if months:
                    self.log_employee_message(f"✅ 从Server5 API获取了 {len(months)} 个可用月份")

                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_months_combo_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, months)
                    )
                else:
                    self.log_employee_message("⚠️ Server5 API返回空月份列表")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_months_combo_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ Server5 API调用失败: {error_msg}")

        except Exception as e:
            self.log_employee_message(f"❌ Server5 API调用异常: {e}")

    def _load_available_months_from_server5_api_for_init(self):
        """#20250710 卡顿修复 - 专用于初始化的可用月份加载，基于按钮点击方法"""
        try:
            self.log_employee_message(f"📡 初始化：调用Server5 API: /api/chart/months?employee_id={self.employee_id}")

            # 调用Server5 API
            result = self.server5_client.get_available_months(self.employee_id)

            if result.get("ok"):
                data = result.get("data", {})
                months = data.get("months", [])

                if months:
                    self.log_employee_message(f"✅ 初始化：从Server5 API获取了 {len(months)} 个可用月份")

                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_months_combo_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, months)
                    )
                else:
                    self.log_employee_message("⚠️ 初始化：Server5 API返回空月份列表")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_months_combo_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
                
                # 标记Months步骤完成
                self._on_step_completed('months')
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ 初始化：Server5 API调用失败: {error_msg}")
                self._on_step_failed('months', error_msg)

        except Exception as e:
            self.log_employee_message(f"❌ 初始化：Server5 API调用异常: {e}")
            self._on_step_failed('months', str(e))

    def _load_chart_data_from_server5_api_for_init(self):
        """#20250711+10：15+修改的主题 - 初始化图表数据加载，同时获取timeprotab的ｶﾚﾝﾀﾞ信息"""
        try:
            target_month = datetime.now()
            display_name = datetime.now().strftime("%Y年%m月")
            
            start_date = target_month.strftime('%Y-%m-01')
            if target_month.month == 12:
                end_date = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            self.log_employee_message(f"📡 初始化：调用Server5 API: /api/chart/generate?employee_id={self.employee_id}&start_date={start_date}&end_date={end_date_str}&chart_type=daily")
            
            # 获取图表数据
            chart_result = self.server5_client.get_chart_data(
                employee_id=self.employee_id,
                start_date=start_date,
                end_date=end_date_str,
                chart_type="daily"
            )
            
            # #20250711+10：15+修改的主题 - 同时获取timeprotab数据以获得ｶﾚﾝﾀﾞ信息
            timeprotab_result = self.server5_client.get_timeprotab_data(
                employee_id=self.employee_id,
                year=target_month.year,
                month=target_month.month
            )
            
            if chart_result.get("ok"):
                chart_data = chart_result.get("data", {})
                chart_days = chart_data.get("chart_days", [])
                
                # #20250711+10：15+修改的主题 - 合并timeprotab的ｶﾚﾝﾀﾞ信息
                if timeprotab_result.get("ok"):
                    timeprotab_data = timeprotab_result.get("data", [])
                    self.log_employee_message(f"✅ 初始化：从Server5 API获取了 {len(timeprotab_data)} 条timeprotab记录")
                    
                    # 创建timeprotab数据的字典，方便查找
                    timeprotab_dict = {}
                    for record in timeprotab_data:
                        date = record.get("日付")
                        if date:
                            if hasattr(date, 'day'):
                                day = date.day
                            else:
                                # 如果是字符串，尝试解析日期
                                try:
                                    from datetime import datetime as dt_class  # #20250711+10：15+修改的主题 - 避免变量名冲突
                                    date_obj = dt_class.strptime(str(date), '%Y-%m-%d')
                                    day = date_obj.day
                                except:
                                    continue
                            
                            timeprotab_dict[day] = {
                                "calendar_type": record.get("ｶﾚﾝﾀﾞ", ""),
                                "weekday": record.get("星期", ""),
                                "absence": record.get("不在", ""),
                                "work_type": record.get("勤務区分", "")
                            }
                    
                    # 将timeprotab信息合并到chart_days中
                    for day_info in chart_days:
                        day = day_info.get("day")
                        if day and day in timeprotab_dict:
                            day_info["calendar_type"] = timeprotab_dict[day]["calendar_type"]
                            day_info["timeprotab_weekday"] = timeprotab_dict[day]["weekday"]
                            day_info["absence"] = timeprotab_dict[day]["absence"]
                            day_info["work_type"] = timeprotab_dict[day]["work_type"]
                    
                    # 为没有chart_days数据的日期也添加timeprotab信息
                    chart_data["timeprotab_info"] = timeprotab_dict
                    
                    self.log_employee_message(f"✅ 初始化：合并了 {len(timeprotab_dict)} 个日期的timeprotab信息")
                else:
                    self.log_employee_message("⚠️ 初始化：无法获取timeprotab数据，使用默认颜色")
                
                #20250710+17：30 - 适配新的Chart API格式
                if chart_days:
                    # 转换为新的格式
                    converted_data = {
                        "chart_days": chart_days,
                        "month_name": chart_data.get("month_name", display_name),
                        "statistics": chart_data.get("statistics", {}),
                        "timeprotab_info": chart_data.get("timeprotab_info", {})  # #20250711+10：15+修改的主题
                    }
                    
                    self.chart_widget.set_chart_data(converted_data)
                    self.chart_month_label.setText(f"勤務時間グラフ ({display_name})")
                    
                    # 从统计信息获取数据点数量
                    stats = chart_data.get("statistics", {})
                    working_days = stats.get("working_days", 0)
                    matched_days = stats.get("matched_days", 0)
                    unmatched_days = stats.get("unmatched_days", 0)
                    
                    self.log_employee_message(f"✅ 初始化：图表数据加载成功 (工作日: {working_days}, 匹配: {matched_days}, 不匹配: {unmatched_days})")
                else:
                    self.log_employee_message("⚠️ 初始化：图表没有数据")
                    # #20250711+10：15+修改的主题 - 即使没有chart_days，也传递timeprotab信息
                    converted_data = {
                        "chart_days": [],
                        "month_name": display_name,
                        "statistics": {},
                        "timeprotab_info": chart_data.get("timeprotab_info", {})
                    }
                    self.chart_widget.set_chart_data(converted_data)
                    self.chart_month_label.setText("勤務時間グラフ (データなし)")
                
                # 标记Chart步骤完成
                self._on_step_completed('chart')
            else:
                error_msg = chart_result.get("error", "未知错误")
                self.log_employee_message(f"❌ 初始化：Server5 API调用失败: {error_msg}")
                self._on_step_failed('chart', error_msg)
                
        except Exception as e:
            self.log_employee_message(f"❌ 初始化：Server5 API调用异常: {e}")
            self._on_step_failed('chart', str(e))

    @QtCore.pyqtSlot(list)
    def _update_months_combo_ui(self, months):
        """在主线程中更新月份下拉框UI"""
        try:
            self.month_combo.clear()
            self.month_combo.setEnabled(True)

            if months:
                for month_info in months:
                    # Server5返回格式: {"year": 2025, "month": 5, "display_name": "2025年05月"}
                    year = int(month_info.get("year", 0))  # 确保是整数
                    month = int(month_info.get("month", 0))  # 确保是整数
                    display_name = month_info.get("display_name", "")  # 修正字段名
                    
                    # 如果没有display_name，则生成一个
                    if not display_name:
                        display_name = f"{year}年{month:02d}月"
                    
                    # 生成月份代码: 2025年5月 -> "202505"
                    month_code = f"{year:04d}{month:02d}"
                    month_data = {
                        "month_code": month_code,
                        "display_name": display_name
                    }
                    self.month_combo.addItem(display_name, month_data)

                self.log_employee_message(f"✅ 月份下拉框更新完成 ({len(months)}个月份)")
                self.chart_month_label.setText("勤務時間グラフ")
            else:
                self.log_employee_message("⚠️ 没有可用的月份数据")
                self.chart_month_label.setText("勤務時間グラフ (データなし)")

        except Exception as e:
            self.log_employee_message(f"❌ 月份下拉框更新失败: {e}")
            self.month_combo.setEnabled(True)
            self.chart_month_label.setText("勤務時間グラフ (エラー)")

    async def _load_available_months_from_db(self):
        """2025/07/03 + 15:40 + 从PostgreSQL数据库加载可用月份"""
        try:
            import asyncpg
            import sys
            from pathlib import Path
            
            # 使用server的数据库配置
            import sys
            from pathlib import Path
            config_path = Path(__file__).parent.parent / "server" / "app"
            sys.path.append(str(config_path))
            
            # 尝试导入本地配置，如果失败则使用远程配置
            try:
                from config_local import IMDB_DATABASE_URL
                print("使用本地数据库配置")
            except ImportError:
                from config import IMDB_DATABASE_URL
                print("使用远程数据库配置")
            
            # 2025/07/03 + 16:10 + 使用server的数据库配置（已经是正确格式）
            db_url = IMDB_DATABASE_URL
            
            # 连接数据库
            conn = await asyncpg.connect(db_url)
            
            try:
                # 从entries表和timeprotab表获取可用月份
                # 查询entries表的月份
                entries_query = """
                    SELECT DISTINCT 
                        TO_CHAR(entry_date, 'YYYY-MM') as month_str,
                        EXTRACT(YEAR FROM entry_date) as year,
                        EXTRACT(MONTH FROM entry_date) as month
                    FROM entries 
                    WHERE employee_id = $1 
                      AND entry_date IS NOT NULL
                    ORDER BY year DESC, month DESC
                """
                
                entries_months = await conn.fetch(entries_query, self.employee_id)
                
                # 查询timeprotab表的可用分区
                timeprotab_query = """
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_name LIKE 'timeprotab_%' 
                      AND table_schema = 'public'
                    ORDER BY table_name DESC
                """
                
                timeprotab_tables = await conn.fetch(timeprotab_query)
                
                # 解析timeprotab分区中的月份
                timeprotab_months = []
                for table in timeprotab_tables:
                    table_name = table['table_name']
                    # 提取月份代码，如 timeprotab_2507 -> 2507
                    if '_' in table_name:
                        month_code = table_name.split('_')[-1]
                        if len(month_code) == 4 and month_code.isdigit():
                            year = 2000 + int(month_code[:2])
                            month = int(month_code[2:])
                            month_str = f"{year:04d}-{month:02d}"
                            timeprotab_months.append({
                                'month_str': month_str,
                                'year': year,
                                'month': month
                            })
                
                # 合并两个数据源的月份
                all_months = set()
                
                # 添加entries表的月份
                for row in entries_months:
                    all_months.add((row['month_str'], row['year'], row['month']))
                
                # 添加timeprotab表的月份
                for month_info in timeprotab_months:
                    all_months.add((month_info['month_str'], month_info['year'], month_info['month']))
                
                # 转换为列表并排序
                months_list = sorted(list(all_months), key=lambda x: (x[1], x[2]), reverse=True)
                
                # 更新UI
                self.month_combo.setEnabled(True)
                self.month_combo.clear()
                
                if months_list:
                    for month_str, year, month in months_list:
                        # 确保year和month是整数类型
                        year = int(year)
                        month = int(month)
                        display_name = f"{year}年{month:02d}月"
                        month_code = f"{year:04d}{month:02d}"
                        month_data = {
                            "month_code": month_code,
                            "display_name": display_name
                        }
                        self.month_combo.addItem(display_name, month_data)
                    
                    self.log_employee_message(f"已加载 {len(months_list)} 个可用月份")
                    self.chart_month_label.setText("勤務時間グラフ (月份を選択してください)")
                    
                    # 自动选择当前月份
                    current_month_code = datetime.now().strftime("%Y%m")
                    for i in range(self.month_combo.count()):
                        item_data = self.month_combo.itemData(i)
                        if item_data and item_data.get("month_code") == current_month_code:
                            self.month_combo.setCurrentIndex(i)
                            self.log_employee_message(f"自动选择当前月份")
                            # 延迟加载图表
                            QtCore.QTimer.singleShot(500, self.load_selected_chart)
                            break
                else:
                    self.log_employee_message("没有可用的月份数据")
                    self.chart_month_label.setText("勤務時間グラフ (データなし)")
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.log_employee_message(f"❌ 月份データ取得エラー: {e}")
            self.month_combo.setEnabled(True)
            self.chart_month_label.setText("勤務時間グラフ (月份読込エラー)")

    def load_selected_chart(self):
        """2025/07/08 + 使用Server5 API加载图表数据 #20250709+15:40+修复主题"""
        current_data = self.month_combo.currentData()
        if not current_data:
            self.log_employee_message("⚠️ 没有选择月份，跳过图表加载")
            return
        month_code = current_data.get("month_code")
        display_name = current_data.get("display_name")
        self.log_employee_message(f"正在加载 {display_name} 的图表数据（Server5 APIから）...")
        self.chart_month_label.setText(f"勤務時間グラフ ({display_name}) - 読込中...")
        self.chart_widget.set_chart_data(None)
        year, month = int(month_code[:4]), int(month_code[4:])
        current_month_dt = datetime(year, month, 1)
        QtCore.QTimer.singleShot(100, lambda: self._load_chart_data_from_server5_api(current_month_dt, display_name, is_current=True))
        try:
            prev_month_dt = datetime(year, month, 1) - timedelta(days=1)
            prev_month_display = prev_month_dt.strftime("%Y年%m月")
            self.log_employee_message(f"正在加载 {prev_month_display} (上月) 的图表数据（Server5 APIから）...")
            self.prev_month_label.setText(f"先月 ({prev_month_display}) - 読込中...")
            self.prev_chart_widget.set_chart_data(None)
            QtCore.QTimer.singleShot(200, lambda: self._load_chart_data_from_server5_api(prev_month_dt, prev_month_display, is_current=False))
        except Exception as e:
            self.log_employee_message(f"计算上个月份失败: {e}")
            self.prev_month_label.setText("上月 - エラー")
            self.prev_chart_widget.set_chart_data(None)

    def _load_chart_data_from_server5_api(self, target_month: datetime, display_name: str, is_current: bool = True):
        """#20250711+10：15+修改的主题 - 使用Server5 API加载图表数据，同时获取timeprotab的ｶﾚﾝﾀﾞ信息"""
        try:
            start_date = target_month.strftime('%Y-%m-01')
            if target_month.month == 12:
                end_date = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
            end_date_str = end_date.strftime('%Y-%m-%d')
            self.log_employee_message(f"📡 调用Server5 API: /api/chart/generate for {display_name}")
            
            # 获取图表数据
            chart_result = self.server5_client.get_chart_data(
                employee_id=self.employee_id,
                start_date=start_date,
                end_date=end_date_str,
                chart_type="daily"
            )
            
            # #20250711+10：15+修改的主题 - 同时获取timeprotab数据以获得ｶﾚﾝﾀﾞ信息
            timeprotab_result = self.server5_client.get_timeprotab_data(
                employee_id=self.employee_id,
                year=target_month.year,
                month=target_month.month
            )
            
            # 处理响应
            if chart_result.get("ok"):
                chart_data = chart_result.get("data", {})
                chart_days = chart_data.get("chart_days", [])
                
                # #20250711+10：15+修改的主题 - 合并timeprotab的ｶﾚﾝﾀﾞ信息
                if timeprotab_result.get("ok"):
                    timeprotab_data = timeprotab_result.get("data", [])
                    self.log_employee_message(f"✅ 从Server5 API获取了 {len(timeprotab_data)} 条timeprotab记录")
                    
                    # 创建timeprotab数据的字典，方便查找
                    timeprotab_dict = {}
                    for record in timeprotab_data:
                        date = record.get("日付")
                        if date:
                            if hasattr(date, 'day'):
                                day = date.day
                            else:
                                # 如果是字符串，尝试解析日期
                                try:
                                    from datetime import datetime as dt_class  # #20250711+10：15+修改的主题 - 避免变量名冲突
                                    date_obj = dt_class.strptime(str(date), '%Y-%m-%d')
                                    day = date_obj.day
                                except:
                                    continue
                            
                            timeprotab_dict[day] = {
                                "calendar_type": record.get("ｶﾚﾝﾀﾞ", ""),
                                "weekday": record.get("星期", ""),
                                "absence": record.get("不在", ""),
                                "work_type": record.get("勤務区分", "")
                            }
                    
                    # 将timeprotab信息合并到chart_days中
                    for day_info in chart_days:
                        day = day_info.get("day")
                        if day and day in timeprotab_dict:
                            day_info["calendar_type"] = timeprotab_dict[day]["calendar_type"]
                            day_info["timeprotab_weekday"] = timeprotab_dict[day]["weekday"]
                            day_info["absence"] = timeprotab_dict[day]["absence"]
                            day_info["work_type"] = timeprotab_dict[day]["work_type"]
                    
                    # 为没有chart_days数据的日期也添加timeprotab信息
                    chart_data["timeprotab_info"] = timeprotab_dict
                    
                    self.log_employee_message(f"✅ 合并了 {len(timeprotab_dict)} 个日期的timeprotab信息")
                else:
                    self.log_employee_message("⚠️ 无法获取timeprotab数据，使用默认颜色")
                
                self.log_employee_message(f"✅ 从Server5 API获取了 {display_name} 的图表数据")
                
                # 在主线程中更新UI
                QtCore.QMetaObject.invokeMethod(
                    self, "_update_chart_ui",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(dict, chart_data),
                    QtCore.Q_ARG(str, display_name),
                    QtCore.Q_ARG(bool, is_current)
                )
            else:
                error_msg = chart_result.get("error", "未知错误")
                self.log_employee_message(f"❌ {display_name} 图表数据获取失败: {error_msg}")
                # 显示空图表
                QtCore.QMetaObject.invokeMethod(
                    self, "_update_chart_ui",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(dict, {}),
                    QtCore.Q_ARG(str, display_name),
                    QtCore.Q_ARG(bool, is_current)
                )
        except Exception as e:
            self.log_employee_message(f"❌ Server5 API调用异常 for {display_name}: {e}")

    @QtCore.pyqtSlot(dict, str, bool)
    def _update_chart_ui(self, chart_data, display_name, is_current):
        """在主线程中更新图表UI #20250709+15:40+修复主题"""
        try:
            #20250710+17：30 - 适配新的Chart API格式
            chart_days = chart_data.get("chart_days", [])
            
            if chart_days:
                # 转换为新的格式
                converted_data = {
                    "chart_days": chart_days,
                    "month_name": chart_data.get("month_name", display_name),
                    "statistics": chart_data.get("statistics", {}),
                    "timeprotab_info": chart_data.get("timeprotab_info", {})  # #20250711+10：15+修改的主题
                }
            else:
                # #20250711+10：15+修改的主题 - 即使没有chart_days，也传递timeprotab信息
                converted_data = {
                    "chart_days": [],
                    "month_name": display_name,
                    "statistics": {},
                    "timeprotab_info": chart_data.get("timeprotab_info", {})
                }
            
            if is_current:
                self.chart_widget.set_chart_data(converted_data)
                self.chart_month_label.setText(f"勤務時間グラフ ({display_name})")
                
                if converted_data:
                    stats = chart_data.get("statistics", {})
                    working_days = stats.get("working_days", 0)
                    matched_days = stats.get("matched_days", 0)
                    unmatched_days = stats.get("unmatched_days", 0)
                    self.log_employee_message(f"✅ 当前月份图表更新完成: {display_name} (工作日: {working_days}, 匹配: {matched_days}, 不匹配: {unmatched_days})")
                else:
                    self.log_employee_message(f"✅ 当前月份图表更新完成: {display_name} (无数据)")
            else:
                self.prev_chart_widget.set_chart_data(converted_data)
                self.prev_month_label.setText(f"先月`色`チェック用 ({display_name})")
                
                if converted_data:
                    stats = chart_data.get("statistics", {})
                    working_days = stats.get("working_days", 0)
                    matched_days = stats.get("matched_days", 0)
                    unmatched_days = stats.get("unmatched_days", 0)
                    self.log_employee_message(f"✅ 上个月份图表更新完成: {display_name} (工作日: {working_days}, 匹配: {matched_days}, 不匹配: {unmatched_days})")
                else:
                    self.log_employee_message(f"✅ 上个月份图表更新完成: {display_name} (无数据)")
        except Exception as e:
            self.log_employee_message(f"❌ 图表UI更新失败 for {display_name}: {e}")
            if is_current:
                self.chart_month_label.setText(f"勤務時間グラフ ({display_name}) - エラー")
            else:
                self.prev_month_label.setText(f"上月 ({display_name}) - エラー")

    @QtCore.pyqtSlot(str, dict, object)
    def on_server5_request_finished(self, request_id: str, result: dict, error: object):
        """#20250710 卡顿修复 - 处理Server5 API请求完成，支持新的异步加载流程"""
        try:
            # #20250710 卡顿修复 - 清除对应的超时定时器
            self._clear_request_timeout(request_id)
            
            if error:
                self.log_employee_message(f"❌ Server5请求失败 [{request_id}]: {error}")
                
                # #20250710 卡顿修复 - 处理初始化加载失败
                if request_id.startswith('table1_load_'):
                    self._on_step_failed('table1', str(error))
                elif request_id.startswith('table3_load_'):
                    self._on_step_failed('table3', str(error))
                elif request_id.startswith('department_load_'):
                    self._on_step_failed('department', str(error))
                elif request_id.startswith('months_load_'):
                    self._on_step_failed('months', str(error))
                elif request_id.startswith('chart_load_'):
                    self._on_step_failed('chart', str(error))
                return
            
            # 根据请求ID前缀分发处理
            if request_id.startswith('table1_load_'):
                self._handle_table1_load_response(result, request_id)
            elif request_id.startswith('table3_load_'):
                self._handle_table3_load_response(result, request_id)
            elif request_id.startswith('department_load_'):
                self._handle_department_load_response(result, request_id)
            elif request_id.startswith('months_load_'):
                self._handle_months_load_response(result, request_id)
            elif request_id.startswith('chart_load_'):
                self._handle_chart_load_response(result, request_id)
            elif request_id.startswith("entries_table3_"):
                self._handle_server5_entries_response(result, request_id)
            elif request_id.startswith("entries_create_"):
                self._handle_server5_create_response(result, request_id)
            elif request_id.startswith("entries_update_") or request_id.startswith("update_progress_"):
                self._handle_server5_update_response(result, request_id)
            elif request_id.startswith("entries_delete_"):
                self._handle_server5_delete_response(result, request_id)
            elif request_id.startswith("entries_chart_"):
                self._handle_server5_chart_response(result, request_id)
            elif request_id.startswith("upload_"):
                # 处理按钮4的上传请求
                self._handle_upload_progress_response(result)
            elif request_id.startswith("delete_progress_"):
                # 处理删除操作响应
                self._handle_delete_progress_response(result)
            else:
                self.log_employee_message(f"未知的Server5请求类型: {request_id}")
                
        except Exception as e:
            self.log_employee_message(f"Server5响应处理异常: {e}")

    @QtCore.pyqtSlot(str, dict, object)
    def on_async_request_finished(self, request_id: str, result: dict, error: object):
        """#20250710 卡顿修复 - 处理异步请求结果，支持新的异步加载流程"""
        # #20250710 卡顿修复 - 清除对应的超时定时器
        self._clear_request_timeout(request_id)
        
        if error:
            self.log_employee_message(f"❌ リクエストエラー [{request_id}]: {error}")
            
            # #20250710 卡顿修复 - 处理初始化加载失败
            if request_id.startswith('department_load_'):
                self._on_step_failed('department', str(error))
            elif request_id.startswith('chart_months_'):
                self.chart_month_label.setText("勤務時間グラフ（月リスト読み込みエラー）")
                self.month_combo.setEnabled(True)
            elif request_id.startswith('chart_generate_current_'):
                current_data = self.month_combo.currentData()
                month_name = current_data.get('display_name', '月') if current_data else '月'
                self.chart_month_label.setText(f"勤務時間グラフ（{month_name}）- データ生成エラー")
            elif request_id.startswith('chart_generate_prev_'): #b4 增加上月图
                self.prev_month_label.setText("先月 - データ生成エラー")

            return

        if request_id.startswith('add1_'):
            self._handle_add1_response(result)
        elif request_id.startswith('getxml_'):
            self._handle_getxml_response(result)
        elif request_id.startswith('upload_'):
            self._handle_upload_progress_response(result)
        elif request_id.startswith('progress_'):
            self._handle_getprogress_response(result)
        elif request_id.startswith('sensor_'):
            self._handle_sensor_response(result)
        elif request_id.startswith('mongo_status_'):
            self._handle_mongo_status_response(result)
        elif request_id.startswith('redis_status_'):
            self._handle_redis_status_response(result)
        elif request_id.startswith('chart_months_'):
            self._handle_chart_months_response(result)
        elif request_id.startswith('chart_generate_'):
            self._handle_chart_generate_response(result, request_id)
        #  修改 b21 增加table3的交互处理。
        elif request_id.startswith('5xml_'):
            self._handle_5xml_response(result)
        elif request_id.startswith('t4_action_'):
            self._handle_t4_action_response(result)
        elif request_id.startswith('update_progress_'):
            self._handle_update_progress_response(result)
        elif request_id.startswith('delete_progress_'):
            self._handle_delete_progress_response(result)
        # #250624/修改数据库change存入逻辑 - 删除log_change处理，因为不再需要单独的日志记录
        elif request_id.startswith('log_delete_'):
            self._handle_log_delete_response(result)
        # 修改，25061804  输入面板
        elif request_id.startswith('get_department_'):
            self._handle_get_department_response(result)
        # #20250710 卡顿修复 - 处理初始化加载的响应
        elif request_id.startswith('department_load_'):
            self._handle_department_load_response(result, request_id)

    def _handle_chart_months_response(self, result: dict):
        """处理图表月份列表响应 - 20250626.s5，6 - 适配Server5的entries API格式"""
        self.month_combo.setEnabled(True)
        if result.get("ok"):
            data = result.get("data", {})
            # 20250626.s5，6 - Server5返回的格式为 {"months": [...]}
            months = data.get("months", [])
            if months:
                self.month_combo.clear()
                for month_info in months:
                    # Server5返回格式: {"month": "2025-01", "display_name": "2025年01月"}
                    month_code = month_info.get("month", "").replace("-", "")  # "2025-01" -> "202501"
                    display_name = month_info.get("display_name", "")
                    month_data = {
                        "month_code": month_code,
                        "display_name": display_name
                    }
                    self.month_combo.addItem(display_name, month_data)
                self.log_employee_message(f"已加载 {len(months)} 个可用月份")
                self.chart_month_label.setText("勤務時間グラフ (月份を選択してください)")

                # 修改 b10 自动加载图表。
                if self._initial_load:
                    current_month_str = datetime.now().strftime("%Y年%m月")
                    for i in range(self.month_combo.count()):
                        if self.month_combo.itemText(i) == current_month_str:
                            self.month_combo.setCurrentIndex(i)
                            self.log_employee_message(f"自动选择月份: {current_month_str}")
                            self.load_selected_chart()
                            break
                    self._initial_load = False #确保只执行一次
            else:
                self.log_employee_message("没有可用的月份数据")
                self.chart_month_label.setText("勤務時間グラフ (データなし)")
        else:
            self.log_employee_message(f"获取月份列表失败: HTTP {result.get('status_code')}")
            self.chart_month_label.setText("勤務時間グラフ (月份読込エラー)")

    def _handle_chart_generate_response(self, result: dict, request_id: str):
        """b4 增加上月图: 处理图表数据生成响应 (支持当前和上月) - 20250626.s5，6 - 适配Server5的entries API格式"""
        
        # 根据请求ID判断是哪个图表
        if request_id.startswith('chart_generate_current_'):
            target_widget = self.chart_widget
            target_label = self.chart_month_label
            log_prefix = "主图表"
            # 从下拉框获取当前月份名称
            current_data = self.month_combo.currentData()
            default_month_name = current_data.get('display_name', '月份') if current_data else '月份'
        elif request_id.startswith('chart_generate_prev_'):
            target_widget = self.prev_chart_widget
            target_label = self.prev_month_label
            log_prefix = "上月图表"
            default_month_name = "上月"
        else:
            return # 不应发生

        if result.get("ok"):
            # 适配新的Chart API格式 (chart_days)
            chart_data = result.get("data", {})
            chart_days = chart_data.get("chart_days", [])
            
            if chart_days:  # 检查是否有Chart数据
                # 从chart_days转换为ChartWidget期望的格式（参考WorkTimeApp.py的格式）
                converted_data = {
                    "chart_days": chart_days,  # 保持新格式数据
                    "month_name": chart_data.get("month_name", default_month_name),
                    "statistics": chart_data.get("statistics", {})
                }
                
                if target_label:
                    # 对于主图表，保持完整标题；对于上月图表，只显示月份
                    month_display = chart_data.get("month_name", default_month_name)
                    if target_widget == self.chart_widget:
                        target_label.setText(f"勤務時間グラフ ({month_display})")
                    else:
                        target_label.setText(month_display)

                target_widget.set_chart_data(converted_data)
                
                # 从统计信息获取数据点数量
                stats = chart_data.get("statistics", {})
                working_days = stats.get("working_days", 0)
                matched_days = stats.get("matched_days", 0)
                unmatched_days = stats.get("unmatched_days", 0)
                
                self.log_employee_message(f"{log_prefix}数据加载成功 (工作日: {working_days}, 匹配: {matched_days}, 不匹配: {unmatched_days})")
            else:
                self.log_employee_message(f"{log_prefix}没有Chart数据")
                if target_label:
                    target_label.setText(f"{default_month_name} - データなし")
                target_widget.set_chart_data(None) # 清空图表
        else:
            http_error = result.get('status_code', 'N/A')
            self.log_employee_message(f"{log_prefix}生成图表数据失败: HTTP {http_error}")
            if target_label:
                target_label.setText(f"{default_month_name} - HTTPエラー")
            target_widget.set_chart_data(None) # 清空图表

    def _populate_table(self, table, records):
        table.clearContents()
        table.setRowCount(0)
        if not records:
            return

        # 对Table3，强制用table3_headers顺序
        if table == self.table3:
            #20250710+17：30 - 在"日付"之后添加"曜日"列
            table3_headers = ['DB_ID', '従業員ｺｰﾄﾞ', '日付', '曜日', '機種', '号機', '工場製番', '工事番号', 'ﾕﾆｯﾄ番号', '区分', '項目', '時間', '所属ｺｰﾄﾞ']
            table.setColumnCount(len(table3_headers))
            table.setHorizontalHeaderLabels(table3_headers)
            table.setRowCount(len(records))
            
            #20250710+17：30 - 日语星期映射
            japanese_weekdays = ['月', '火', '水', '木', '金', '土', '日']
            
            for r, rec in enumerate(records):
                # 确保rec是字典类型
                if isinstance(rec, dict):
                    for c, k in enumerate(table3_headers):
                        if k == '曜日':
                            #20250710+17：30 - 根据"日付"计算曜日
                            date_str = rec.get('日付', '')
                            if date_str:
                                try:
                                    date_obj = datetime.strptime(str(date_str), '%Y-%m-%d')
                                    weekday_num = date_obj.weekday()  # 0=Monday, 6=Sunday
                                    japanese_weekday = japanese_weekdays[weekday_num]
                                    table.setItem(r, c, QtWidgets.QTableWidgetItem(japanese_weekday))
                                except ValueError:
                                    # 如果日期格式不正确，显示空
                                    table.setItem(r, c, QtWidgets.QTableWidgetItem(""))
                            else:
                                table.setItem(r, c, QtWidgets.QTableWidgetItem(""))
                        elif k == 'DB_ID':
                            #20250710+18：00 - DB_ID应该对应entries的external_id，而不是id
                            value = rec.get('external_id', '')
                            table.setItem(r, c, QtWidgets.QTableWidgetItem(str(value)))
                        else:
                            value = rec.get(k, "")
                            table.setItem(r, c, QtWidgets.QTableWidgetItem(str(value)))
                else:
                    # 如果不是字典，显示错误信息
                    table.setItem(r, 0, QtWidgets.QTableWidgetItem(f"数据格式错误: {str(rec)}"))
            table.resizeColumnsToContents()
            return

        # #20250711+16：40+修改的主题 - 移除对table1的特殊处理，因为它现在使用Model
        # table1现在使用QTableView + Table1Model，不再在这里处理

        # 其他表格，保持原逻辑
        if records and isinstance(records[0], dict):
            headers = list(records[0].keys())
            table.setColumnCount(len(headers))
            table.setHorizontalHeaderLabels(headers)
            table.setRowCount(len(records))
            for r, rec in enumerate(records):
                for c, k in enumerate(headers):
                    table.setItem(r, c, QtWidgets.QTableWidgetItem(str(rec.get(k, ""))))
            table.resizeColumnsToContents()
        else:
            # 如果数据格式不正确，显示错误信息
            table.setColumnCount(1)
            table.setHorizontalHeaderLabels(['错误'])
            table.setRowCount(len(records))
            for r, rec in enumerate(records):
                table.setItem(r, 0, QtWidgets.QTableWidgetItem(f"数据格式错误: {str(rec)}"))

    def return_to_main(self):
        """返回主界面"""
        self.log_employee_message("メイン画面に戻ります")
        # 2025/06/26.11:50+分离ui操作 - 根据运行模式处理返回
        if self.is_standalone:
            # 独立运行模式：直接关闭程序
            self.close()
        else:
            # 集成运行模式：返回主窗口
            if self.main_window:
                self.main_window.show()
                self.main_window.raise_()
                self.main_window.activateWindow()
            self.close()
    
    def logout(self):
        """退出登录"""
        if QtWidgets.QMessageBox.question(self, "ログアウト", f"ログアウトしてもよろしいですか？\n現在のユーザー: {self.employee_name}") == QtWidgets.QMessageBox.StandardButton.Yes:
            self.log_employee_message(f"従業員 {self.employee_name} がログアウトしました")
            self.main_window.log_message(f"従業員 {self.employee_name} ({self.employee_id}) がログアウトしました")
            self.return_to_main()
    
    def closeEvent(self, event):
        """20250708 + 处理窗口关闭事件 - 修复连接清理问题"""
        try:
            print("开始清理Program1资源...")
            
            # 1. 关闭所有HTTP客户端
            if hasattr(self, 'server5_client') and self.server5_client:
                self.server5_client.close()
            
            if hasattr(self, 'async_http_client') and self.async_http_client:
                try:
                    # 异步客户端需要异步关闭
                    if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                        asyncio.run_coroutine_threadsafe(self.async_http_client.close(), self.loop)
                    else:
                        # 如果没有运行的事件循环，创建一个新的来关闭
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.async_http_client.close())
                        loop.close()
                except Exception as e:
                    print(f"关闭async_http_client时出错: {e}")
            
            if hasattr(self, 'sync_http_client') and self.sync_http_client:
                self.sync_http_client.close()
            
            if hasattr(self, 'server5_async_client') and self.server5_async_client:
                try:
                    if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                        asyncio.run_coroutine_threadsafe(self.server5_async_client.close(), self.loop)
                    else:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.server5_async_client.close())
                        loop.close()
                except Exception as e:
                    print(f"关闭server5_async_client时出错: {e}")

            # 2. 停止传感器定时器
            if hasattr(self, 'sensor_timer'):
                self.sensor_timer.stop()

            # 3. 停止WebSocket连接
            if hasattr(self, 'websocket_task') and self.websocket_task:
                self.websocket_task.cancel()

            # 4. 停止异步任务
            if hasattr(self, 'async_task') and self.async_task:
                self.async_task.cancel()

            # 5. 关闭通知WebSocket连接
            if hasattr(self, 'notification_connected') and self.notification_connected:
                self.log_employee_message("正在断开通知WebSocket连接...")
                self.notification_connected = False
                
                if hasattr(self, 'notification_websocket') and self.notification_websocket:
                    try:
                        # 在事件循环中关闭WebSocket
                        if hasattr(self, 'app_loop') and self.app_loop and not self.app_loop.is_closed():
                            asyncio.run_coroutine_threadsafe(
                                self.notification_websocket.close(), 
                                self.app_loop
                            )
                        elif hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
                            asyncio.run_coroutine_threadsafe(
                                self.notification_websocket.close(), 
                                self.loop
                            )
                        else:
                            # 如果没有可用的事件循环，创建一个新的来关闭
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(self.notification_websocket.close())
                            loop.close()
                    except Exception as e:
                        print(f"关闭通知WebSocket时出错: {e}")
                    finally:
                        self.notification_websocket = None
                
                # 等待通知连接线程结束
                if hasattr(self, 'notification_connect_thread') and self.notification_connect_thread and self.notification_connect_thread.is_alive():
                    try:
                        self.notification_connect_thread.join(timeout=2.0)  # 等待最多2秒
                        if self.notification_connect_thread.is_alive():
                            print("通知连接线程未能在超时时间内结束")
                    except Exception as e:
                        print(f"等待通知连接线程结束时出错: {e}")

            # 6. 关闭事件循环
            if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)

            self.log_employee_message("Program1窗口已关闭")
            print("Program1资源清理完成")

        except Exception as e:
            print(f"关闭窗口时发生错误: {e}")

        self.return_to_main()
        event.accept()

    def fetch_sensor_data(self):
        """获取传感器数据"""
        req_id = f"sensor_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        self.async_http_client.get_async("/api/extra/sensor_data", req_id)

    def check_mongo_status(self):
        """检查MongoDB状态"""
        self.log_employee_message("MongoDB接続状態を確認しています...")
        req_id = f"mongo_status_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        self.async_http_client.get_async("/api/extra/mongo_status", req_id)

    def check_redis_status(self):
        """检查Redis状态"""
        self.log_employee_message("Redis接続状態を確認しています...")
        req_id = f"redis_status_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        self.async_http_client.get_async("/api/extra/redis_status", req_id)

    def log_employee_message(self, message):
        """记录员工操作日志"""
        timestamp = QtCore.QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.employee_log.append(f"[{timestamp}] {message}")

    # #20250711+12：45+修改的主题@program1.py - 删除on_plus1_clicked方法，因为相关UI元素已被删除

    def fetch_xml_for_table1(self, index: int):
        """20250708 + 使用Server5 API获取timeprotab数据"""
        if index == 1:
            # 上个月数据
            target_month = datetime.now().replace(day=1) - timedelta(days=1)
            month_name = "先月"
        elif index == 2:
            # 当月数据
            target_month = datetime.now()
            month_name = "今月"
        else:
            # 其他月份（基于index计算）
            target_month = datetime.now()
            month_name = f"第{index}月"

        self.log_employee_message(f"Table1に{month_name}のデータを取得しています（Server5 APIから）...")

        # 直接调用Server5 API获取timeprotab数据
        QtCore.QTimer.singleShot(100, lambda: self._load_timeprotab_from_server5_api(target_month))

    def _load_timeprotab_from_server5_api(self, target_month: datetime):
        """20250708 + 使用Server5 API加载timeprotab数据到Table1"""
        try:
            year = target_month.year
            month = target_month.month

            self.log_employee_message(f"📡 调用Server5 API: /api/timeprotab?employee_id={self.employee_id}&year={year}&month={month}")

            # 调用Server5的timeprotab API获取数据
            result = self.server5_client.get_timeprotab_data(
                employee_id=self.employee_id,
                year=year,
                month=month
            )

            if result.get("ok"):
                timeprotab_data = result.get("data", [])
                if timeprotab_data:
                    self.log_employee_message(f"✅ 从Server5 API获取了 {len(timeprotab_data)} 条timeprotab记录")

                    # 转换为Table1显示格式
                    table1_records = []
                    for record in timeprotab_data:
                        # 保持原有的字段名称
                        table1_records.append(record)

                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table1_records)
                    )
                else:
                    # 如果没有数据，显示空表格
                    self.log_employee_message("⚠️ 没有找到timeprotab数据，显示空表格")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ Server5 API调用失败: {error_msg}")
                # 回退到直接数据库连接
                self._schedule_async_task(self._load_timeprotab_data_for_table1(target_month))

        except Exception as e:
            self.log_employee_message(f"❌ Server5 API调用异常: {e}")
            # 回退到直接数据库连接
            self._schedule_async_task(self._load_timeprotab_data_for_table1(target_month))

    def _load_timeprotab_from_server5_api_for_init(self, target_month: datetime):
        """#20250710 卡顿修复 - 专用于初始化的Table1数据加载，基于按钮点击方法"""
        try:
            year = target_month.year
            month = target_month.month

            self.log_employee_message(f"📡 初始化：调用Server5 API: /api/timeprotab?employee_id={self.employee_id}&year={year}&month={month}")

            # 调用Server5的timeprotab API获取数据
            result = self.server5_client.get_timeprotab_data(
                employee_id=self.employee_id,
                year=year,
                month=month
            )

            if result.get("ok"):
                timeprotab_data = result.get("data", [])
                if timeprotab_data:
                    self.log_employee_message(f"✅ 初始化：从Server5 API获取了 {len(timeprotab_data)} 条timeprotab记录")

                    # 转换为Table1显示格式
                    table1_records = []
                    for record in timeprotab_data:
                        # 保持原有的字段名称
                        table1_records.append(record)

                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table1_records)
                    )
                else:
                    # 如果没有数据，显示空表格
                    self.log_employee_message("⚠️ 初始化：没有找到timeprotab数据，显示空表格")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
                
                # 标记Table1步骤完成
                self._on_step_completed('table1')
            else:
                error_msg = result.get("error", "未知错误")
                self.log_employee_message(f"❌ 初始化：Server5 API调用失败: {error_msg}")
                self._on_step_failed('table1', error_msg)

        except Exception as e:
            self.log_employee_message(f"❌ 初始化：Server5 API调用异常: {e}")
            self._on_step_failed('table1', str(e))

    # 20250708 + 删除Server5-2相关的timeprotab采集方法，不再需要

    @QtCore.pyqtSlot(list)
    def _update_table1_ui(self, records):
        """在主线程中更新Table1 UI"""
        try:
            # #20250711+16：40+修改的主题 - 使用新的Model更新数据
            self.table1_model.update_data(records)
            # #20250712+10：30+修改的主题 - 在数据加载后强制重新排序
            self.table1.sortByColumn(0, QtCore.Qt.SortOrder.AscendingOrder) # <--- 请添加这一行

            # 数据更新后重新调整列宽度
            QtCore.QTimer.singleShot(100, self._adjust_table1_column_widths)
            self.log_employee_message(f"✅ Table1 UI更新完成 ({len(records)}条记录)")
        except Exception as e:
            self.log_employee_message(f"❌ Table1 UI更新失败: {e}")

    async def _load_timeprotab_data_for_table1(self, target_month: datetime):
        """2025/07/03 + 16:10 + 从PostgreSQL timeprotab表加载数据到Table1"""
        try:
            import asyncpg
            import sys
            from pathlib import Path
            
            # 添加config路径
            # 使用server的数据库配置
            import sys
            from pathlib import Path
            config_path = Path(__file__).parent.parent / "server" / "app"
            sys.path.append(str(config_path))
            
            # 尝试导入本地配置，如果失败则使用远程配置
            try:
                from config_local import IMDB_DATABASE_URL, TABLE_CONFIG
                print("使用本地数据库配置")
            except ImportError:
                from config import IMDB_DATABASE_URL, TABLE_CONFIG
                print("使用远程数据库配置")
            
            # 2025/07/03 + 16:10 + 使用server的数据库配置（已经是正确格式）
            db_url = IMDB_DATABASE_URL
            
            # 2025/07/03 + 16:10 + 计算月份代码和分区表名
            month_code = target_month.strftime("%y%m")
            partition_name = TABLE_CONFIG['timeprotab']['partition_format'].format(month_code=month_code)
            
            # 2025/07/03 + 16:10 + 连接数据库
            conn = await asyncpg.connect(db_url)
            
            try:
                # 2025/07/03 + 16:10 + 检查分区是否存在
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = $1 AND table_schema = 'public'
                    )
                """, partition_name)
                
                if not exists:
                    self.log_employee_message(f"⚠️ 2025/07/03 + 16:10 + {target_month.strftime('%Y/%m')} のデータがありません")
                    # 显示"没有数据"
                    empty_record = {"メッセージ": "2025/07/03 + 16:10 + データがありません"}
                    self._populate_table(self.table1, [empty_record])
                    return
                
                # 2025/07/03 + 16:10 + 查询timeprotab数据
                query = f"""
                    SELECT 
                        employee_id,
                        日付,
                        星期,
                        ｶﾚﾝﾀﾞ,
                        不在,
                        勤務区分,
                        事由,
                        出勤時刻,
                        ＭＣ_出勤,
                        退勤時刻,
                        ＭＣ_退勤,
                        所定時間,
                        早出残業,
                        内深夜残業,
                        遅刻早退,
                        休出時間,
                        出張残業,
                        外出時間,
                        戻り時間,
                        コメント
                    FROM {partition_name}
                    WHERE employee_id = $1
                    ORDER BY 日付
                """
                
                rows = await conn.fetch(query, self.employee_id)
                
                if rows:
                    # 2025/07/03 + 16:10 + 转换为字典格式
                    records = []
                    for row in rows:
                        record = {}
                        for i, column in enumerate(row.keys()):
                            value = row[i]
                            # 格式化日期和时间
                            if isinstance(value, (datetime.date, datetime.datetime)):
                                record[column] = value.strftime('%Y/%m/%d')
                            elif isinstance(value, datetime.time):
                                record[column] = value.strftime('%H:%M')
                            else:
                                record[column] = str(value) if value is not None else ""
                        records.append(record)
                    
                    self._populate_table(self.table1, records)
                    self.log_employee_message(f"✅ 2025/07/03 + 16:10 + Table1に{len(records)}件のデータを読み込みました")
                else:
                    self.log_employee_message(f"⚠️ 2025/07/03 + 16:10 + {target_month.strftime('%Y/%m')} のデータがありません")
                    empty_record = {"メッセージ": "2025/07/03 + 16:10 + データがありません"}
                    self._populate_table(self.table1, [empty_record])
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.log_employee_message(f"❌ 2025/07/03 + 16:10 + timeprotabデータ取得エラー: {e}")
            error_record = {"エラー": f"2025/07/03 + 16:10 + {str(e)}"}
            self._populate_table(self.table1, [error_record])

    async def _load_entries_data_for_table3(self, target_month: datetime):
        """2025/07/03 + 16:10 + 从PostgreSQL entries表加载数据到Table3"""
        try:
            import asyncpg
            import sys
            from pathlib import Path
            
            # 使用server的数据库配置
            import sys
            from pathlib import Path
            config_path = Path(__file__).parent.parent / "server" / "app"
            sys.path.append(str(config_path))
            from config import IMDB_DATABASE_URL, TABLE_CONFIG
            
            # 2025/07/03 + 16:10 + 使用server的数据库配置（已经是正确格式）
            db_url = IMDB_DATABASE_URL
            
            # 2025/07/03 + 16:10 + 计算该月的开始和结束日期
            start_date = target_month.replace(day=1).strftime('%Y-%m-%d')
            if target_month.month == 12:
                next_month = target_month.replace(year=target_month.year + 1, month=1, day=1)
            else:
                next_month = target_month.replace(month=target_month.month + 1, day=1)
            end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 2025/07/03 + 16:10 + 连接数据库
            conn = await asyncpg.connect(db_url)
            
            try:
                # 2025/07/03 + 16:10 + 查询entries数据
                query = """
                    SELECT 
                        id as db_id,
                        employee_id,
                        entry_date,
                        department,
                        model,
                        number,
                        factory_number,
                        project_number,
                        unit_number,
                        category,
                        item,
                        duration,
                        source,
                        ts
                    FROM entries
                    WHERE employee_id = $1 
                      AND entry_date >= $2 
                      AND entry_date <= $3
                    ORDER BY entry_date DESC, ts DESC
                """
                
                rows = await conn.fetch(query, self.employee_id, start_date, end_date)
                
                if rows:
                    # 2025/07/03 + 16:10 + 转换为Table3期望的格式
                    records = []
                    for row in rows:
                        record = {
                            'DB_ID': row['db_id'],
                            '従業員ｺｰﾄﾞ': row['employee_id'],
                            '日付': row['entry_date'].strftime('%Y/%m/%d') if row['entry_date'] else '',
                            '機種': row['model'] or '',
                            '号機': row['number'] or '',
                            '工場製番': row['factory_number'] or '',
                            '工事番号': row['project_number'] or '',
                            'ﾕﾆｯﾄ番号': row['unit_number'] or '',
                            '区分': str(row['category']) if row['category'] else '',
                            '項目': str(row['item']) if row['item'] else '',
                            '時間': str(row['duration']) if row['duration'] else '',
                            '所属ｺｰﾄﾞ': row['department'] or ''
                        }
                        records.append(record)
                    
                    self._populate_table(self.table3, records)
                    self.log_employee_message(f"✅ 2025/07/03 + 16:10 + Table3に{len(records)}件のデータを読み込みました")
                else:
                    self.log_employee_message(f"⚠️ 2025/07/03 + 16:10 + {target_month.strftime('%Y/%m')} のデータがありません")
                    self.table3.setRowCount(0)
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.log_employee_message(f"❌ 2025/07/03 + 16:10 + entriesデータ取得エラー: {e}")
            self.table3.setRowCount(0)

    def fetch_progress_list(self):
        """从服务器获取完整的进度列表 (4.xml) 并填充table3"""
        self.table3_data_source = '4.xml' # 记录数据源
        self.log_employee_message("Table3をリフレッシュしています（4.xmlから）...")
        req_id = f"progress_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        self.async_http_client.get_async("/api/extra/progress_list", req_id)

    # 20250709 table修改: 删除on_fetch_xml_clicked方法，不再需要XML相关功能

    def on_upload_progress_clicked(self):
        # 从输入面板收集数据
        data = {}
        
        # 收集所有字段的数据
        for field, entry in self.input_fields.items():
            data[field] = entry.text().strip()
        
        # 验证必填字段
        required_fields = ['employee_id', 'category', 'item', 'time', 'department']
        for field in required_fields:
            if not data.get(field):
                field_labels = {
                    'employee_id': '従業員ID',
                    'category': '区分',
                    'item': '項目', 
                    'time': '時間',
                    'department': '部門'
                }
                QtWidgets.QMessageBox.critical(self, "入力エラー", 
                    f"「{field_labels.get(field, field)}」は必須です")
                return
        
        # 验证时间字段
        try:
            time_value = float(data['time'])
            if time_value <= 0:
                raise ValueError("时间必须大于0")
            data['time'] = str(time_value)
        except ValueError:
            QtWidgets.QMessageBox.critical(self, "入力エラー", "時間は有効な正数を入力してください")
            return
        
        # 确认发送
        reply = QtWidgets.QMessageBox.question(self, "確認", "送信してもよろしいですか？",
                                             QtWidgets.QMessageBox.StandardButton.Yes | 
                                             QtWidgets.QMessageBox.StandardButton.No)
        if reply != QtWidgets.QMessageBox.StandardButton.Yes:
            return
        
        # 20250626.s5，6 - 进行客户端的修改，使用Server5的entries API
        # 将输入面板的数据转换为entries格式 - 修复字段映射以匹配Server6期望的格式
        entry_data = {
            "entry_date": data.get('date', '').replace('/', '-'),  # 转换日期格式 YYYY-MM-DD
            "employee_id": data.get('employee_id', ''),
            "duration": float(data.get('time', '0')),
            "model": data.get('model', ''),  # 机种 - 直接映射到model字段
            "number": data.get('number', ''),  # 号机 - 直接映射到number字段
            "factory_number": data.get('factory_number', ''),  # 工场製番 - 直接映射到factory_number字段
            "project_number": data.get('project_number', ''),  # 工事番号 - 直接映射到project_number字段
            "unit_number": data.get('unit_number', ''),  # ユニット番号 - 直接映射到unit_number字段
            "category": data.get('category', ''),  # 区分 - 直接映射到category字段，使用字符串
            "item": data.get('item', ''),  # 项目 - 直接映射到item字段，使用字符串
            "department": data.get('department', ''),
            "source": "user"  # 2025 07/04 +  16：30 + 相关主题: 明确标记为用户操作，以触发同步
        }
        
        req_id = f"upload_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        # 使用Server5的客户端entries网关API端点 - 修复：使用Server5专用客户端
        if self.is_server5_enabled:
            self.server5_async_client.post_async("/client/entries/create", req_id, json=entry_data)
            self.log_employee_message("進捗アップロードリクエストを送信しました (Server5客户端entries网关へ)")
        else:
            # 回退到原来的API
            #self.async_http_client.post_async("/client/entries/create", req_id, json=entry_data)
            self.log_employee_message("server5 fail")

    def _handle_add1_response(self, res):
        if res.get("ok"):
            self.output_n_edit.setText(str(res["data"]["result"]))
            self.log_employee_message("n+1 計算成功")
        else:
            self.log_employee_message(f"n+1 失敗: {res.get('status_code')}")

    def _handle_getxml_response(self, res):
        if res.get("ok"):
            self._populate_table(self.table1, res["data"]["records"])
            self.log_employee_message("Table1 が更新されました")
        else:
            self.log_employee_message(f"XML 取得失敗: {res.get('status_code')}")

    def _handle_upload_progress_response(self, res):
        # 20250714+09:30+ 修复错误处理：确保res是字典类型
        if not isinstance(res, dict):
            self.log_employee_message(f"アップロード失敗: 无效的响应格式 - {type(res)}")
            return
            
        # 20250626.s5，6 - 进行客户端的修改，适配Server5的客户端entries网关API响应
        if res.get("ok"):
            data = res.get("data", {})
            entry_id = data.get("entry_id", "")
            success = data.get("success", False)
            message = data.get("message", "")
            
            if success:
                self.log_employee_message(f"アップロード成功: {message}")
                # 刷新table3数据
                self._fetch_5xml_data_for_table3()
            else:
                self.log_employee_message(f"アップロード失败: {message}")
        else:
            error_detail = res.get('data', {}).get('detail', res.get('status_code'))
            self.log_employee_message(f"アップロード失敗: {error_detail}")

    def _handle_getprogress_response(self, res):
        if res.get("ok"):
            self._populate_table(self.table3, res["data"]["records"])
            self.log_employee_message("Table3 がリフレッシュされました")
        else:
            self.log_employee_message(f"Table3 リフレッシュ失敗: {res.get('status_code')}")

    def _handle_sensor_response(self, res):
        if res.get("ok"):
            data = res["data"]
            self.sensor_id_display.setText(data.get("sensor_id", "N/A"))
            self.sensor_value_display.setText(str(data.get("sensor_value", "N/A")))
            timestamp = data.get("timestamp", "")
            if timestamp:
                try:
                    dt = QtCore.QDateTime.fromString(timestamp, QtCore.Qt.DateFormat.ISODate)
                    self.sensor_timestamp_display.setText(dt.toString("yyyy-MM-dd hh:mm:ss"))
                except:
                     self.sensor_timestamp_display.setText(timestamp)
        else:
            self.log_employee_message(f"センサーデータ取得失敗: {res.get('status_code')}")

    def _handle_mongo_status_response(self, res):
        if res.get("ok"):
            data = res["data"]
            if data.get("status") == "connected":
                msg = f"MongoDB连接正常\nHost: {data.get('host')}:{data.get('port')}\nDB: {data.get('database')}\nCollection: {data.get('collection')}\nRecords: {data.get('sensor_records')}"
                QtWidgets.QMessageBox.information(self, "MongoDB状态", msg)
            else:
                QtWidgets.QMessageBox.warning(self, "MongoDB状态", f"MongoDB连接失败: {data.get('message')}")
        else:
            QtWidgets.QMessageBox.critical(self, "MongoDB状态", "检查失败")

    def _handle_redis_status_response(self, res):
        if res.get("ok"):
            data = res["data"]
            if data.get("status") == "connected":
                msg = f"Redis连接正常\nHost: {data.get('host')}:{data.get('port')}\nMemory: {data.get('memory_used')}"
                QtWidgets.QMessageBox.information(self, "Redis状态", msg)
            else:
                QtWidgets.QMessageBox.warning(self, "Redis状态", f"Redis连接失败: {data.get('message')}")
        else:
            QtWidgets.QMessageBox.critical(self, "Redis状态", "检查失败")

    def _handle_5xml_response(self, res):
        """ #20250711+10：15+修改的主题 - 处理entries API数据响应，统一字段映射 """
        # 20250626.s5，6 - 进行客户端的修改，适配Server5的entries API格式
        if res.get("ok"):
            entries_data = res.get("data", [])
            if entries_data:
                # #20250711+10：15+修改的主题 - 转换Server5的entries格式为table3期望的格式，与初始化方法保持一致
                converted_records = []
                for entry in entries_data:
                    # 将entries字段映射到table3的列，与初始化方法一致
                    record = {
                        'DB_ID': entry.get('external_id', ''),  #20250710+18：50+修复：DB_ID应该映射到external_id
                        '従業員ｺｰﾄﾞ': entry.get('employee_id', ''),
                        '日付': entry.get('entry_date', ''),
                        '機種': entry.get('model', ''),  #20250711+10：15+修改的主题 - 应该是model而不是project_code
                        '号機': entry.get('number', ''),  #20250711+10：15+修改的主题 - 添加number字段
                        '工場製番': entry.get('factory_number', ''),  #20250711+10：15+修改的主题 - 添加factory_number字段
                        '工事番号': entry.get('project_number', ''),  #20250711+10：15+修改的主题 - 添加project_number字段
                        'ﾕﾆｯﾄ番号': entry.get('unit_number', ''),  #20250711+10：15+修改的主题 - 添加unit_number字段
                        '区分': str(entry.get('category', '')),  #20250711+10：15+修改的主题 - 应该是category而不是status
                        '項目': str(entry.get('item', '')),  #20250711+10：15+修改的主题 - 应该是item而不是description
                        '時間': str(entry.get('duration', 0)),  #20250711+10：15+修改的主题 - 转换为字符串
                        '所属ｺｰﾄﾞ': entry.get('department', ''),  # 部门
                        # 内部使用字段
                        'external_id': entry.get('external_id', ''),  #20250711+10：15+修改的主题 - 添加内部字段
                        'db_id': entry.get('id', '')  #20250711+10：15+修改的主题 - 添加内部字段
                    }
                    converted_records.append(record)
                
                self._populate_table(self.table3, converted_records)
                self.log_employee_message(f"Table3 已从 entries API 更新 ({len(converted_records)}条记录)")  #20250711+10：15+修改的主题
            else:
                self.log_employee_message("没有找到entries数据")
                self.table3.clearContents()
                self.table3.setRowCount(0)
        else:
            self.log_employee_message(f"从 entries API 获取数据失败: {res.get('status_code')}")  #20250711+10：15+修改的主题
            self.table3.clearContents()
            self.table3.setRowCount(0)

    def _handle_t4_action_response(self, res):
        """ 修改 b21 增加table3的交互处理。 - 处理更改操作响应 """
        if res.get("ok"):
            msg = res['data'].get('message', '操作成功')
            self.log_employee_message(f"更改操作成功: {msg}")
            QtWidgets.QMessageBox.information(self, "成功", msg)
            self._fetch_5xml_data_for_table3() # Refresh data
        else:
            self.log_employee_message(f"更改操作失败: {res.get('status_code')}")
    
    def _handle_update_progress_response(self, res):
        """ 修改，25061801: 处理更新请求的响应 """
        # 20250626.s5，6 - 进行客户端的修改，适配Server5的entries PUT API响应
        if res.get("ok"):
            data = res.get("data", {})
            entry_id = data.get("id", "")
            self.log_employee_message(f"更新成功 (ID: {entry_id})")
            QtWidgets.QMessageBox.information(self, "成功", f"レコードが正常に更新されました (ID: {entry_id})")
            # 退出编辑模式并刷新表格
            self._exit_edit_mode()
            self._fetch_5xml_data_for_table3()
        else:
            error_detail = res.get('data', {}).get('detail', res.get('status_code'))
            error_msg = f"更新失败: {error_detail}"
            self.log_employee_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "错误", error_msg)

    def _handle_delete_progress_response(self, res):
        """ 修改，25061802  更改删除。 - 处理删除操作响应 """
        # 20250626.s5，6 - 进行客户端的修改，适配Server5的entries DELETE API响应
        # Server5删除API返回格式: {"message": "Entry {entry_id} 删除成功，已触发同步"}
        
        # 添加详细的响应日志
        self.log_employee_message(f"📋 删除响应详情: {res}")
        
        # 检查HTTP状态码
        status_code = res.get('status_code', 200)
        
        if status_code == 200:
            # HTTP状态码200表示成功
            message = res.get("message", "")
            if message and "删除成功" in message:
                self.log_employee_message("✅ 削除操作成功")
                QtWidgets.QMessageBox.information(self, "成功", "レコードが正常に削除されました")
                self._fetch_5xml_data_for_table3() # Refresh data
            else:
                # 状态码200但没有"删除成功"消息，可能是其他成功响应
                self.log_employee_message("✅ 削除操作完成")
                QtWidgets.QMessageBox.information(self, "成功", "レコードが正常に削除されました")
                self._fetch_5xml_data_for_table3() # Refresh data
        else:
            # HTTP状态码不是200，处理错误情况
            error_detail = res.get('detail', res.get('message', f'HTTP {status_code}'))
            error_msg = f"削除操作失败: {error_detail}"
            self.log_employee_message(f"❌ {error_msg}")
            QtWidgets.QMessageBox.critical(self, "错误", error_msg)
        
        # 重新启用删除按钮
        self.t3_delete_btn.setEnabled(True)

    # #250624/修改数据库change存入逻辑 - 删除log_change处理，因为不再需要单独的日志记录

    def _handle_log_delete_response(self, res):
        """ 修改，25061803  更改删除。- 处理删除日志记录的响应 """
        if res.get("ok"):
            self.log_employee_message("删除日志记录成功，现在执行删除操作。")
            # 从响应中获取db_id来触发删除
            db_id = res['data'].get('message').split(' ')[1] # "ID {db_id} logged..."
            self._trigger_delete_action(db_id)
        else:
            error_msg = f"记录删除日志失败: {res.get('data', {}).get('detail', res.get('status_code'))}"
            self.log_employee_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "错误", error_msg)

    def _trigger_delete_action(self, db_id: str):
        """
        修改，25061803  更改删除。
        处理 "删除" 的核心逻辑，在日志记录成功后被调用。
        """
        self.log_employee_message(f"请求删除 ID: {db_id} 的数据...")
        req_id = f"delete_progress_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        self.async_http_client.post_async("/api/extra/delete_progress", req_id, json={"db_id": db_id})

    # 修改，25061804  输入面板

    def fetch_employee_department(self):
        """获取员工的默认部门代码"""
        req_id = f"get_department_{QtCore.QDateTime.currentSecsSinceEpoch()}"
        # 注意：这里要调用 /api/department/employee/{employee_id}
        endpoint = f"/api/department/employee/{self.employee_id}"
        # 使用 server5_async_client，它的 base_url 已经是 http://localhost:8009
        self.server5_async_client.get_async(endpoint, req_id)

    # 修改，25061804  输入面板
    def _handle_get_department_response(self, res):
        """处理获取部门代码的响应"""
        if res.get("ok"):
            department = res["data"].get("department", "111")
            if 'department' in self.input_fields:
                self.input_fields['department'].setText(department)
            self.log_employee_message(f"已自动填充部门代码: {department}")
        else:
            self.log_employee_message(f"获取部门代码失败: {res.get('status_code')}, 使用默认值 111")
            if 'department' in self.input_fields:
                self.input_fields['department'].setText("111")
    
    def _handle_table1_load_response(self, result: dict, request_id: str):
        """#20250710 卡顿修复 - 处理Table1数据加载响应"""
        try:
            if result.get("ok"):
                timeprotab_data = result.get("data", [])
                if timeprotab_data:
                    self.log_employee_message(f"✅ Table1数据加载成功: {len(timeprotab_data)} 条记录")
                    
                    # 转换为Table1显示格式 - 与"显示当月"按钮保持一致
                    table1_records = []
                    for record in timeprotab_data:
                        # 确保每条记录都是字典格式
                        if isinstance(record, dict):
                            table1_records.append(record)
                        else:
                            # 如果不是字典，跳过这条记录
                            continue
                    
                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table1_records)
                    )
                else:
                    self.log_employee_message("⚠️ Table1没有数据")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table1_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
                
                # 标记步骤完成
                self._on_step_completed('table1')
            else:
                error_msg = result.get("error", "未知错误")
                self._on_step_failed('table1', error_msg)
        except Exception as e:
            self._on_step_failed('table1', str(e))
    
    def _handle_table3_load_response(self, result: dict, request_id: str):
        """#20250710 卡顿修复 - 处理Table3数据加载响应"""
        try:
            if result.get("ok"):
                entries_data = result.get("data", [])
                if entries_data:
                    self.log_employee_message(f"✅ Table3数据加载成功: {len(entries_data)} 条记录")
                    # 转换为Table3显示格式 - 与显示当月按钮保持一致的字段映射
                    table3_records = []
                    for entry in entries_data:
                        record = {
                            'DB_ID': entry.get('external_id', ''),  #20250710+18：50+修复：初始化时DB_ID应该映射到external_id
                            '従業員ｺｰﾄﾞ': entry.get('employee_id', ''),
                            '日付': entry.get('entry_date', ''),
                            '機種': entry.get('model', ''),  #20250710+18：50+修复：应该是model而不是project_code
                            '号機': entry.get('number', ''),  #20250710+18：50+修复：添加number字段
                            '工場製番': entry.get('factory_number', ''),  #20250710+18：50+修复：添加factory_number字段
                            '工事番号': entry.get('project_number', ''),  #20250710+18：50+修复：添加project_number字段
                            'ﾕﾆｯﾄ番号': entry.get('unit_number', ''),  #20250710+18：50+修复：添加unit_number字段
                            '区分': str(entry.get('category', '')),  #20250710+18：50+修复：应该是category而不是status
                            '項目': str(entry.get('item', '')),  #20250710+18：50+修复：应该是item而不是description
                            '時間': str(entry.get('duration', 0)),  #20250710+18：50+修复：转换为字符串
                            '所属ｺｰﾄﾞ': entry.get('department', '')
                        }
                        table3_records.append(record)
                    
                    # 在主线程中更新UI
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, table3_records)
                    )
                else:
                    self.log_employee_message("⚠️ Table3没有数据")
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_table3_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(list, [])
                    )
                
                # 标记步骤完成
                self._on_step_completed('table3')
            else:
                error_msg = result.get("error", "未知错误")
                self._on_step_failed('table3', error_msg)
        except Exception as e:
            self._on_step_failed('table3', str(e))
    
    def _handle_department_load_response(self, result: dict, request_id: str):
        """处理部门信息加载响应"""
        try:
            if result.get("ok"):
                dept = result["data"].get("department", "111")
                if 'department' in self.input_fields:
                    self.input_fields['department'].setText(dept)
                self.log_employee_message(f"✅ 部门信息加载成功: {dept}")
                self._on_step_completed('department')
            else:
                err = result.get("message", "未知错误")
                self._on_step_failed('department', err)
        except Exception as e:
            self._on_step_failed('department', str(e))

    
    def _handle_months_load_response(self, result: dict, request_id: str):
        """#20250710 卡顿修复 - 处理可用月份加载响应"""
        try:
            if result.get("ok"):
                # 修复：直接从data获取月份列表
                months = result.get("data", [])
                
                if months:
                    self.month_combo.clear()
                    for month_info in months:
                        month_code = month_info.get("month", "").replace("-", "")
                        display_name = month_info.get("display_name", "")
                        month_data = {
                            "month_code": month_code,
                            "display_name": display_name
                        }
                        self.month_combo.addItem(display_name, month_data)
                    
                    self.log_employee_message(f"✅ 可用月份加载成功: {len(months)} 个月份")
                    
                    # 自动选择当前月份
                    current_month_str = datetime.now().strftime("%Y年%m月")
                    for i in range(self.month_combo.count()):
                        if self.month_combo.itemText(i) == current_month_str:
                            self.month_combo.setCurrentIndex(i)
                            break
                else:
                    self.log_employee_message("⚠️ 没有可用的月份数据")
                
                # 标记步骤完成
                self._on_step_completed('months')
            else:
                error_msg = result.get("error", "未知错误")
                self._on_step_failed('months', error_msg)
        except Exception as e:
            self._on_step_failed('months', str(e))
    
    def _handle_chart_load_response(self, result: dict, request_id: str):
        """#20250710 卡顿修复 - 处理图表数据加载响应"""
        try:
            if result.get("ok"):
                chart_data = result.get("data", {})
                
                if chart_data.get("dates"):
                    # 转换格式
                    converted_data = {
                        "dates": chart_data.get("dates", []),
                        "duration": chart_data.get("duration", []),
                        "overtime_hours": chart_data.get("overtime_hours", []),
                        "total_hours": chart_data.get("total_hours", []),
                        "month_name": datetime.now().strftime("%Y年%m月")
                    }
                    
                    self.chart_widget.set_chart_data(converted_data)
                    self.chart_month_label.setText(f"勤務時間グラフ ({converted_data['month_name']})")
                    
                    self.log_employee_message(f"✅ 图表数据加载成功: {len(chart_data.get('dates', []))} 个数据点")
                else:
                    self.log_employee_message("⚠️ 图表没有数据")
                    self.chart_widget.set_chart_data(None)
                    self.chart_month_label.setText("勤務時間グラフ (データなし)")
                
                # 标记步骤完成
                self._on_step_completed('chart')
            else:
                error_msg = result.get("error", "未知错误")
                self._on_step_failed('chart', error_msg)
        except Exception as e:
            self._on_step_failed('chart', str(e))
    
    # 20250618.19:20 实时信息交流 - 聊天功能方法
    def connect_to_chat(self):
        """20250618.19:20 实时信息交流 - 连接到聊天服务器"""
        try:
            self.log_employee_message("开始连接聊天功能...")
            
            if self.chat_connected:
                self.log_employee_message("聊天已连接")
                return
            
            # 获取JWT token（从主窗口的登录中获取）
            self.jwt_token = self.get_employee_jwt_token()
            self.log_employee_message(f"获取到Token: {'已获取' if self.jwt_token else '获取失败'}")
            
            if not self.jwt_token:
                QtWidgets.QMessageBox.warning(self, "连接错误", "无法获取认证token，请重新登录")
                return
            
            self.log_employee_message("正在连接聊天服务器...")
            self.chat_status_label.setText("连接状态: 连接中...")
            self.chat_status_label.setStyleSheet("font-weight: bold; color: orange; padding: 5px; border: 1px solid #ccc;")
            
            # 在后台线程中建立WebSocket连接
            self.chat_connect_thread = threading.Thread(target=self._connect_chat_websocket, daemon=True)
            self.chat_connect_thread.start()
            self.log_employee_message("聊天连接线程已启动")
            
        except Exception as e:
            self.log_employee_message(f"连接聊天时出现异常: {e}")
            import traceback
            self.log_employee_message(f"详细错误: {traceback.format_exc()}")
    
    def test_chat_function(self):
        """2025/06/26.11:50+分离ui操作 - 测试聊天功能的简化版本"""
        try:
            self.log_employee_message("=== 开始测试聊天功能 ===")
            
            # 测试1: 检查基本变量
            self.log_employee_message(f"员工ID: {self.employee_id}")
            self.log_employee_message(f"员工姓名: {self.employee_name}")
            self.log_employee_message(f"独立运行模式: {self.is_standalone}")
            self.log_employee_message(f"聊天连接状态: {self.chat_connected}")
            
            # 测试2: 检查token获取
            token = self.get_employee_jwt_token()
            if token:
                self.log_employee_message(f"Token获取成功: {token[:50]}...")
            else:
                self.log_employee_message("Token获取失败")
            
            # 测试3: 检查聊天服务器连接
            self.log_employee_message("测试聊天服务器连接...")
            try:
                import requests
                response = requests.get("http://localhost:8005/health", timeout=3)
                if response.status_code == 200:
                    self.log_employee_message("✅ 聊天服务器连接正常")
                else:
                    self.log_employee_message(f"❌ 聊天服务器返回错误: {response.status_code}")
            except Exception as e:
                self.log_employee_message(f"❌ 聊天服务器连接失败: {e}")
            
            # 测试4: 检查事件循环
            if self.is_standalone:
                if self.app_loop:
                    self.log_employee_message(f"✅ 独立事件循环可用: {self.app_loop}")
                else:
                    self.log_employee_message("❌ 独立事件循环未设置")
            else:
                if self.main_window and hasattr(self.main_window, 'client_app'):
                    self.log_employee_message("✅ 主窗口事件循环可用")
                else:
                    self.log_employee_message("❌ 主窗口事件循环不可用")
            
            self.log_employee_message("=== 聊天功能测试完成 ===")
            
        except Exception as e:
            self.log_employee_message(f"测试聊天功能时出错: {e}")
            import traceback
            self.log_employee_message(f"详细错误: {traceback.format_exc()}")
    
    def disconnect_from_chat(self):
        """20250618.19:20 实时信息交流 - 断开聊天连接"""
        if not self.chat_connected:
            return
        
        self.log_employee_message("正在断开聊天连接...")
        self.chat_connected = False
        
        if self.chat_websocket:
            try:
                # 2025/06/26.11:50+分离ui操作 - 根据运行模式选择合适的事件循环
                if self.is_standalone and self.app_loop:
                    target_loop = self.app_loop
                elif not self.is_standalone and self.main_window:
                    target_loop = self.main_window.client_app.loop
                else:
                    # 直接关闭，不使用事件循环
                    asyncio.create_task(self.chat_websocket.close())
                    self.chat_websocket = None
                    self.update_chat_connection_status(False)
                    self.clear_user_list()
                    self.append_chat_message("系统", "已断开聊天连接", "system")
                    return
                
                asyncio.run_coroutine_threadsafe(
                    self.chat_websocket.close(), 
                    target_loop
                )
            except:
                pass
            self.chat_websocket = None
        
        # 更新UI状态
        self.update_chat_connection_status(False)
        self.clear_user_list()
        self.append_chat_message("系统", "已断开聊天连接", "system")
    
    def _connect_chat_websocket(self):
        """20250618.19:20 实时信息交流 - 后台线程中连接WebSocket"""
        try:
            # 2025/06/26.11:50+分离ui操作 - 根据运行模式选择合适的事件循环
            target_loop = None
            
            if self.is_standalone and self.app_loop:
                # 独立运行模式：使用独立的事件循环
                target_loop = self.app_loop
                self.log_employee_message("使用独立运行模式的事件循环")
            elif not self.is_standalone and self.main_window and hasattr(self.main_window, 'client_app') and self.main_window.client_app.loop:
                # 集成运行模式：使用主窗口的事件循环
                target_loop = self.main_window.client_app.loop
                self.log_employee_message("使用集成模式的事件循环")
            else:
                # 回退：在新线程中创建新的事件循环
                self.log_employee_message("使用新的事件循环（回退模式）")
                asyncio.run(self._websocket_chat_loop())
                return
            
            if target_loop and not target_loop.is_closed():
                future = asyncio.run_coroutine_threadsafe(
                    self._websocket_chat_loop(), 
                    target_loop
                )
                future.result()  # 等待连接完成或失败
            else:
                self.log_employee_message("事件循环不可用，使用回退模式")
                asyncio.run(self._websocket_chat_loop())
                
        except Exception as e:
            self.log_employee_message(f"聊天连接失败: {e}")
            import traceback
            self.log_employee_message(f"详细错误: {traceback.format_exc()}")
            QtCore.QMetaObject.invokeMethod(
                self, "update_chat_connection_status",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(bool, False)
            )
    
    async def _websocket_chat_loop(self):
        """20250618.19:20 实时信息交流 - WebSocket连接循环"""
        import ssl
        import websockets
        import urllib.parse
        
        # 2025/06/26.11:50+分离ui操作 - 动态获取最新token以确保有效性
        fresh_token = self.get_employee_jwt_token()
        if not fresh_token:
            self.log_employee_message("无法获取有效的JWT token")
            return
            
        # 记录token信息以调试
        if fresh_token.startswith('fallback_'):
            self.log_employee_message("⚠️ 使用了fallback token，可能无法通过认证")
        else:
            self.log_employee_message(f"使用JWT token: {fresh_token[:30]}...")
        
        # 20250618.20:15 微服务-信息交流 - 连接到新的聊天微服务器（端口8005）
        # 确保token正确编码到URL中
        encoded_token = urllib.parse.quote_plus(fresh_token)
        chat_ws_url = f"ws://localhost:8005/ws/chat?token={encoded_token}"
        
        self.log_employee_message(f"WebSocket连接URL: ws://localhost:8005/ws/chat?token=...")
        
        # 20250618.20:15 微服务-信息交流 - 暂时使用HTTP连接（无SSL）
        # 如果微服务器启用SSL，则改为wss://localhost:8005/ws/chat
        ssl_context = None  # 不使用SSL
        
        try:
            async with websockets.connect(chat_ws_url, ssl=ssl_context) as websocket:
                self.chat_websocket = websocket
                self.chat_connected = True
                
                # 在主线程中更新UI
                QtCore.QMetaObject.invokeMethod(
                    self, "update_chat_connection_status",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(bool, True)
                )
                
                # 监听消息
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        # 在主线程中处理消息
                        QtCore.QMetaObject.invokeMethod(
                            self, "handle_chat_message",
                            QtCore.Qt.ConnectionType.QueuedConnection,
                            QtCore.Q_ARG(dict, data)
                        )
                    except json.JSONDecodeError as e:
                        self.log_employee_message(f"消息解析错误: {e}")
                    except Exception as e:
                        self.log_employee_message(f"消息处理错误: {e}")
                        
        except Exception as e:
            self.log_employee_message(f"WebSocket连接错误: {e}")
            # 立即更新UI状态显示连接失败
            QtCore.QMetaObject.invokeMethod(
                self, "update_chat_connection_status_with_error",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, str(e))
            )
        finally:
            self.chat_connected = False
            self.chat_websocket = None
            QtCore.QMetaObject.invokeMethod(
                self, "update_chat_connection_status",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(bool, False)
            )
    
    @QtCore.pyqtSlot(bool)
    def update_chat_connection_status(self, connected: bool):
        """20250618.19:20 实时信息交流 - 更新聊天连接状态UI"""
        if connected:
            self.chat_status_label.setText("连接状态: 已连接")
            self.chat_status_label.setStyleSheet("font-weight: bold; color: green; padding: 5px; border: 1px solid #ccc;")
            self.connect_chat_btn.setEnabled(False)
            self.disconnect_chat_btn.setEnabled(True)
            self.message_input.setEnabled(True)
            self.send_btn.setEnabled(True)
            # 20250618.20:30 增加加密和文件共享以及数据库 - 启用新功能按钮
            self.private_chat_btn.setEnabled(True)
            self.file_upload_btn.setEnabled(True)
            self.append_chat_message("系统", "聊天连接成功！", "system")
        else:
            self.chat_status_label.setText("连接状态: 未连接")
            self.chat_status_label.setStyleSheet("font-weight: bold; color: red; padding: 5px; border: 1px solid #ccc;")
            self.connect_chat_btn.setEnabled(True)
            self.disconnect_chat_btn.setEnabled(False)
            self.message_input.setEnabled(False)
            self.send_btn.setEnabled(False)
            # 20250618.20:30 增加加密和文件共享以及数据库 - 禁用新功能按钮
            self.private_chat_btn.setEnabled(False)
            self.file_upload_btn.setEnabled(False)
            self.online_count_label.setText("在线用户: 0")
    
    @QtCore.pyqtSlot(str)
    def update_chat_connection_status_with_error(self, error_msg: str):
        """2025/06/26.11:50+分离ui操作 - 更新聊天连接状态UI（带错误信息）"""
        # 更新为断开状态
        self.update_chat_connection_status(False)
        
        # 显示具体错误信息
        if "HTTP 403" in error_msg:
            error_display = "连接被拒绝 (认证失败)"
            self.append_chat_message("系统", "聊天连接失败：认证错误，可能是token无效", "system")
        elif "timeout" in error_msg.lower():
            error_display = "连接超时"
            self.append_chat_message("系统", "聊天连接失败：连接超时", "system")
        else:
            error_display = f"连接失败: {error_msg[:50]}..."
            self.append_chat_message("系统", f"聊天连接失败：{error_msg}", "system")
        
        self.chat_status_label.setText(f"连接状态: {error_display}")
        self.chat_status_label.setStyleSheet("font-weight: bold; color: red; padding: 5px; border: 1px solid #f44336; background-color: #ffebee;")
    
    @QtCore.pyqtSlot(dict)
    def handle_chat_message(self, data: dict):
        """20250618.19:20 实时信息交流 - 处理收到的聊天消息"""
        message_type = data.get("type", "")
        
        if message_type == "user_list_update":
            self.update_user_list(data.get("online_users", []))
            self.online_count_label.setText(f"在线用户: {data.get('total_count', 0)}")
            
        elif message_type == "chat_message":
            user_name = data.get("user_name", "Unknown")
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            
            # 区分自己的消息和他人的消息
            if user_id == self.employee_id:
                self.append_chat_message(user_name, message, "self")
            else:
                self.append_chat_message(user_name, message, "other")
                
        elif message_type == "private_message":
            sender_name = data.get("sender_name", "Unknown")
            message = data.get("message", "")
            self.append_chat_message(f"[私聊] {sender_name}", message, "private")
            
        elif message_type == "system_message":
            message = data.get("message", "")
            self.append_chat_message("系统", message, "system")
            
        elif message_type == "chat_history":
            messages = data.get("messages", [])
            for msg in messages:
                user_name = msg.get("user_name", "Unknown")
                message_text = msg.get("message", "")
                user_id = msg.get("user_id", "")
                
                if user_id == self.employee_id:
                    self.append_chat_message(user_name, message_text, "self")
                else:
                    self.append_chat_message(user_name, message_text, "other")
                    
        elif message_type == "pong":
            # 心跳响应，无需处理
            pass
        else:
            self.log_employee_message(f"未知消息类型: {message_type}")

    # 2025/07/16 新增：MDB同步状态通知WebSocket连接
    def connect_to_notifications(self):
        """连接到通知WebSocket"""
        if self.notification_connected:
            self.log_employee_message("通知连接已存在")
            return
        
        self.log_employee_message("正在连接通知服务...")
        
        # 在后台线程中连接WebSocket
        self.notification_connect_thread = threading.Thread(
            target=self._connect_notification_websocket,
            daemon=True
        )
        self.notification_connect_thread.start()
    
    def disconnect_from_notifications(self):
        """断开通知WebSocket连接"""
        if not self.notification_connected:
            return
        
        self.log_employee_message("断开通知连接...")
        self.notification_connected = False
        
        if self.notification_websocket:
            try:
                # 在事件循环中关闭WebSocket
                if hasattr(self, 'app_loop') and self.app_loop:
                    asyncio.run_coroutine_threadsafe(
                        self.notification_websocket.close(), 
                        self.app_loop
                    )
            except Exception as e:
                self.log_employee_message(f"关闭通知WebSocket时出错: {e}")
            finally:
                self.notification_websocket = None
    
    def _connect_notification_websocket(self):
        """2025/07/16 新增：后台线程中连接通知WebSocket"""
        try:
            # 根据运行模式选择合适的事件循环
            target_loop = None
            
            if self.is_standalone and hasattr(self, 'app_loop') and self.app_loop:
                # 独立运行模式：使用独立的事件循环
                target_loop = self.app_loop
                self.log_employee_message("使用独立运行模式的事件循环连接通知")
            elif not self.is_standalone and self.main_window and hasattr(self.main_window, 'client_app') and self.main_window.client_app.loop:
                # 集成运行模式：使用主窗口的事件循环
                target_loop = self.main_window.client_app.loop
                self.log_employee_message("使用集成模式的事件循环连接通知")
            else:
                # 回退：在新线程中创建新的事件循环
                self.log_employee_message("使用新的事件循环连接通知（回退模式）")
                asyncio.run(self._websocket_notification_loop())
                return
            
            if target_loop and not target_loop.is_closed():
                future = asyncio.run_coroutine_threadsafe(
                    self._websocket_notification_loop(), 
                    target_loop
                )
                future.result()  # 等待连接完成或失败
            else:
                self.log_employee_message("事件循环不可用，使用回退模式连接通知")
                asyncio.run(self._websocket_notification_loop())
                
        except Exception as e:
            self.log_employee_message(f"通知连接失败: {e}")
            import traceback
            self.log_employee_message(f"详细错误: {traceback.format_exc()}")
            QtCore.QMetaObject.invokeMethod(
                self, "update_notification_connection_status",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(bool, False)
            )
    
    async def _websocket_notification_loop(self):
        """2025/07/16 新增：通知WebSocket连接循环"""
        import ssl
        import websockets
        import urllib.parse
        
        # 获取最新的JWT token
        fresh_token = self.get_employee_jwt_token()
        if not fresh_token:
            self.log_employee_message("无法获取有效的JWT token，通知连接失败")
            return
            
        # 记录token信息以调试
        if fresh_token.startswith('fallback_'):
            self.log_employee_message("⚠️ 使用了fallback token，可能无法通过通知认证")
        else:
            self.log_employee_message(f"使用JWT token连接通知: {fresh_token[:30]}...")
        
        # 连接到Server5的通知WebSocket端点
        encoded_token = urllib.parse.quote_plus(fresh_token)
        notification_ws_url = f"ws://localhost:8009/notification/ws/notify/{self.employee_id}?token={encoded_token}"
        
        self.log_employee_message(f"通知WebSocket连接URL: ws://localhost:8009/notification/ws/notify/{self.employee_id}?token=...")
        
        # 不使用SSL
        ssl_context = None
        
        try:
            async with websockets.connect(notification_ws_url, ssl=ssl_context) as websocket:
                self.notification_websocket = websocket
                self.notification_connected = True
                
                # 在主线程中更新UI
                QtCore.QMetaObject.invokeMethod(
                    self, "update_notification_connection_status",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(bool, True)
                )
                
                self.log_employee_message("✅ 通知WebSocket连接成功，开始监听MDB同步状态...")
                
                # 监听消息
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        # 在主线程中处理消息
                        QtCore.QMetaObject.invokeMethod(
                            self, "handle_notification_message",
                            QtCore.Qt.ConnectionType.QueuedConnection,
                            QtCore.Q_ARG(dict, data)
                        )
                    except json.JSONDecodeError as e:
                        self.log_employee_message(f"通知消息解析错误: {e}")
                    except Exception as e:
                        self.log_employee_message(f"通知消息处理错误: {e}")
                        
        except Exception as e:
            self.log_employee_message(f"通知WebSocket连接错误: {e}")
            # 立即更新UI状态显示连接失败
            QtCore.QMetaObject.invokeMethod(
                self, "update_notification_connection_status_with_error",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, str(e))
            )
        finally:
            self.notification_connected = False
            self.notification_websocket = None
            QtCore.QMetaObject.invokeMethod(
                self, "update_notification_connection_status",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(bool, False)
            )
    
    @QtCore.pyqtSlot(bool)
    def update_notification_connection_status(self, connected: bool):
        """更新通知连接状态UI"""
        try:
            if connected:
                # 更新状态指示器（如果有的话）
                self.log_employee_message("📢 通知服务已连接，将接收MDB同步状态更新")
                
                # 可以在这里添加UI状态更新，比如状态栏图标等
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage("通知服务已连接", 3000)
            else:
                self.log_employee_message("📢 通知服务已断开")
                
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage("通知服务已断开", 3000)
                    
        except Exception as e:
            self.log_employee_message(f"更新通知连接状态失败: {e}")
    
    @QtCore.pyqtSlot(str)
    def update_notification_connection_status_with_error(self, error_msg: str):
        """更新通知连接状态UI（带错误信息）"""
        try:
            self.log_employee_message(f"📢 通知连接失败: {error_msg}")
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"通知连接失败: {error_msg}", 5000)
                
        except Exception as e:
            self.log_employee_message(f"更新通知连接错误状态失败: {e}")
    
    @QtCore.pyqtSlot(dict)
    def handle_notification_message(self, data: dict):
        """处理通知消息"""
        try:
            message_type = data.get('type', '')
            
            if message_type == 'connection_confirm':
                # 连接确认消息
                message = data.get('message', '')
                self.log_employee_message(f"📢 {message}")
                
            elif message_type == 'operation_synced':
                # MDB同步成功通知
                operation = data.get('operation', '')
                entry_id = data.get('entry_id', '')
                external_id = data.get('external_id', '')
                message = data.get('message', '')
                
                self.log_employee_message(f"✅ {message}")
                
                # 触发UI刷新
                self._handle_mdb_sync_success(operation, entry_id, external_id)
                
            elif message_type == 'operation_failure':
                # MDB同步失败通知
                operation = data.get('operation', '')
                entry_id = data.get('entry_id', '')
                error_message = data.get('error_message', '')
                message = data.get('message', '')
                
                self.log_employee_message(f"❌ {message}")
                self.log_employee_message(f"详细错误: {error_message}")
                
                # 可以在这里添加失败处理逻辑
                
            elif message_type == 'pong':
                # 心跳响应
                pass  # 静默处理心跳
                
            elif message_type == 'status':
                # 状态响应
                connected = data.get('connected', False)
                self.log_employee_message(f"📢 通知服务状态: {'已连接' if connected else '未连接'}")
                
            else:
                self.log_employee_message(f"收到未知通知类型: {message_type}")
                
        except Exception as e:
            self.log_employee_message(f"处理通知消息失败: {e}")
    
    def _handle_mdb_sync_success(self, operation: str, entry_id: str, external_id: str):
        """2025/07/16 + 13：24 + 修改小弹窗 - 处理MDB同步成功事件"""
        try:
            self.log_employee_message(f"🔄 检测到MDB同步成功: {operation} - entry_id={entry_id}, external_id={external_id}")
            
            # 根据操作类型执行相应的UI更新
            if operation == 'INSERT':
                # 插入操作成功，刷新Table3数据
                self.log_employee_message("🔄 刷新Table3数据以显示新插入的记录...")
                self._fetch_5xml_data_for_table3()
                
            elif operation == 'UPDATE':
                # 更新操作成功，刷新Table3数据
                self.log_employee_message("🔄 刷新Table3数据以显示更新后的记录...")
                self._fetch_5xml_data_for_table3()
                
            elif operation == 'DELETE':
                # 删除操作成功，刷新Table3数据
                self.log_employee_message("🔄 刷新Table3数据以移除已删除的记录...")
                self._fetch_5xml_data_for_table3()
            
            # 2025/07/16 + 13：24 + 修改小弹窗 - 使用右下角非模态通知弹窗替代模态对话框
            operation_text = {
                'INSERT': '插入',
                'UPDATE': '更新', 
                'DELETE': '删除'
            }.get(operation, operation)
            
            title = f"✅ {operation_text}同步成功"
            message = f"数据已成功同步到MDB系统\n操作: {operation_text}\n记录ID: {entry_id}\n外部ID: {external_id}"
            
            # 创建并显示通知弹窗
            notification = NotificationToast(title, message, parent=self, duration=4000)
            notification.show_notification()
            
        except Exception as e:
            self.log_employee_message(f"处理MDB同步成功事件失败: {e}")
            import traceback
            self.log_employee_message(f"详细错误: {traceback.format_exc()}")

    def update_user_list(self, online_users: list):
        """20250618.19:20 实时信息交流 - 更新在线用户列表"""
        self.user_list_widget.clear()
        
        for user in online_users:
            employee_id = user.get("employee_id", "")
            employee_name = user.get("employee_name", "")
            status = user.get("status", "online")
            
            # 创建列表项
            item_text = f"{employee_name} ({employee_id})"
            if employee_id == self.employee_id:
                item_text += " (我)"
            
            item = QtWidgets.QListWidgetItem(item_text)
            
            # 设置状态颜色
            if status == "online":
                item.setForeground(QtGui.QColor("green"))
            else:
                item.setForeground(QtGui.QColor("gray"))
            
            # 存储用户信息到item数据中
            item.setData(QtCore.Qt.ItemDataRole.UserRole, user)
            
            self.user_list_widget.addItem(item)
    
    def clear_user_list(self):
        """20250618.19:20 实时信息交流 - 清空用户列表"""
        self.user_list_widget.clear()
    
    def append_chat_message(self, sender: str, message: str, message_type: str = "other"):
        """20250618.19:20 实时信息交流 - 添加聊天消息到显示区"""
        timestamp = QtCore.QDateTime.currentDateTime().toString("hh:mm:ss")
        
        # 根据消息类型设置不同的样式
        if message_type == "system":
            html = f'<div style="color: #666; font-style: italic; margin: 5px 0;">[{timestamp}] <b>系统:</b> {message}</div>'
        elif message_type == "self":
            html = f'<div style="color: #2196F3; margin: 5px 0; text-align: right;"><b>我 [{timestamp}]:</b> {message}</div>'
        elif message_type == "private":
            html = f'<div style="color: #FF9800; margin: 5px 0; background-color: #fff3e0; padding: 5px; border-left: 3px solid #FF9800;">[{timestamp}] <b>{sender}:</b> {message}</div>'
        else:  # other
            html = f'<div style="color: #333; margin: 5px 0;"><b>{sender} [{timestamp}]:</b> {message}</div>'
        
        # 插入HTML消息
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QtGui.QTextCursor.MoveOperation.End)
        cursor.insertHtml(html)
        cursor.insertText("\n")
        
        # 自动滚动到底部
        scrollbar = self.chat_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def send_message(self):
        """20250618.19:20 实时信息交流 - 发送聊天消息"""
        if not self.chat_connected or not self.chat_websocket:
            QtWidgets.QMessageBox.warning(self, "发送失败", "聊天未连接")
            return
        
        message_text = self.message_input.text().strip()
        if not message_text:
            return
        
        # 构造消息数据
        message_data = {
            "type": "chat_message",
            "message": message_text
        }
        
        try:
            # 在事件循环中发送消息
            # 2025/06/26.11:50+分离ui操作 - 根据运行模式选择合适的事件循环
            if self.is_standalone and self.app_loop:
                target_loop = self.app_loop
            elif not self.is_standalone and self.main_window:
                target_loop = self.main_window.client_app.loop
            else:
                # 使用当前事件循环
                target_loop = asyncio.get_event_loop()
            
            asyncio.run_coroutine_threadsafe(
                self.chat_websocket.send(json.dumps(message_data)),
                target_loop
            )
            
            # 清空输入框
            self.message_input.clear()
            
        except Exception as e:
            self.log_employee_message(f"发送消息失败: {e}")
            QtWidgets.QMessageBox.warning(self, "发送失败", f"消息发送失败: {e}")
    
    def get_employee_jwt_token(self):
        """20250618.19:20 实时信息交流 - 获取员工JWT token"""
        # 2025/06/26.11:50+分离ui操作 - 根据运行模式获取token
        if self.is_standalone and self.token:
            # 独立运行模式：优先使用从Launcher传入的有效token
            self.log_employee_message("使用Launcher传入的有效token")
            return self.token
        elif not self.is_standalone and self.main_window:
            # 集成运行模式：从主应用获取
            try:
                main_token = self.main_window.client_app.get_jwt_token()
                self.log_employee_message("使用主窗口的token")
                return main_token
            except Exception as e:
                self.log_employee_message(f"主窗口token获取失败: {e}, 尝试获取新token")
                return self._get_fresh_jwt_token()
        else:
            # 回退到从认证服务获取新token
            self.log_employee_message("回退模式：从认证服务获取新token")
            try:
                return self._get_fresh_jwt_token()
            except Exception as e:
                self.log_employee_message(f"获取token失败: {e}")
                return None
    
    def _get_fresh_jwt_token(self):
        """2025/06/26.11:50+分离ui操作 - 从认证服务获取新的JWT token"""
        # 独立运行模式下，如果传入的token无效，应该提示用户重新登录
        if self.is_standalone:
            self.log_employee_message("⚠️ 独立运行模式下不应该重新获取token，请通过Launcher重新登录")
            return None
            
        try:
            import requests
            
            # 使用员工凭据登录获取新token（仅在集成模式下）
            login_data = {
                "employee_id": self.employee_id,
                "password": "test123"  # 在实际应用中应该安全存储
            }
            
            response = requests.post(
                "http://localhost:8006/login",
                json=login_data,
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    token = result.get("access_token")
                    if token:
                        self.log_employee_message("从认证服务获取新token成功")
                        return token
                    else:
                        self.log_employee_message("认证服务响应中缺少access_token")
                else:
                    self.log_employee_message(f"认证失败: {result.get('message', '未知错误')}")
            else:
                self.log_employee_message(f"认证请求失败: HTTP {response.status_code}")
                
            return None
            
        except Exception as e:
            self.log_employee_message(f"获取新token失败: {e}")
            # 仅在集成模式下使用本地生成的回退token
            if not self.is_standalone:
                return self._generate_local_jwt_token()
            else:
                return None
    
    def _generate_local_jwt_token(self):
        """20250618.19:20 实时信息交流 - 本地生成JWT token"""
        try:
            from datetime import datetime, timedelta
            
            # 创建标准JWT payload
            payload = {
                "sub": f"employee_{self.employee_id}",
                "employee_id": self.employee_id,
                "employee_name": self.employee_name,
                "user_type": "employee",
                "exp": datetime.utcnow() + timedelta(hours=24)
            }
            
            # 与服务器一致的JWT配置
            SECRET_KEY = "your-very-secret-signing-key"
            ALGORITHM = "HS256"
            
            # 优先使用jose库
            try:
                from jose import jwt
                token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
                self.log_employee_message("使用jose库成功生成JWT token")
                return token
            except ImportError:
                self.log_employee_message("jose库未安装，尝试PyJWT")
                
            # 次选PyJWT库
            try:
                import jwt as pyjwt
                token = pyjwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
                # PyJWT在某些版本中返回bytes，需要解码
                if isinstance(token, bytes):
                    token = token.decode('utf-8')
                self.log_employee_message("使用PyJWT成功生成JWT token")
                return token
            except ImportError:
                self.log_employee_message("PyJWT库未安装，使用简化token")
                
            # 最后的回退方案（不是真正的JWT，只用于调试）
            import base64
            import json
            import time
            
            token_data = {
                "employee_id": self.employee_id,
                "employee_name": self.employee_name,
                "user_type": "employee",
                "exp": int(time.time()) + 3600,
                "warning": "This is NOT a valid JWT token - fallback only"
            }
            
            fallback_token = base64.b64encode(json.dumps(token_data).encode()).decode()
            self.log_employee_message("使用简化token（非JWT格式，仅用于调试）")
            return f"fallback_{fallback_token}"
            
        except Exception as e:
            self.log_employee_message(f"生成token失败: {e}")
            return None
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 文件共享功能
    def upload_file_to_chat(self):
        """上传文件到聊天"""
        if not self.chat_connected:
            QtWidgets.QMessageBox.warning(self, "错误", "请先连接聊天服务器")
            return
        
        # 选择文件
        file_path, _ = QtWidgets.QFileDialog.getOpenFileName(
            self, "选择要上传的文件", "", 
            "所有文件 (*.*);;图片 (*.jpg *.png *.gif *.bmp);;文档 (*.pdf *.doc *.docx *.txt);;压缩包 (*.zip *.rar)"
        )
        
        if not file_path:
            return
        
        file_path = Path(file_path)
        
        # 检查文件大小（限制为10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        if file_path.stat().st_size > max_size:
            QtWidgets.QMessageBox.warning(self, "错误", "文件大小不能超过10MB")
            return
        
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 创建上传对话框
            dialog = FileUploadDialog(file_path.name, file_path.stat().st_size, self)
            if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
                room_id = dialog.get_room_id()
                is_private = dialog.is_private()
                
                # 发送文件到微服务器
                self._upload_file_to_microservice(file_content, file_path.name, room_id, is_private)
                
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "错误", f"文件上传失败: {str(e)}")
    
    def _upload_file_to_microservice(self, file_content: bytes, filename: str, room_id: str = None, is_private: bool = False):
        """发送文件到微服务器"""
        try:
            # 准备上传数据
            files = {'file': (filename, file_content)}
            data = {
                'room_id': room_id or 'room_general',
                'is_private': str(is_private).lower()
            }
            
            # 获取JWT token
            headers = {'Authorization': f'Bearer {self.get_employee_jwt_token()}'}
            
            # 发送到微服务器
            import requests
            response = requests.post(
                'http://localhost:8005/api/files/upload',
                files=files,
                data=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    file_info = result['data']
                    self.append_chat_message("系统", f"文件上传成功: {filename}", "system")
                    
                    # 在聊天中发送文件消息
                    if self.chat_connected and self.chat_websocket:
                        file_message = {
                            "type": "chat_message",
                            "message": f"[文件] {filename}",
                            "message_type": "file",
                            "file_info": file_info,
                            "room_id": room_id or "room_general"
                        }
                        # 2025/06/26.11:50+分离ui操作 - 根据运行模式选择合适的事件循环
                        if self.is_standalone and self.app_loop:
                            target_loop = self.app_loop
                        elif not self.is_standalone and self.main_window:
                            target_loop = self.main_window.client_app.loop
                        else:
                            target_loop = asyncio.get_event_loop()
                        
                        asyncio.run_coroutine_threadsafe(
                            self.chat_websocket.send(json.dumps(file_message)),
                            target_loop
                        )
                else:
                    QtWidgets.QMessageBox.warning(self, "错误", f"上传失败: {result.get('message', '未知错误')}")
            else:
                QtWidgets.QMessageBox.warning(self, "错误", f"上传失败: HTTP {response.status_code}")
                
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "错误", f"文件上传失败: {str(e)}")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 私聊功能
    def send_private_message(self):
        """发送私聊消息"""
        if not self.chat_connected:
            QtWidgets.QMessageBox.warning(self, "错误", "请先连接聊天服务器")
            return
        
        # 获取选中的用户
        current_item = self.user_list_widget.currentItem()
        if not current_item:
            QtWidgets.QMessageBox.warning(self, "错误", "请选择要私聊的用户")
            return
        
        user_data = current_item.data(QtCore.Qt.ItemDataRole.UserRole)
        recipient_id = user_data.get("employee_id")
        recipient_name = user_data.get("employee_name")
        
        if recipient_id == self.employee_id:
            QtWidgets.QMessageBox.information(self, "提示", "不能给自己发送私聊消息")
            return
        
        # 创建私聊对话框
        dialog = PrivateMessageDialog(recipient_name, self)
        if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            message = dialog.get_message()
            encrypted = dialog.is_encrypted()
            
            if message.strip():
                self._send_private_message_to_microservice(recipient_id, message, encrypted)
    
    def _send_private_message_to_microservice(self, recipient_id: str, message: str, encrypted: bool = False):
        """发送私聊消息到微服务器"""
        try:
            headers = {'Authorization': f'Bearer {self.get_employee_jwt_token()}'}
            data = {
                'recipient_id': recipient_id,
                'content': message,
                'message_type': 'text',
                'encrypted': encrypted
            }
            
            import requests
            response = requests.post(
                'http://localhost:8005/api/private/send',
                json=data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    self.append_chat_message("系统", f"私聊消息已发送", "system")
                else:
                    QtWidgets.QMessageBox.warning(self, "错误", f"发送失败: {result.get('message', '未知错误')}")
            else:
                QtWidgets.QMessageBox.warning(self, "错误", f"发送失败: HTTP {response.status_code}")
                
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "错误", f"私聊发送失败: {str(e)}")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 聊天室管理功能
    def show_room_management(self):
        """显示聊天室管理界面"""
        dialog = RoomManagementDialog(self)
        dialog.exec()
    
    def get_available_rooms(self):
        """获取可用聊天室列表"""
        try:
            headers = {'Authorization': f'Bearer {self.get_employee_jwt_token()}'}
            
            import requests
            response = requests.get(
                'http://localhost:8005/api/rooms/',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    return result['data']['rooms']
            
            return []
            
        except Exception as e:
            self.log_employee_message(f"获取聊天室列表失败: {e}")
            return []
    
    # 20250619.17:00 视频监控 - 视频相关方法
    def connect_to_video(self):
        """连接到视频监控服务"""
        pass
    
    def disconnect_from_video(self):
        """断开视频连接"""
        pass
    
    def _connect_video_websocket(self):
        """建立视频WebSocket连接（在独立线程中运行）"""
        pass
    
    async def _websocket_video_loop(self):
        """视频WebSocket连接循环"""
        pass
    
    @QtCore.pyqtSlot(bool)
    def update_video_connection_status(self, connected: bool):
        """更新视频连接状态UI"""
        pass
    
    @QtCore.pyqtSlot(dict)
    def handle_video_message(self, data: dict):
        pass

    
    def display_video_frame(self, frame_data: str):
        """显示视频帧"""
        pass
    
    def get_video_snapshot(self):
        """获取视频快照"""
        pass

    
    def restart_video_camera(self):
        """重启视频摄像头（解决显示问题）"""
        pass

    
    def initialize_yolo_model(self):
        pass
    
    def toggle_yolo_detection(self):
        pass
    
    def update_yolo_button_state(self):
        """更新YOLO按钮状态"""
        pass
    
    def update_yolo_stats_display(self):
        """更新YOLO统计信息显示"""
        try:
            pass
        except Exception as e:
            pass

    def _handle_server5_entries_response(self, result: dict, request_id: str):
        """
        2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 处理entries列表响应
        """
        try:
            if result.get("ok"):
                entries = result.get("data", [])
                self.table3_current_data = entries
                
                if entries:
                    self.log_employee_message(f"从entries分区表加载了 {len(entries)} 条记录")
                    # 转换为Table3显示格式
                    table3_records = []
                    for entry in entries:
                        # 字段映射：Server5 entries -> Table3显示
                        record = {
                            'db_id': entry.get('id'),  # 内部ID
                            'external_id': entry.get('external_id'),  # MDB ID
                            'DB_ID': entry.get('external_id', ''),  #20250710+18：50+修复：DB_ID应该映射到external_id
                            '従業員ｺｰﾄﾞ': entry.get('employee_id', ''),
                            '日付': entry.get('entry_date', ''),
                            '機種': entry.get('model', ''),
                            '号機': entry.get('number', ''),
                            '工場製番': entry.get('factory_number', ''),
                            '工事番号': entry.get('project_number', ''),
                            'ﾕﾆｯﾄ番号': entry.get('unit_number', ''),
                            '区分': str(entry.get('category', '')),
                            '項目': str(entry.get('item', '')),
                            '時間': str(entry.get('duration', 0)),
                            '所属ｺｰﾄﾞ': entry.get('department', '')
                        }
                        table3_records.append(record)
                    
                    # 填充Table3
                    self._populate_table(self.table3, table3_records)
                else:
                    self.log_employee_message("该月份没有entries记录")
                    self.table3.setRowCount(0)
            else:
                status_code = result.get("status_code", "未知")
                error_msg = result.get("message", "获取entries数据失败")
                self.log_employee_message(f"从 entries分区表 获取数据失败: {status_code} - {error_msg}")
                self.table3.setRowCount(0)
        except Exception as e:
            self.log_employee_message(f"处理entries响应异常: {e}")

    def _handle_server5_create_response(self, result: dict, request_id: str):
        """
        2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 处理创建entry响应
        """
        try:
            if result.get("ok"):
                entry_data = result.get("data", {})
                entry_id = entry_data.get('id')
                external_id = entry_data.get('external_id')
                
                self.log_employee_message(f"✅ 数据创建成功 (ID: {entry_id}, 外部ID: {external_id or '待同步'})")
                
                # 触发f4->f2->f6流程提示
                if not external_id:
                    self.log_employee_message("📤 正在后台同步到MDB...")
                
                # 刷新Table3数据
                self._fetch_5xml_data_for_table3()
                
                # 退出编辑模式
                self._exit_edit_mode()
                
            else:
                error_msg = result.get("message", "创建失败")
                self.log_employee_message(f"❌ 数据创建失败: {error_msg}")
                
        except Exception as e:
            self.log_employee_message(f"处理创建响应异常: {e}")

    def _handle_server5_update_response(self, result: dict, request_id: str):
        """
        2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 处理更新entry响应
        """
        try:
            if result.get("ok"):
                entry_data = result.get("data", {})
                entry_id = entry_data.get('id')
                
                self.log_employee_message(f"✅ 数据更新成功 (ID: {entry_id})")
                self.log_employee_message("📤 正在后台同步到MDB...")
                
                # 刷新Table3数据
                self._fetch_5xml_data_for_table3()
                
                # 退出编辑模式
                self._exit_edit_mode()
                
            else:
                error_msg = result.get("message", "更新失败")
                self.log_employee_message(f"❌ 数据更新失败: {error_msg}")
                
        except Exception as e:
            self.log_employee_message(f"处理更新响应异常: {e}")

    def _handle_server5_delete_response(self, result: dict, request_id: str):
        """
        2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 处理删除entry响应
        """
        try:
            if result.get("ok"):
                self.log_employee_message("✅ 数据删除成功")
                self.log_employee_message("📤 正在后台同步到MDB...")
                
                # 刷新Table3数据
                self._fetch_5xml_data_for_table3()
                
            else:
                error_msg = result.get("message", "删除失败")
                self.log_employee_message(f"❌ 数据删除失败: {error_msg}")
                
        except Exception as e:
            self.log_employee_message(f"处理删除响应异常: {e}")

    def _handle_server5_chart_response(self, result: dict, request_id: str):
        """20250709+15:40+修复主题 只处理Server5新API格式"""
        try:
            if result.get("ok"):
                chart_data = result.get("data", {})
                if chart_data and "daily_data" in chart_data:
                    is_current = "current" in request_id
                    QtCore.QMetaObject.invokeMethod(
                        self, "_update_chart_ui",
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(dict, chart_data),
                        QtCore.Q_ARG(str, chart_data.get("month_name", "")),
                        QtCore.Q_ARG(bool, is_current)
                    )
                else:
                    self.log_employee_message("⚠️ API返回的图表数据格式不正确")
                    self.log_employee_message(f"📊 返回的数据结构: {chart_data}")
            else:
                error_msg = result.get("message", "获取图表数据失败")
                self.log_employee_message(f"❌ 图表数据获取失败: {error_msg}")
        except Exception as e:
            self.log_employee_message(f"处理图表响应异常: {e}")

    def _fetch_xml_data_fallback(self):
        """
        2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - XML数据回退处理
        """
        # 回退到原始的XML数据获取逻辑
        self.log_employee_message("使用XML数据源获取Table3数据...")
        # 这里应该调用原来的XML获取逻辑
        pass

    async def _load_chart_data_from_db(self, target_month: datetime, display_name: str, is_current: bool = True):
        """
        2025/07/03 + 16:30 + 从PostgreSQL数据库加载图表数据（entries+timeprotab对比分析）
        修复闪退问题：添加缺失的图表数据加载方法
        """
        try:
            import asyncpg
            import sys
            from pathlib import Path
            from datetime import time
            
            # 使用server的数据库配置
            config_path = Path(__file__).parent.parent / "server" / "app"
            sys.path.append(str(config_path))
            
            from config import IMDB_DATABASE_URL, TABLE_CONFIG
            
            # 2025/07/03 + 16:10 + 使用server的数据库配置（已经是正确格式）
            db_url = IMDB_DATABASE_URL
            
            # 计算月份代码和分区表名
            month_code = target_month.strftime("%y%m")
            partition_name = TABLE_CONFIG['timeprotab']['partition_format'].format(month_code=month_code)
            
            # 连接数据库
            conn = await asyncpg.connect(db_url)
            
            try:
                # 检查timeprotab分区是否存在
                timeprotab_exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = $1 AND table_schema = 'public'
                    )
                """, partition_name)
                
                daily_data = []
                
                if timeprotab_exists:
                    # 从timeprotab获取考勤数据
                    timeprotab_query = f"""
                        SELECT 
                            日付,
                            出勤時刻,
                            退勤時刻
                        FROM {partition_name}
                        WHERE employee_id = $1
                        ORDER BY 日付
                    """
                    
                    timeprotab_rows = await conn.fetch(timeprotab_query, self.employee_id)
                    
                    # 从entries获取工作记录数据
                    start_date = target_month.replace(day=1).strftime('%Y-%m-%d')
                    if target_month.month == 12:
                        next_month = target_month.replace(year=target_month.year + 1, month=1, day=1)
                    else:
                        next_month = target_month.replace(month=target_month.month + 1, day=1)
                    end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
                    
                    entries_query = """
                        SELECT 
                            entry_date,
                            duration
                        FROM entries
                        WHERE employee_id = $1 
                          AND entry_date >= $2 
                          AND entry_date <= $3
                        ORDER BY entry_date
                    """
                    
                    entries_rows = await conn.fetch(entries_query, self.employee_id, start_date, end_date)
                    
                    # 创建entries数据的字典，方便查找
                    entries_dict = {}
                    for row in entries_rows:
                        if row['entry_date']:
                            entries_dict[row['entry_date']] = {
                                'duration': row['duration']
                            }
                    
                    # 处理timeprotab数据
                    for row in timeprotab_rows:
                        try:
                            date = row['日付']
                            if not date:
                                continue
                                
                            day_of_month = date.day
                            
                            # 安全处理出勤時刻和退勤時刻
                            clock_in_time = None
                            clock_out_time = None
                            
                            # 处理出勤時刻
                            if row['出勤時刻']:
                                try:
                                    if isinstance(row['出勤時刻'], time):
                                        clock_in_time = [row['出勤時刻'].hour, row['出勤時刻'].minute]
                                    elif isinstance(row['出勤時刻'], str):
                                        # 尝试解析字符串格式的时间
                                        if ':' in row['出勤時刻']:
                                            parts = row['出勤時刻'].split(':')
                                            if len(parts) >= 2:
                                                clock_in_time = [int(parts[0]), int(parts[1])]
                                        else:
                                            # 尝试解析其他格式
                                            parsed_time = time.fromisoformat(row['出勤時刻'])
                                            clock_in_time = [parsed_time.hour, parsed_time.minute]
                                except (ValueError, TypeError, AttributeError) as e:
                                    self.log_employee_message(f"⚠️ 出勤時刻解析失败 {date}: {row['出勤時刻']} - {e}")
                                    continue
                            
                            # 处理退勤時刻
                            if row['退勤時刻']:
                                try:
                                    if isinstance(row['退勤時刻'], time):
                                        clock_out_time = [row['退勤時刻'].hour, row['退勤時刻'].minute]
                                    elif isinstance(row['退勤時刻'], str):
                                        # 尝试解析字符串格式的时间
                                        if ':' in row['退勤時刻']:
                                            parts = row['退勤時刻'].split(':')
                                            if len(parts) >= 2:
                                                clock_out_time = [int(parts[0]), int(parts[1])]
                                        else:
                                            # 尝试解析其他格式
                                            parsed_time = time.fromisoformat(row['退勤時刻'])
                                            clock_out_time = [parsed_time.hour, parsed_time.minute]
                                except (ValueError, TypeError, AttributeError) as e:
                                    self.log_employee_message(f"⚠️ 退勤時刻解析失败 {date}: {row['退勤時刻']} - {e}")
                                    continue
                            
                            # 只有当两个时间都有效时才添加数据
                            if clock_in_time and clock_out_time:
                                # 检查是否与entries数据匹配
                                is_match = False
                                if date in entries_dict:
                                    entries_data = entries_dict[date]
                                    # 简单的匹配逻辑：检查是否有工时记录
                                    if entries_data['duration'] and entries_data['duration'] > 0:
                                        is_match = True
                                
                                daily_data.append({
                                    "day_of_month": day_of_month,
                                    "clock_in": clock_in_time,
                                    "clock_out": clock_out_time,
                                    "is_match": is_match
                                })
                        
                        except Exception as e:
                            self.log_employee_message(f"⚠️ 处理日期 {date} 数据时出错: {e}")
                            continue
                
                # 构建图表数据
                chart_data = {
                    "daily_data": daily_data,
                    "month_name": display_name
                }
                
                # 根据is_current参数决定更新哪个图表
                if is_current:
                    self.chart_widget.set_chart_data(chart_data)
                    self.chart_month_label.setText(f"勤務時間グラフ ({display_name})")
                    self.log_employee_message(f"✅ 主图表数据加载成功 ({len(daily_data)}个数据点)")
                else:
                    self.prev_chart_widget.set_chart_data(chart_data)
                    self.prev_month_label.setText(display_name)
                    self.log_employee_message(f"✅ 上月图表数据加载成功 ({len(daily_data)}个数据点)")
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.log_employee_message(f"❌ 图表数据加载失败: {e}")
            
            # 根据is_current参数决定更新哪个图表的错误状态
            if is_current:
                self.chart_widget.set_chart_data(None)
                self.chart_month_label.setText(f"勤務時間グラフ ({display_name}) - エラー")
            else:
                self.prev_chart_widget.set_chart_data(None)
                self.prev_month_label.setText(f"{display_name} - エラー")

    def _schedule_async_task(self, coro):
        """调度异步任务，在PyQt6环境中安全运行"""
        try:
            # 在新线程中运行异步任务  20250708 + 16:00 + 相关主题: 修复闪退问题
            import threading
            
            def run_coro():
                try:
                    # 在新线程中创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # 运行协程
                        loop.run_until_complete(coro)
                    finally:
                        loop.close()
                except Exception as e:
                    # 使用QMetaObject.invokeMethod在主线程中记录错误
                    QtCore.QMetaObject.invokeMethod(
                        self, 
                        "log_employee_message", 
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(str, f"❌ 异步任务执行失败: {e}")
                    )
            
            thread = threading.Thread(target=run_coro, daemon=True)
            thread.start()
            
        except Exception as e:
            self.log_employee_message(f"❌ 异步任务调度失败: {e}")


# #20250711+16：20+修改的主题@program1.py - 添加自定义Table Model类
# #20250711+16：40+修改的主题 - 添加Table1自定义模型类
class Table1Model(QtCore.QAbstractTableModel):
    """Table1的自定义数据模型，支持排序"""
    
    def __init__(self, data=None, headers=None, parent=None):
        super().__init__(parent)
        self._data = data or []
        self._headers = headers or []
        
    def rowCount(self, parent=QtCore.QModelIndex()):
        return len(self._data)
        
    def columnCount(self, parent=QtCore.QModelIndex()):
        return len(self._headers)
        
    def data(self, index, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None
            
        row = index.row()
        col = index.column()
        
        if row >= len(self._data) or col >= len(self._headers):
            return None
            
        if role == QtCore.Qt.ItemDataRole.DisplayRole:
            header = self._headers[col]
            # #20250711+16：40+修改的主题 - Table1特殊字段映射
            rec = self._data[row]
            if header == '曜日':
                value = rec.get('星期', '')  # 星期 -> 曜日
            elif header == 'カレンダ':
                value = rec.get('ｶﾚﾝﾀﾞ', '')  # 修复：使用正确的日文字段名
            elif header == 'MC_出勤':
                value = rec.get('ＭＣ_出勤', '')  # 修复：使用正确的全角字段名
            elif header == 'MC_退勤':
                value = rec.get('ＭＣ_退勤', '')  # 修复：使用正确的全角字段名
            else:
                value = rec.get(header, '')
            
            # 将None值显示为空字符串
            if value is None or value == 'None':
                value = ''
            
            return str(value)
        elif role == QtCore.Qt.ItemDataRole.TextAlignmentRole:
            # 时间相关的列右对齐
            header = self._headers[col]
            if any(time_keyword in header for time_keyword in ['時間', '時刻', '時分']):
                return QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter
            return QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter
        elif role == QtCore.Qt.ItemDataRole.BackgroundRole:
            # 20250714+09:30+ 法定/法外休日背景色
            try:
                rec = self._data[index.row()]
                calendar_value = rec.get('ｶﾚﾝﾀﾞ', '')
                if str(calendar_value) in ["法外", "法定"]:
                    return QtGui.QColor('darkgray')
            except IndexError:
                pass # Fails silently if the row index is out of bounds
                
            # 交替行背景色
            if row % 2 == 0:
                return QtGui.QColor("#ececec")
            return QtGui.QColor("#ffffff")
            
        return None
        
    def headerData(self, section, orientation, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if orientation == QtCore.Qt.Orientation.Horizontal and role == QtCore.Qt.ItemDataRole.DisplayRole:
            if section < len(self._headers):
                return self._headers[section]
        elif role == QtCore.Qt.ItemDataRole.BackgroundRole:
            # 20250714+09:30+ 表头背景色设置为浅绿色
            return QtGui.QColor("#90EE90")  # 浅绿色
        return None
        
    def sort(self, column, order=QtCore.Qt.SortOrder.AscendingOrder):
        """排序功能"""
        if column < 0 or column >= len(self._headers):
            return
            
        header = self._headers[column]
        
        # 开始重置模型
        self.beginResetModel()
        
        # 根据列类型选择排序方式
        if '日付' in header or '日' in header:
            # 日期类型排序
            def parse_date(date_str):
                try:
                    # 尝试多种日期格式
                    for fmt in ['%Y/%m/%d', '%Y-%m-%d', '%m/%d', '%d']:
                        try:
                            return datetime.strptime(str(date_str), fmt)
                        except ValueError:
                            continue
                    return datetime.min
                except:
                    return datetime.min
            
            self._data.sort(key=lambda x: parse_date(x.get(header, '')), 
                          reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        elif any(time_keyword in header for time_keyword in ['時間', '時刻', '時分']):
            # 时间类型排序
            def parse_time(time_str):
                try:
                    time_str = str(time_str).strip()
                    if ':' in time_str:
                        parts = time_str.split(':')
                        hours = int(parts[0])
                        minutes = int(parts[1]) if len(parts) > 1 else 0
                        return hours * 60 + minutes
                    else:
                        return float(time_str) if time_str else 0
                except:
                    return 0
            
            self._data.sort(key=lambda x: parse_time(x.get(header, '')), 
                          reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        else:
            # 文本类型排序
            self._data.sort(key=lambda x: str(x.get(header, '')), 
                          reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        
        # 完成重置模型
        self.endResetModel()
        
    def update_data(self, data):
        """更新数据"""
        self.beginResetModel()
        self._data = data or []
        self.endResetModel()
        
    def get_row_data(self, row):
        """获取指定行的数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None

class Table3Model(QtCore.QAbstractTableModel):
    """Table3的自定义数据模型，支持排序"""
    
    def __init__(self, data=None, headers=None, parent=None):
        super().__init__(parent)
        self._data = data or []
        self._headers = headers or []
        
    def rowCount(self, parent=QtCore.QModelIndex()):
        return len(self._data)
        
    def columnCount(self, parent=QtCore.QModelIndex()):
        return len(self._headers)
        
    def data(self, index, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None
            
        row = index.row()
        col = index.column()
        
        if row >= len(self._data) or col >= len(self._headers):
            return None
            
        if role == QtCore.Qt.ItemDataRole.DisplayRole:
            header = self._headers[col]
            return str(self._data[row].get(header, ''))
        elif role == QtCore.Qt.ItemDataRole.TextAlignmentRole:
            # 数字类型的列右对齐
            header = self._headers[col]
            if header in ['時間', 'DB_ID']:
                return QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter
            return QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter
        elif role == QtCore.Qt.ItemDataRole.BackgroundRole:
            # 交替行背景色（灰色稍微加深）
            if row % 2 == 0:
                return QtGui.QColor("#ececec")  # 比 #f9f9f9 稍深
            return QtGui.QColor("#ffffff")
        return None
        
    def headerData(self, section, orientation, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if orientation == QtCore.Qt.Orientation.Horizontal and role == QtCore.Qt.ItemDataRole.DisplayRole:
            if section < len(self._headers):
                return self._headers[section]
        elif role == QtCore.Qt.ItemDataRole.BackgroundRole:
            # 20250714+09:30+ 表头背景色设置为浅绿色
            return QtGui.QColor("#90EE90")  # 浅绿色
        return None
        
    def sort(self, column, order=QtCore.Qt.SortOrder.AscendingOrder):
        """排序功能"""
        if column < 0 or column >= len(self._headers):
            return
            
        header = self._headers[column]
        
        # 开始重置模型
        self.beginResetModel()
        
        # 根据列类型选择排序方式
        if header in ['時間', 'DB_ID']:
            # 数字类型排序
            try:
                self._data.sort(key=lambda x: float(x.get(header, 0) or 0), 
                              reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
            except (ValueError, TypeError):
                # 如果转换失败，按字符串排序
                self._data.sort(key=lambda x: str(x.get(header, '')), 
                              reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        elif header == '日付':
            # 日期类型排序
            def parse_date(date_str):
                try:
                    return datetime.strptime(str(date_str), '%Y-%m-%d')
                except:
                    return datetime.min
            
            self._data.sort(key=lambda x: parse_date(x.get(header, '')), 
                          reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        else:
            # 文本类型排序
            self._data.sort(key=lambda x: str(x.get(header, '')), 
                          reverse=(order == QtCore.Qt.SortOrder.DescendingOrder))
        
        # 完成重置模型
        self.endResetModel()
        
    def update_data(self, data):
        """更新数据"""
        self.beginResetModel()
        self._data = data or []
        self.endResetModel()
        
    def get_row_data(self, row):
        """获取指定行的数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None

class ChartWidget(QtWidgets.QWidget):
    """自定义图表绘制Widget - 柱状图版本"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.chart_data = None
        self.setMinimumHeight(300)
        self.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        
        # 定义图表常量
        self.Y_AXIS_START_HOUR = 6
        self.Y_AXIS_END_HOUR = 22
        self.X_PADDING_LEFT = 50
        self.X_PADDING_RIGHT = 100  #20250710+18：00 - 增加右边距为时间标签留空间
        self.Y_PADDING_TOP = 40
        self.Y_PADDING_BOTTOM = 30
        
        self.COLOR_GRID = QtGui.QColor("#e0e0e0")
        self.COLOR_AXIS = QtGui.QColor("black")
        self.COLOR_MATCH = QtGui.QColor("skyblue")
        self.COLOR_MISMATCH = QtGui.QColor("red")
        self.COLOR_MATCH_BORDER = QtGui.QColor("blue")

    def set_chart_data(self, data):
        """设置图表数据并触发重绘"""
        #20250710+18：50+修复：添加调试信息
        widget_width = self.width()
        chart_type = "小chart" if widget_width < 300 else "大chart"
        
        if data:
            chart_days = data.get("chart_days", [])
            month_name = data.get("month_name", "未知月份")
            print(f"[DEBUG] {chart_type} 接收到数据: {month_name}, 数据点数: {len(chart_days)}")
        else:
            print(f"[DEBUG] {chart_type} 接收到空数据")
            
        self.chart_data = data
        self.update()

    def paintEvent(self, event):
        """绘制图表"""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)
        painter.fillRect(self.rect(), QtGui.QColor("white"))

        if not self.chart_data:
            #20250710+18：50+修复：小chart也显示基本框架
            widget_width = self.width()
            
            # 即使没有数据，也绘制基本的轴线和标签
            if widget_width < 300:  # 小chart - 简化显示
                # 绘制简单的轴线
                painter.setPen(self.COLOR_AXIS)
                # 垂直轴
                painter.drawLine(30, 20, 30, self.height() - 20)
                # 水平轴
                painter.drawLine(30, self.height() - 20, self.width() - 10, self.height() - 20)
                
                # 显示数据状态
                painter.setFont(QtGui.QFont("Arial", 10))
                painter.drawText(self.rect(), QtCore.Qt.AlignmentFlag.AlignCenter, "データなし")
            else:  # 大chart - 正常显示
                painter.setPen(self.COLOR_AXIS)
                painter.setFont(QtGui.QFont("Arial", 12))
                painter.drawText(self.rect(), QtCore.Qt.AlignmentFlag.AlignCenter, "データなし")
            return
        
        # #20250711+10：15+修改的主题 - 即使没有chart_days，也要显示完整的X轴标签
        chart_days = self.chart_data.get("chart_days", [])
        if not chart_days:
            # 没有chart_days数据，但仍然显示完整的坐标轴
            self._draw_bar_chart(painter)
            return

        self._draw_bar_chart(painter)

    def _time_to_y_coord(self, hour, minute, chart_area_rect):
        """将时分转换为Y轴坐标"""
        time_in_hours = hour + minute / 60.0
        total_hours_in_axis = self.Y_AXIS_END_HOUR - self.Y_AXIS_START_HOUR
        if total_hours_in_axis <= 0:
            return chart_area_rect.bottom()

        proportion = (time_in_hours - self.Y_AXIS_START_HOUR) / total_hours_in_axis
        # Y轴是反的，顶部是0
        return chart_area_rect.top() + (1 - proportion) * chart_area_rect.height()

    def _draw_bar_chart(self, painter):
        """绘制柱状图"""
        #20250710+18：50+修复：根据Widget大小动态调整边距
        widget_width = self.width()
        widget_height = self.height()
        
        # 动态调整边距 - 小chart用小边距，大chart用大边距
        if widget_width < 300:  # 小chart
            x_padding_left = 30
            x_padding_right = 10  # 小chart不需要右侧标签空间
            y_padding_top = 20
            y_padding_bottom = 40  # #20250711+10：15+修改的主题 - 增加底边距，留出日期和星期显示空间
        else:  # 大chart
            x_padding_left = self.X_PADDING_LEFT
            x_padding_right = self.X_PADDING_RIGHT
            y_padding_top = self.Y_PADDING_TOP
            y_padding_bottom = 50  # #20250711+10：15+修改的主题 - 增加底边距，留出日期和星期显示空间
        
        # 计算绘图区域
        chart_rect = self.rect().adjusted(
            x_padding_left, y_padding_top,
            -x_padding_right, -y_padding_bottom
        )
        if chart_rect.width() <= 0 or chart_rect.height() <= 0:
            return

        # 绘制Y轴 (时间)
        painter.setPen(self.COLOR_AXIS)
        painter.drawLine(chart_rect.topLeft(), chart_rect.bottomLeft())
        painter.setFont(QtGui.QFont("Arial", 8))
        
        total_hours = self.Y_AXIS_END_HOUR - self.Y_AXIS_START_HOUR
        for i in range(total_hours + 1):
            hour_val = self.Y_AXIS_END_HOUR - i
            if hour_val % 1 != 0 and hour_val != self.Y_AXIS_START_HOUR: # 每2小时一个标签
                continue

            y = chart_rect.top() + (i / total_hours) * chart_rect.height()
            
            # 绘制刻度和网格线
            painter.setPen(self.COLOR_GRID)
            painter.drawLine(chart_rect.left(), int(y), chart_rect.right(), int(y))
            
            # 绘制标签 - 小chart简化标签
            painter.setPen(self.COLOR_AXIS)
            label = f"{hour_val:02d}:00"
            if widget_width < 300:  # 小chart用更小的标签区域
                painter.drawText(0, int(y) - 10, x_padding_left - 2, 20,
                                 QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter, label)
            else:  # 大chart用正常标签
                painter.drawText(0, int(y) - 10, x_padding_left - 5, 20,
                                 QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter, label)

        # 绘制X轴
        painter.setPen(self.COLOR_AXIS)
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.bottomRight())

        #20250710+18：00 - 添加公司上班规则的背景效果
        self._draw_company_rules_background(painter, chart_rect)

        # 绘制数据柱和X轴标签 - #20250711+10：15+修改的主题
        chart_days = self.chart_data.get("chart_days", [])
        
        # #20250711+10：15+修改的主题 - 根据实际月份计算正确的天数，不再硬编码31天
        try:
            # 从chart_data获取当前月份信息
            month_name = self.chart_data.get("month_name", "")
            if month_name:
                # 解析月份名称，如"2025年07月"
                import re
                from datetime import datetime as dt_class
                import calendar
                match = re.search(r'(\d{4})年(\d{1,2})月', month_name)
                if match:
                    year = int(match.group(1))
                    month = int(match.group(2))
                    # 获取该月的实际天数
                    num_days = calendar.monthrange(year, month)[1]
                    print(f"[DEBUG] Chart显示: {year}年{month}月，实际天数: {num_days}天")  # #20250711+10：15+修改的主题 - 调试信息
                else:
                    num_days = 31  # 如果解析失败，回退到31天
                    print(f"[DEBUG] Chart显示: 月份解析失败，使用默认31天")  # #20250711+10：15+修改的主题 - 调试信息
            else:
                num_days = 31  # 如果没有月份信息，回退到31天
                print(f"[DEBUG] Chart显示: 没有月份信息，使用默认31天")  # #20250711+10：15+修改的主题 - 调试信息
        except:
            num_days = 31  # 如果出现任何错误，回退到31天
            print(f"[DEBUG] Chart显示: 天数计算异常，使用默认31天")  # #20250711+10：15+修改的主题 - 调试信息
            
        day_slot_width = chart_rect.width() / num_days
        
        # 创建一个完整的日期映射，包含timeprotab信息
        days_dict = {}
        for day_info in chart_days:
            day = day_info.get("day")
            if day:
                days_dict[day] = day_info
        
        # 为所有日期绘制X轴标签，不管是否有数据
        for day in range(1, num_days + 1):
            bar_x = chart_rect.left() + (day - 1) * day_slot_width
            
            # 如果有该日期的数据，绘制柱状图
            if day in days_dict:
                day_info = days_dict[day]
                clock_in_str = day_info.get("clock_in")
                clock_out_str = day_info.get("clock_out")
                is_matched = day_info.get("is_matched", False)

                if clock_in_str and clock_out_str:
                    # 转换字符串时间为 [hour, minute] 格式
                    try:
                        clock_in_parts = clock_in_str.split(":")
                        clock_in = [int(clock_in_parts[0]), int(clock_in_parts[1])]
                        
                        clock_out_parts = clock_out_str.split(":")
                        clock_out = [int(clock_out_parts[0]), int(clock_out_parts[1])]
                        
                        # 计算柱体坐标
                        y1 = self._time_to_y_coord(clock_in[0], clock_in[1], chart_rect)
                        y2 = self._time_to_y_coord(clock_out[0], clock_out[1], chart_rect)
                        
                        # Y坐标是反的，所以y1（上班）应该比y2（下班）大
                        bar_top = min(y1, y2)
                        bar_height = abs(y1 - y2)

                        bar_rect = QtCore.QRectF(bar_x, bar_top, day_slot_width * 0.8, bar_height)
                        
                        # 设置颜色 (根据WorkTimeApp.py: 匹配=蓝色, 不匹配=红色)
                        fill_color = self.COLOR_MATCH if is_matched else self.COLOR_MISMATCH
                        border_color = self.COLOR_MATCH_BORDER if is_matched else self.COLOR_MISMATCH

                        painter.setBrush(QtGui.QBrush(fill_color))
                        painter.setPen(QtGui.QPen(border_color, 1))
                        painter.drawRect(bar_rect)
                        
                    except (ValueError, IndexError):
                        pass  # 跳过无效的时间格式
                        
                # 使用实际的day_info
                actual_day_info = day_info
            else:
                # 没有数据时，创建一个基本的day_info
                # 需要根据日期计算星期几
                try:
                    # 从chart_data获取当前月份信息
                    month_name = self.chart_data.get("month_name", "")
                    if month_name:
                        # 解析月份名称，如"2025年07月"
                        import re
                        from datetime import datetime as dt_class  # #20250711+10：15+修改的主题 - 避免变量名冲突
                        match = re.search(r'(\d{4})年(\d{1,2})月', month_name)
                        if match:
                            year = int(match.group(1))
                            month = int(match.group(2))
                            date_obj = dt_class(year, month, day)
                            weekday = date_obj.weekday()  # 0=Monday, 6=Sunday
                        else:
                            weekday = -1
                    else:
                        weekday = -1
                except:
                    weekday = -1
                    
                # #20250711+10：15+修改的主题 - 从timeprotab_info获取ｶﾚﾝﾀﾞ信息
                timeprotab_info = self.chart_data.get("timeprotab_info", {})
                if day in timeprotab_info:
                    calendar_type = timeprotab_info[day]["calendar_type"]
                    timeprotab_weekday = timeprotab_info[day]["weekday"]
                    absence = timeprotab_info[day]["absence"]
                    work_type = timeprotab_info[day]["work_type"]
                else:
                    calendar_type = ""
                    timeprotab_weekday = ""
                    absence = ""
                    work_type = ""
                    
                actual_day_info = {
                    "day": day,
                    "weekday": weekday,
                    "calendar_type": calendar_type,
                    "timeprotab_weekday": timeprotab_weekday,
                    "absence": absence,
                    "work_type": work_type
                }
            
            # 绘制X轴标签 (日期和星期) - #20250711+10：15+修改的主题
            # 获取timeprotab的ｶﾚﾝﾀﾞ属性来确定颜色
            calendar_type = actual_day_info.get("calendar_type", "")  # 从day_info获取ｶﾚﾝﾀﾞ属性
            
            # 根据ｶﾚﾝﾀﾞ属性设置颜色
            if calendar_type in ["法外", "法定"]:
                text_color = QtGui.QColor(255, 0, 0)  # 红色
            elif calendar_type and calendar_type not in ["", None]:
                text_color = QtGui.QColor(0, 0, 255)  # 蓝色
            else:
                text_color = QtGui.QColor(0, 0, 0)  # 黑色（默认）
            
            painter.setPen(text_color)
            
            # 准备日期和星期文本
            date_text = str(day)
            
            # 日语星期映射 - #20250711+10：15+修改的主题
            # 优先使用timeprotab中的"星期"信息
            timeprotab_weekday = actual_day_info.get("timeprotab_weekday", "")
            if timeprotab_weekday:
                weekday_text = timeprotab_weekday
            else:
                # 如果没有timeprotab信息，使用计算得出的星期
                japanese_weekdays = ['月', '火', '水', '木', '金', '土', '日']
                weekday = actual_day_info.get("weekday", -1)
                if 0 <= weekday <= 6:
                    weekday_text = japanese_weekdays[weekday]
                else:
                    weekday_text = "?"
            
            # 设置字体大小
            font_size = 8 if widget_width < 300 else 10
            painter.setFont(QtGui.QFont("Arial", font_size))
            
            # 计算标签区域，分为上下两部分
            label_rect = QtCore.QRectF(bar_x, chart_rect.bottom(), day_slot_width, y_padding_bottom)
            date_rect = QtCore.QRectF(bar_x, chart_rect.bottom(), day_slot_width, y_padding_bottom / 2)
            weekday_rect = QtCore.QRectF(bar_x, chart_rect.bottom() + y_padding_bottom / 2, day_slot_width, y_padding_bottom / 2)
            
            # 小chart只显示部分标签，避免过度拥挤
            should_show_label = True
            if widget_width < 300:
                should_show_label = (day % 5 == 1)  # 每5天显示一个标签
            
            if should_show_label:
                # 绘制日期（上半部分）
                painter.drawText(date_rect, QtCore.Qt.AlignmentFlag.AlignCenter, date_text)
                # 绘制星期（下半部分）
                painter.drawText(weekday_rect, QtCore.Qt.AlignmentFlag.AlignCenter, weekday_text)

        # 绘制标题 - 小chart用小字体
        if widget_width < 300:  # 小chart
            painter.setFont(QtGui.QFont("Arial", 10, QtGui.QFont.Weight.Bold))
        else:  # 大chart
            painter.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Weight.Bold))
        painter.drawText(self.rect().adjusted(0, 5, 0, 0), QtCore.Qt.AlignmentFlag.AlignHCenter | QtCore.Qt.AlignmentFlag.AlignTop,
                         self.chart_data.get("month_name", "勤務時間グラフ"))

    def _draw_company_rules_background(self, painter, chart_rect):
        """#20250710+18：00 - 绘制公司上班规则的背景效果"""
        # 公司规则时间点
        work_start_time = (8, 30)   # 08:30 正式上班
        work_end_time = (17, 30)    # 17:30 正式下班
        lunch_start_time = (12, 10)  # 12:10 午休开始
        lunch_end_time = (13, 0)     # 13:00 午休结束
        
        #20250710+18：50+修复：左边Chart显示区域太小，简化背景效果
        is_small_chart = chart_rect.width() < 300
        
        # 计算Y坐标
        work_start_y = self._time_to_y_coord(work_start_time[0], work_start_time[1], chart_rect)
        work_end_y = self._time_to_y_coord(work_end_time[0], work_end_time[1], chart_rect)
        lunch_start_y = self._time_to_y_coord(lunch_start_time[0], lunch_start_time[1], chart_rect)
        lunch_end_y = self._time_to_y_coord(lunch_end_time[0], lunch_end_time[1], chart_rect)
        
        # 1. 绘制正式上班时间背景 (08:30-17:30) - 浅蓝色（仅大Chart显示）
        if not is_small_chart:
            work_rect = QtCore.QRectF(
                chart_rect.left(), 
                work_end_y,  # 17:30 (Y轴是反的)
                chart_rect.width(), 
                work_start_y - work_end_y  # 08:30 到 17:30
            )
            painter.fillRect(work_rect, QtGui.QColor(173, 216, 230, 50))  # 浅蓝色，半透明
            
            # 2. 绘制午休时间背景 (12:10-13:00) - 灰色（仅大Chart显示）
            lunch_rect = QtCore.QRectF(
                chart_rect.left(),
                lunch_end_y,  # 13:00 (Y轴是反的)
                chart_rect.width(),
                lunch_start_y - lunch_end_y  # 12:10 到 13:00
            )
            painter.fillRect(lunch_rect, QtGui.QColor(128, 128, 128, 80))  # 灰色，半透明
        
        # 3. 绘制关键时间线 - 大Chart使用粗线，小Chart使用细线
        line_width = 1 if is_small_chart else 2
        
        # 08:30 上班线和17:30 下班线 (红色虚线)
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 0, 0), line_width, QtCore.Qt.PenStyle.DashLine))
        painter.drawLine(chart_rect.left(), int(work_start_y), chart_rect.right(), int(work_start_y))
        painter.drawLine(chart_rect.left(), int(work_end_y), chart_rect.right(), int(work_end_y))
        
        # 午休线 (绿色实线)
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 128, 0), line_width, QtCore.Qt.PenStyle.SolidLine))
        painter.drawLine(chart_rect.left(), int(lunch_start_y), chart_rect.right(), int(lunch_start_y))
        painter.drawLine(chart_rect.left(), int(lunch_end_y), chart_rect.right(), int(lunch_end_y))
        
        # 4. 添加时间标签 (在图表右侧) - 仅大Chart显示
        if not is_small_chart:
            painter.setFont(QtGui.QFont("Arial", 8, QtGui.QFont.Weight.Bold))
            label_x = chart_rect.right() + 5
            
            # 上班时间标签 (红色)
            painter.setPen(QtGui.QColor(255, 0, 0))
            painter.drawText(label_x, int(work_start_y) - 15, 80, 20,
                             QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter, "08:30 出勤")
            painter.drawText(label_x, int(work_end_y) - 5, 80, 20,
                             QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter, "17:30 退勤")
            
            # 午休时间标签 (绿色) - 修复重叠问题
            painter.setPen(QtGui.QColor(0, 128, 0))
            painter.drawText(label_x, int(lunch_start_y) - 20, 80, 20,
                             QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter, "12:10 昼休")
            painter.drawText(label_x, int(lunch_end_y) + 10, 80, 20,
                             QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter, "13:00 昼休終")




# 20250618.20:30 增加加密和文件共享以及数据库 - 新的对话框类

class FileUploadDialog(QtWidgets.QDialog):
    """文件上传对话框"""
    
    def __init__(self, filename: str, file_size: int, parent=None):
        super().__init__(parent)
        self.setWindowTitle("文件上传设置")
        self.setFixedSize(400, 300)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # 文件信息
        info_group = QtWidgets.QGroupBox("文件信息")
        info_layout = QtWidgets.QFormLayout(info_group)
        
        info_layout.addRow("文件名:", QtWidgets.QLabel(filename))
        info_layout.addRow("文件大小:", QtWidgets.QLabel(f"{file_size / 1024:.1f} KB"))
        
        layout.addWidget(info_group)
        
        # 上传设置
        settings_group = QtWidgets.QGroupBox("上传设置")
        settings_layout = QtWidgets.QFormLayout(settings_group)
        
        # 聊天室选择
        self.room_combo = QtWidgets.QComboBox()
        self.room_combo.addItem("通用聊天室", "room_general")
        self.room_combo.addItem("工作讨论", "room_work")
        self.room_combo.addItem("项目A讨论组", "room_project_a")
        settings_layout.addRow("目标聊天室:", self.room_combo)
        
        # 私有设置
        self.private_checkbox = QtWidgets.QCheckBox("设为私有文件")
        self.private_checkbox.setToolTip("私有文件只有上传者可以下载")
        settings_layout.addRow("", self.private_checkbox)
        
        layout.addWidget(settings_group)
        
        # 按钮
        buttons = QtWidgets.QHBoxLayout()
        
        self.upload_btn = QtWidgets.QPushButton("上传")
        self.upload_btn.clicked.connect(self.accept)
        self.upload_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        
        self.cancel_btn = QtWidgets.QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        
        buttons.addStretch()
        buttons.addWidget(self.upload_btn)
        buttons.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons)
    
    def get_room_id(self) -> str:
        return self.room_combo.currentData()
    
    def is_private(self) -> bool:
        return self.private_checkbox.isChecked()

class PrivateMessageDialog(QtWidgets.QDialog):
    """私聊消息对话框"""
    
    def __init__(self, recipient_name: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"发送私聊消息给 {recipient_name}")
        self.setFixedSize(450, 350)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # 接收者信息
        recipient_label = QtWidgets.QLabel(f"接收者: {recipient_name}")
        recipient_label.setStyleSheet("font-weight: bold; color: #2196F3; padding: 10px; border: 1px solid #ccc;")
        layout.addWidget(recipient_label)
        
        # 消息输入
        message_group = QtWidgets.QGroupBox("消息内容")
        message_layout = QtWidgets.QVBoxLayout(message_group)
        
        self.message_edit = QtWidgets.QTextEdit()
        self.message_edit.setPlaceholderText("请输入私聊消息...")
        self.message_edit.setMaximumHeight(150)
        message_layout.addWidget(self.message_edit)
        
        layout.addWidget(message_group)
        
        # 加密选项
        encryption_group = QtWidgets.QGroupBox("安全选项")
        encryption_layout = QtWidgets.QVBoxLayout(encryption_group)
        
        self.encrypt_checkbox = QtWidgets.QCheckBox("加密消息")
        self.encrypt_checkbox.setToolTip("启用端到端加密（需要服务器支持）")
        encryption_layout.addWidget(self.encrypt_checkbox)
        
        layout.addWidget(encryption_group)
        
        # 按钮
        buttons = QtWidgets.QHBoxLayout()
        
        self.send_btn = QtWidgets.QPushButton("发送")
        self.send_btn.clicked.connect(self.accept)
        self.send_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        
        self.cancel_btn = QtWidgets.QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        
        buttons.addStretch()
        buttons.addWidget(self.send_btn)
        buttons.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons)
    
    def get_message(self) -> str:
        return self.message_edit.toPlainText()
    
    def is_encrypted(self) -> bool:
        return self.encrypt_checkbox.isChecked()

# 2025/07/16 + 13：24 + 修改小弹窗 - 创建右下角通知弹窗类
class NotificationToast(QtWidgets.QWidget):
    """2025/07/16 + 13：24 + 修改小弹窗 - 右下角通知弹窗，非模态，不阻塞UI操作"""
    
    def __init__(self, title: str, message: str, parent=None, duration: int = 5000):
        super().__init__(parent)
        self.duration = duration
        self.setup_ui(title, message)
        self.setup_animation()
        
    def setup_ui(self, title: str, message: str):
        """设置UI界面"""
        # 设置窗口属性 - 无边框，置顶，工具窗口
        self.setWindowFlags(
            QtCore.Qt.WindowType.FramelessWindowHint |
            QtCore.Qt.WindowType.Tool |
            QtCore.Qt.WindowType.WindowStaysOnTopHint
        )
        
        # 设置窗口属性
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_DeleteOnClose)
        
        # 创建主布局
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建内容容器
        self.content_widget = QtWidgets.QWidget()
        self.content_widget.setObjectName("notification_content")
        self.content_widget.setStyleSheet("""
            QWidget#notification_content {
                background-color: #2c3e50;
                border-radius: 8px;
                border: 1px solid #34495e;
            }
        """)
        
        content_layout = QtWidgets.QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(8)
        
        # 标题
        title_label = QtWidgets.QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(title_label)
        
        # 消息内容
        message_label = QtWidgets.QLabel(message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        content_layout.addWidget(message_label)
        
        # 添加关闭按钮
        close_button = QtWidgets.QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #95a5a6;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                color: #e74c3c;
            }
        """)
        close_button.clicked.connect(self.close)
        
        # 创建水平布局放置标题和关闭按钮
        header_layout = QtWidgets.QHBoxLayout()
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(close_button)
        content_layout.insertLayout(0, header_layout)
        
        layout.addWidget(self.content_widget)
        
        # 设置固定大小
        self.setFixedSize(300, 100)
        
    def setup_animation(self):
        """设置动画效果"""
        # 创建淡入动画
        self.fade_in = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_in.setDuration(300)
        self.fade_in.setStartValue(0.0)
        self.fade_in.setEndValue(1.0)
        
        # 创建淡出动画
        self.fade_out = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_out.setDuration(300)
        self.fade_out.setStartValue(1.0)
        self.fade_out.setEndValue(0.0)
        self.fade_out.finished.connect(self.close)
        
        # 创建滑动动画
        self.slide_in = QtCore.QPropertyAnimation(self, b"geometry")
        self.slide_in.setDuration(300)
        
        self.slide_out = QtCore.QPropertyAnimation(self, b"geometry")
        self.slide_out.setDuration(300)
        
    def show_notification(self):
        """显示通知"""
        # 获取主窗口位置和大小
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.right() - self.width() - 20
            y = parent_rect.bottom() - self.height() - 20
        else:
            # 如果没有父窗口，使用屏幕右下角
            screen = QtWidgets.QApplication.primaryScreen().geometry()
            x = screen.right() - self.width() - 20
            y = screen.bottom() - self.height() - 20
        
        # 设置初始位置（屏幕外）
        self.setGeometry(x + self.width(), y, self.width(), self.height())
        
        # 设置滑动动画
        self.slide_in.setStartValue(self.geometry())
        self.slide_in.setEndValue(QtCore.QRect(x, y, self.width(), self.height()))
        
        # 显示窗口
        self.show()
        
        # 开始动画
        self.fade_in.start()
        self.slide_in.start()
        
        # 设置自动关闭定时器
        QtCore.QTimer.singleShot(self.duration, self.start_fade_out)
        
    def start_fade_out(self):
        """开始淡出动画"""
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.right() - self.width() - 20
            y = parent_rect.bottom() - self.height() - 20
        else:
            screen = QtWidgets.QApplication.primaryScreen().geometry()
            x = screen.right() - self.width() - 20
            y = screen.bottom() - self.height() - 20
        
        # 设置滑动动画
        self.slide_out.setStartValue(self.geometry())
        self.slide_out.setEndValue(QtCore.QRect(x + self.width(), y, self.width(), self.height()))
        
        self.fade_out.start()
        self.slide_out.start()
        
    def mousePressEvent(self, event):
        """鼠标点击事件 - 点击任意位置关闭"""
        if event.button() == QtCore.Qt.MouseButton.LeftButton:
            self.start_fade_out()

class RoomManagementDialog(QtWidgets.QDialog):
    """聊天室管理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("聊天室管理")
        self.setFixedSize(600, 500)
        self.parent_window = parent
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # 标题
        title = QtWidgets.QLabel("聊天室管理")
        title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; padding: 10px;")
        layout.addWidget(title)
        
        # 聊天室列表
        rooms_group = QtWidgets.QGroupBox("可用聊天室")
        rooms_layout = QtWidgets.QVBoxLayout(rooms_group)
        
        # 刷新按钮
        refresh_btn = QtWidgets.QPushButton("刷新聊天室列表")
        refresh_btn.clicked.connect(self.refresh_rooms)
        refresh_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px;")
        rooms_layout.addWidget(refresh_btn)
        
        # 聊天室表格
        self.rooms_table = QtWidgets.QTableWidget(0, 6)
        self.rooms_table.setHorizontalHeaderLabels([
            "聊天室名称", "分类", "类型", "成员数", "最大成员", "我的角色"
        ])
        self.rooms_table.horizontalHeader().setStretchLastSection(True)
        rooms_layout.addWidget(self.rooms_table)
        
        layout.addWidget(rooms_group)
        
        # 操作按钮
        actions_group = QtWidgets.QGroupBox("操作")
        actions_layout = QtWidgets.QHBoxLayout(actions_group)
        
        self.join_btn = QtWidgets.QPushButton("加入聊天室")
        self.join_btn.clicked.connect(self.join_room)
        self.join_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        
        self.leave_btn = QtWidgets.QPushButton("退出聊天室")
        self.leave_btn.clicked.connect(self.leave_room)
        self.leave_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        
        self.info_btn = QtWidgets.QPushButton("查看详情")
        self.info_btn.clicked.connect(self.show_room_info)
        self.info_btn.setStyleSheet("background-color: #607D8B; color: white; font-weight: bold; padding: 8px;")
        
        actions_layout.addWidget(self.join_btn)
        actions_layout.addWidget(self.leave_btn)
        actions_layout.addWidget(self.info_btn)
        actions_layout.addStretch()
        
        layout.addWidget(actions_group)
        
        # 关闭按钮
        close_btn = QtWidgets.QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        layout.addWidget(close_btn)
        
        # 加载初始数据
        self.refresh_rooms()
    
    def refresh_rooms(self):
        """刷新聊天室列表"""
        try:
            rooms = self.parent_window.get_available_rooms()
            
            self.rooms_table.setRowCount(len(rooms))
            
            for i, room in enumerate(rooms):
                self.rooms_table.setItem(i, 0, QtWidgets.QTableWidgetItem(room.get("name", "")))
                self.rooms_table.setItem(i, 1, QtWidgets.QTableWidgetItem(room.get("category", "")))
                
                room_type = "私有" if room.get("is_private") else "公开"
                self.rooms_table.setItem(i, 2, QtWidgets.QTableWidgetItem(room_type))
                
                self.rooms_table.setItem(i, 3, QtWidgets.QTableWidgetItem(str(room.get("member_count", 0))))
                self.rooms_table.setItem(i, 4, QtWidgets.QTableWidgetItem(str(room.get("max_members", 0))))
                
                user_role = room.get("user_role", "非成员")
                self.rooms_table.setItem(i, 5, QtWidgets.QTableWidgetItem(user_role))
                
                # 存储房间ID
                self.rooms_table.item(i, 0).setData(QtCore.Qt.ItemDataRole.UserRole, room.get("room_id"))
                
        except Exception as e:
            QtWidgets.QMessageBox.warning(self, "错误", f"刷新聊天室列表失败: {str(e)}")
    
    def join_room(self):
        """加入聊天室"""
        current_row = self.rooms_table.currentRow()
        if current_row < 0:
            QtWidgets.QMessageBox.information(self, "提示", "请选择要加入的聊天室")
            return
        
        room_name = self.rooms_table.item(current_row, 0).text()
        QtWidgets.QMessageBox.information(self, "提示", f"加入聊天室功能开发中: {room_name}")
    
    def leave_room(self):
        """退出聊天室"""
        current_row = self.rooms_table.currentRow()
        if current_row < 0:
            QtWidgets.QMessageBox.information(self, "提示", "请选择要退出的聊天室")
            return
        
        room_name = self.rooms_table.item(current_row, 0).text()
        QtWidgets.QMessageBox.information(self, "提示", f"退出聊天室功能开发中: {room_name}")
    
    def show_room_info(self):
        """显示聊天室详情"""
        current_row = self.rooms_table.currentRow()
        if current_row < 0:
            QtWidgets.QMessageBox.information(self, "提示", "请选择要查看的聊天室")
            return
        
        room_name = self.rooms_table.item(current_row, 0).text()
        QtWidgets.QMessageBox.information(self, "提示", f"聊天室详情功能开发中: {room_name}")

# 2025/06/26.11:50+分离ui操作 - 独立运行的主程序
def main():
    """主程序入口"""
    print("MySuite 员工操作界面程序启动中...")
    
    # 设置信号处理器
    import signal
    def signal_handler(signum, frame):
        print(f"收到信号 {signum}，正在安全退出...")
        try:
            QtWidgets.QApplication.quit()
        except:
            sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析启动参数
    token, employee_id, employee_name = parse_startup_args()
    print(f"启动参数: 员工ID={employee_id}, 员工姓名={employee_name}")
    
    # 创建应用程序（在token验证之前创建，以便显示错误对话框）
    app = QtWidgets.QApplication(sys.argv)
    app.setApplicationName("MySuite 员工操作界面")
    app.setApplicationVersion("1.0.0")
    
    # 验证token
    if not verify_token(token):
        print("Token验证失败，程序退出")
        QtWidgets.QMessageBox.critical(None, "验证失败", "登录状态已过期，请重新登录")
        sys.exit(1)
    
    print("Token验证成功")
    
    # 创建主窗口（独立运行模式）
    window = EmployeeInterfaceWindow(employee_id, employee_name, token)
    
    # 设置异步事件循环（独立运行需要）
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # 2025/06/26.11:50+分离ui操作 - 将事件循环引用传递给窗口
    window.app_loop = loop
    
    # 在后台线程中运行事件循环
    loop_thread = threading.Thread(target=loop.run_forever, daemon=True)
    loop_thread.start()
    
    # 设置HTTP客户端的事件循环
    window.async_http_client.set_event_loop(loop)
    
    # 显示窗口
    window.show()
    
    print(f"员工操作界面已启动 - {employee_name} ({employee_id})")
    
    # 运行应用程序
    try:
        result = app.exec()
        return result
    finally:
        # 清理
        print("程序清理中...")
        try:
            # 清理WebSocket连接
            if hasattr(window, 'chat_websocket') and window.chat_websocket:
                try:
                    asyncio.run_coroutine_threadsafe(window.chat_websocket.close(), loop)
                except Exception as e:
                    print(f"清理chat_websocket时出错: {e}")
            if hasattr(window, 'video_websocket') and window.video_websocket:
                try:
                    asyncio.run_coroutine_threadsafe(window.video_websocket.close(), loop)
                except Exception as e:
                    print(f"清理video_websocket时出错: {e}")
            
            # 清理HTTP客户端 - 使用正确的清理方法
            if hasattr(window, 'server5_client') and window.server5_client:
                window.server5_client.close()
            
            if hasattr(window, 'sync_http_client') and window.sync_http_client:
                window.sync_http_client.close()
            
            if hasattr(window, 'async_http_client') and window.async_http_client:
                try:
                    asyncio.run_coroutine_threadsafe(window.async_http_client.close(), loop)
                except Exception as e:
                    print(f"清理async_http_client时出错: {e}")
            
            if hasattr(window, 'server5_async_client') and window.server5_async_client:
                try:
                    asyncio.run_coroutine_threadsafe(window.server5_async_client.close(), loop)
                except Exception as e:
                    print(f"清理server5_async_client时出错: {e}")
            
            # 停止事件循环
            loop.call_soon_threadsafe(loop.stop)
            
            # 等待事件循环线程结束
            if loop_thread.is_alive():
                loop_thread.join(timeout=2)
                
        except Exception as e:
            print(f"清理过程中出现错误: {e}")
        
        print("程序清理完成")

if __name__ == "__main__":
    sys.exit(main()) 