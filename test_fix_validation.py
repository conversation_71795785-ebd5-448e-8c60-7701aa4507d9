#!/usr/bin/env python3
"""
测试category和item字段修复是否有效
"""

def test_safe_int_conversion():
    """测试安全整数转换函数"""
    
    def _safe_int_conversion(value):
        """
        安全的整数转换函数，正确处理字符串"0"的情况
        """
        if value is None:
            return 0
        
        # 去除前后空格
        cleaned_value = value.strip()
        
        # 如果是空字符串，返回0
        if not cleaned_value:
            return 0
        
        try:
            # 尝试转换为整数
            return int(cleaned_value)
        except (ValueError, TypeError):
            # 转换失败时返回默认值0
            print(f"无法将值 '{value}' 转换为整数，使用默认值0")
            return 0
    
    print("🧪 测试安全整数转换函数:")
    print("=" * 50)
    
    test_cases = [
        ("0", "字符串0"),
        ("1", "字符串1"),
        ("", "空字符串"),
        ("   ", "只有空格"),
        (None, "None值"),
        ("abc", "无效字符串"),
        ("123", "正常数字字符串"),
        ("-5", "负数字符串")
    ]
    
    for value, description in test_cases:
        result = _safe_int_conversion(value)
        print(f"  {description:15} '{value}' -> {result}")
    
    print("\n✅ 关键测试：字符串'0'应该转换为整数0")
    assert _safe_int_conversion("0") == 0
    assert _safe_int_conversion("  0  ") == 0
    print("  ✅ 字符串'0'正确转换为整数0")

def test_old_vs_new_logic():
    """对比旧逻辑和新逻辑的差异"""
    
    def old_logic(value):
        """旧的转换逻辑"""
        return int(value) if value and value.strip() else 0
    
    def new_logic(value):
        """新的转换逻辑"""
        if value is None:
            return 0
        cleaned_value = value.strip()
        if not cleaned_value:
            return 0
        try:
            return int(cleaned_value)
        except (ValueError, TypeError):
            return 0
    
    print("\n🔄 对比旧逻辑和新逻辑:")
    print("=" * 50)
    
    test_cases = ["0", "1", "", "   ", None, "abc"]
    
    for value in test_cases:
        try:
            old_result = old_logic(value)
        except Exception as e:
            old_result = f"ERROR: {e}"
        
        new_result = new_logic(value)
        
        status = "✅" if old_result == new_result else "❌"
        print(f"  {status} 值: {repr(value):10} | 旧: {old_result:10} | 新: {new_result}")

def simulate_complete_flow():
    """模拟完整的数据流程"""
    
    print("\n🔄 模拟完整数据流程:")
    print("=" * 50)
    
    # 1. 客户端发送的数据
    client_data = {
        "category": "0",  # 用户在输入框中输入"0"
        "item": "0"       # 用户在输入框中输入"0"
    }
    
    print("1️⃣ 客户端发送:")
    for key, value in client_data.items():
        print(f"  {key}: {repr(value)}")
    
    # 2. 新的安全转换逻辑
    def _safe_int_conversion(value):
        if value is None:
            return 0
        cleaned_value = value.strip()
        if not cleaned_value:
            return 0
        try:
            return int(cleaned_value)
        except (ValueError, TypeError):
            return 0
    
    converted_data = {
        "category": _safe_int_conversion(client_data["category"]),
        "item": _safe_int_conversion(client_data["item"])
    }
    
    print("\n2️⃣ 微服务5转换后:")
    for key, value in converted_data.items():
        print(f"  {key}: {value} (类型: {type(value).__name__})")
    
    # 3. F2推送服务处理
    def f2_conversion(entry_data):
        return {
            'category': str(entry_data.get('category')) if entry_data.get('category') is not None else None,
            'item': str(entry_data.get('item')) if entry_data.get('item') is not None else None
        }
    
    f2_data = f2_conversion(converted_data)
    
    print("\n3️⃣ F2推送服务处理:")
    for key, value in f2_data.items():
        print(f"  {key}: {repr(value)}")
    
    # 验证结果
    if f2_data['category'] == '0' and f2_data['item'] == '0':
        print("  ✅ 修复成功！字符串'0'正确传递")
    else:
        print("  ❌ 修复失败！数据转换有问题")

if __name__ == "__main__":
    test_safe_int_conversion()
    test_old_vs_new_logic()
    simulate_complete_flow()
