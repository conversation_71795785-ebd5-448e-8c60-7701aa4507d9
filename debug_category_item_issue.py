#!/usr/bin/env python3
"""
调试category和item字段0转换为None的问题
分析插入和更新操作的数据流程差异
"""

def analyze_insert_flow():
    """分析插入操作的数据流程"""
    print("🔍 分析插入操作数据流程:")
    print("=" * 50)
    
    # 1. 客户端发送的数据
    print("1️⃣ 客户端发送数据:")
    client_data = {
        "entry_date": "2025-07-15",
        "employee_id": "215829",
        "duration": 1.0,
        "model": "",
        "number": "",
        "factory_number": "",
        "project_number": "",
        "unit_number": "",
        "category": "0",  # 客户端发送字符串"0"
        "item": "0",      # 客户端发送字符串"0"
        "department": "TEST",
        "source": "user"
    }
    
    for key, value in client_data.items():
        print(f"  {key}: {repr(value)} (类型: {type(value).__name__})")
    
    # 2. 微服务5的entries_gateway.py处理
    print("\n2️⃣ 微服务5 entries_gateway.py 处理:")
    print("  UIEntryCreate模型定义:")
    print("    category: Optional[int] = Field(None, description='区分')")
    print("    item: Optional[int] = Field(None, description='項目')")
    print("  ❌ 问题：Pydantic会尝试将字符串'0'转换为整数0")
    
    # 3. _convert_ui_to_entries_format函数处理
    print("\n3️⃣ _convert_ui_to_entries_format函数处理:")
    print("  逻辑：if ui_field in ui_data and ui_data[ui_field] is not None:")
    print("  ❌ 问题：当category=0或item=0时，条件为True，但0被认为是falsy值")
    
    # 模拟转换过程
    entries_data = {}
    ui_specific_mapping = {
        'category': 'category',
        'item': 'item'
    }
    
    # 模拟Pydantic转换后的数据
    pydantic_data = {
        'category': 0,  # 字符串"0"被转换为整数0
        'item': 0       # 字符串"0"被转换为整数0
    }
    
    print(f"  Pydantic转换后: category={pydantic_data['category']}, item={pydantic_data['item']}")
    
    for ui_field, entries_field in ui_specific_mapping.items():
        if ui_field in pydantic_data and pydantic_data[ui_field] is not None:
            entries_data[entries_field] = pydantic_data[ui_field]
            print(f"  ✅ {ui_field}: {pydantic_data[ui_field]} -> entries_data['{entries_field}']")
    
    print(f"  转换结果: {entries_data}")
    
    # 4. F2推送服务处理
    print("\n4️⃣ F2推送服务处理:")
    print("  逻辑：str(entry_data.get('category')) if entry_data.get('category') is not None else None")
    print("  ❌ 问题：当category=0时，entry_data.get('category')返回0")
    print("  ❌ 问题：0 is not None为True，所以执行str(0)='0'")
    print("  ❌ 但是在某些情况下，0可能被误判为falsy值")
    
    # 模拟F2处理
    entry_data = {'category': 0, 'item': 0}
    
    # 当前的逻辑
    category_result = str(entry_data.get('category')) if entry_data.get('category') is not None else None
    item_result = str(entry_data.get('item')) if entry_data.get('item') is not None else None
    
    print(f"  F2处理结果: category='{category_result}', item='{item_result}'")
    
    # 5. Server6客户端处理
    print("\n5️⃣ Server6客户端处理:")
    print("  逻辑：final_value = str(value) if value != '' and value is not None else '0'")
    print("  ✅ 这里应该正确处理'0'值")

def analyze_update_flow():
    """分析更新操作的数据流程"""
    print("\n🔍 分析更新操作数据流程:")
    print("=" * 50)
    
    # 1. 客户端发送的数据
    print("1️⃣ 客户端发送数据:")
    client_data = {
        "entry_date": "2025-07-15",
        "employee_id": "215829",
        "duration": 1.0,
        "category": "0",  # 客户端发送字符串"0"
        "item": "0",      # 客户端发送字符串"0"
        "department": "TEST",
        "source": "user"
    }
    
    for key, value in client_data.items():
        print(f"  {key}: {repr(value)} (类型: {type(value).__name__})")
    
    # 2. 微服务5的entries_api.py处理（更新使用不同的路由）
    print("\n2️⃣ 微服务5 entries_api.py 处理:")
    print("  更新操作使用PUT /api/entries/{entry_id}")
    print("  ❌ 可能使用不同的数据验证和转换逻辑")

def find_root_cause():
    """找出根本原因"""
    print("\n🎯 根本原因分析:")
    print("=" * 50)
    
    print("1️⃣ 插入和更新使用不同的API端点:")
    print("  插入: POST /client/entries/create (client_entries_gateway.py)")
    print("  更新: PUT /api/entries/{id} (entries_api.py)")
    
    print("\n2️⃣ 数据模型定义不同:")
    print("  插入: ClientEntryCreate - category: Optional[str]")
    print("  更新: UIEntryUpdate - category: Optional[int]")
    
    print("\n3️⃣ 关键差异:")
    print("  插入操作：category作为字符串处理，可能在某个环节被错误转换")
    print("  更新操作：category作为整数处理，转换逻辑更直接")
    
    print("\n4️⃣ 可能的问题点:")
    print("  ❌ client_entries_gateway.py中的数据处理逻辑")
    print("  ❌ F2推送服务中对0值的特殊处理")
    print("  ❌ Server6客户端的数据序列化逻辑")

def test_zero_conversion():
    """测试0值转换逻辑"""
    print("\n🧪 测试0值转换逻辑:")
    print("=" * 50)
    
    # 测试不同的0值表示
    test_values = [0, "0", "", None, "   ", "NULL"]
    
    for value in test_values:
        print(f"\n测试值: {repr(value)} (类型: {type(value).__name__})")
        
        # 模拟F2的转换逻辑
        result1 = str(value) if value is not None else None
        print(f"  str(value) if value is not None else None: {repr(result1)}")
        
        # 模拟Server6客户端的转换逻辑
        if value == "" or value is None:
            result2 = "0"
        else:
            result2 = str(value)
        print(f"  Server6转换: {repr(result2)}")
        
        # 检查是否为falsy值
        is_falsy = not bool(value)
        print(f"  是否为falsy值: {is_falsy}")

if __name__ == "__main__":
    analyze_insert_flow()
    analyze_update_flow()
    find_root_cause()
    test_zero_conversion()
